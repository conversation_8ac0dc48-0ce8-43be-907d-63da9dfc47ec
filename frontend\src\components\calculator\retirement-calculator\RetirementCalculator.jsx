"use client";
import { useState, useEffect } from "react";
import Highcharts from "highcharts";
import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";

const RetirementCalculator = () => {
  // Form state
  const [formData, setFormData] = useState({
    currentAge: 30,
    retireAge: 60,
    lifeExpectancy: 80,
    currentExpenses: 50000,
    inflationRate: 5,
    investmentReturn: 12,
    currentSavings: 100000,
    corpusReturn: 6,
  });

  // Results state
  const [results, setResults] = useState({
    futureExpense: 0,
    corpusAmount: 0,
    lumpsumAmount: 0,
    sipAmount: 0,
    yearsToInvest: 0,
    futureSavingsValue: 0,
    targetCorpus: 0,
    investedAmount: 0,
    totalEarnings: 0,
  });

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    let parsedValue = value.replace(/,/g, "");
    parsedValue = isNaN(parsedValue) ? 0 : Number.parseInt(parsedValue);

    setFormData((prev) => ({
      ...prev,
      [name]: parsedValue,
    }));
  };

  // Handle slider changes
  const handleSliderChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: Number.parseInt(value),
    }));
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat("en-IN").format(num);
  };

  // Calculate retirement values
  const calculateWealthy = () => {
    const {
      currentAge,
      retireAge,
      lifeExpectancy,
      currentExpenses,
      inflationRate,
      investmentReturn,
      currentSavings,
      corpusReturn,
    } = formData;

    // Validate inputs
    if (currentAge >= retireAge) {
      alert("Please enter retirement age greater than current age.");
      return;
    }
    if (retireAge >= lifeExpectancy) {
      alert("Please enter life expectancy greater than retirement age.");
      return;
    }

    const horizon = retireAge - currentAge;
    const yearsAfterRetirement = lifeExpectancy - retireAge;

    // Calculate future monthly expenses at retirement
    const exInflation = inflationRate / 100;
    const futureExpense = currentExpenses * Math.pow(1 + exInflation, horizon);

    // Calculate total corpus needed
    const retInvest1 = corpusReturn / 100;
    const retInvest2 = inflationRate / 100;
    const interestRate = ((1 + retInvest1) / (1 + retInvest2) - 1) * 0.083333;
    const numberOfMonths = yearsAfterRetirement * 12;

    let totalAmount;
    if (interestRate === 0) {
      totalAmount = futureExpense * numberOfMonths;
    } else {
      totalAmount =
        (futureExpense / interestRate) *
        (1 - Math.pow(1 + interestRate, -numberOfMonths));
    }
    totalAmount = Math.round(totalAmount);

    // Calculate future value of current savings
    const value3 = 1 + investmentReturn / 100;
    const value5 = Math.pow(value3, horizon);
    const futureSavingsValue = Math.round(currentSavings * value5);

    // Calculate target corpus needed
    const targetAmount = Math.round(totalAmount - futureSavingsValue);

    // Calculate monthly SIP needed
    const value3Monthly = 1 + investmentReturn / 100 / 12;
    const months = horizon * 12;

    let value4 = 0;
    for (let m = 1; m <= months; m++) {
      value4 += Math.pow(value3Monthly, m);
    }

    const monthlySavings = Math.round(targetAmount / value4);
    const sipAmount = Math.ceil(monthlySavings);

    // Calculate lumpsum investment needed
    const lumpsumAmount = Math.round(
      targetAmount * (1 / Math.pow(value3, horizon))
    );

    // Calculate invested amount and earnings for chart
    const investedAmount = months * sipAmount;
    const totalEarnings = targetAmount - investedAmount;

    setResults({
      futureExpense: Math.round(futureExpense),
      corpusAmount: Math.round(totalAmount),
      lumpsumAmount,
      sipAmount,
      yearsToInvest: horizon,
      futureSavingsValue: Math.round(futureSavingsValue),
      targetCorpus: Math.round(targetAmount),
      investedAmount,
      totalEarnings,
    });

    // Render chart
    renderChart(investedAmount, totalEarnings);
  };

  // Render pie chart
  const renderChart = (amount, totalInterest) => {
    if (
      typeof window !== "undefined" &&
      document.getElementById("emipiechart")
    ) {
      Highcharts.chart("emipiechart", {
        colors: ["#A91E22", "#1B1E49"],
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false,
        },
        title: {
          text: "Break-up of Total Payment",
          style: {
            font: "bold 12px Helvetica Neue, Helvetica, Arial, sans-serif",
          },
        },
        legend: {
          borderWidth: 1,
          borderRadius: 5,
          itemStyle: {
            font: "normal 12px Helvetica Neue, Helvetica, Arial, sans-serif",
          },
        },
        credits: {
          enabled: false,
        },
        tooltip: {
          pointFormat: "{point.percentage:.1f}%",
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: "pointer",
            dataLabels: {
              enabled: false,
              color: "#000000",
              connectorColor: "#000000",
              format: "<b>{point.name}</b>: {point.percentage:.1f} %",
            },
            showInLegend: true,
          },
        },
        series: [
          {
            type: "pie",
            data: [
              ["Amount Invested", amount],
              {
                name: "Total Growth",
                y: totalInterest,
                sliced: true,
                selected: true,
              },
            ],
          },
        ],
      });
    }
  };

  // Calculate on component mount and when form data changes
  useEffect(() => {
    // Add a small delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      calculateWealthy();
    }, 100);

    return () => clearTimeout(timer);
  }, [formData]);

  return (
    <section className="bg-[#fff]">
      <div className="s_wrapper">
        <div className="container mx-auto md:px-4 md:py-8 text-black">
          <h2 className="md:mb-6 text-2xl md:text-4xl lg:text-5xl font-medium text-[#040404]">
            <SkewFadeInWords text="Retirement Calculator" />
          </h2>
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Left column - Inputs */}
            <div className="w-full lg:w-7/12 bg-white rounded-lg shadow-md p-4">
              {/* Current Age */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">What is your current age?</label>
                  <input
                    type="text"
                    name="currentAge"
                    value={formatNumber(formData.currentAge)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1"
                  max="99"
                  value={formData.currentAge}
                  onChange={(e) =>
                    handleSliderChange("currentAge", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Retirement Age */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    At what age you want to retire?
                  </label>
                  <input
                    type="text"
                    name="retireAge"
                    value={formatNumber(formData.retireAge)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1"
                  max="99"
                  value={formData.retireAge}
                  onChange={(e) =>
                    handleSliderChange("retireAge", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Life Expectancy */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">Expected life expectancy?</label>
                  <input
                    type="text"
                    name="lifeExpectancy"
                    value={formatNumber(formData.lifeExpectancy)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1"
                  max="99"
                  value={formData.lifeExpectancy}
                  onChange={(e) =>
                    handleSliderChange("lifeExpectancy", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Current Monthly Expenses */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    Your current monthly household expenses?
                  </label>
                  <input
                    type="text"
                    name="currentExpenses"
                    value={formatNumber(formData.currentExpenses)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1000"
                  max="1000000"
                  step="1000"
                  value={formData.currentExpenses}
                  onChange={(e) =>
                    handleSliderChange("currentExpenses", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Inflation Rate */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    Expected inflation rate over the years (% per annum)
                  </label>
                  <input
                    type="text"
                    name="inflationRate"
                    value={formatNumber(formData.inflationRate)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="0"
                  max="10"
                  value={formData.inflationRate}
                  onChange={(e) =>
                    handleSliderChange("inflationRate", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Investment Return */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    The expected rate of return on your investments (% per
                    annum)
                  </label>
                  <input
                    type="text"
                    name="investmentReturn"
                    value={formatNumber(formData.investmentReturn)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1"
                  max="30"
                  value={formData.investmentReturn}
                  onChange={(e) =>
                    handleSliderChange("investmentReturn", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Current Savings */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    How much investments you have now (Rs)
                  </label>
                  <input
                    type="text"
                    name="currentSavings"
                    value={formatNumber(formData.currentSavings)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="0"
                  max="10000000"
                  step="1000"
                  value={formData.currentSavings}
                  onChange={(e) =>
                    handleSliderChange("currentSavings", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
              <div className="border-b border-gray-300 my-4"></div>

              {/* Corpus Return */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="font-seminbold text-sm">
                    What return you expect on your retirement corpus (% per
                    annum)
                  </label>
                  <input
                    type="text"
                    name="corpusReturn"
                    value={formatNumber(formData.corpusReturn)}
                    onChange={handleInputChange}
                    className="w-24 text-center border border-gray-300 rounded px-2 py-1 font-semibold text-[#333] text-sm"
                  />
                </div>
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={formData.corpusReturn}
                  onChange={(e) =>
                    handleSliderChange("corpusReturn", e.target.value)
                  }
                  className="w-full accent-[#A91E22]"
                />
              </div>
            </div>

            {/* Right column - Results */}
            <div className="w-full lg:w-5/12">
              {/* Pie Chart */}
              <div
                id="emipiechart"
                className="bg-white rounded-lg shadow-md p-4 mb-4 h-80"
              ></div>

              {/* Results Summary */}
              <div className="bg-white rounded-lg shadow-md p-4">
                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Your current monthly household expenses
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(formData.currentExpenses)}
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Your future monthly household expenses
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(results.futureExpense)}
                  </p>
                  <p className="text-xs text-gray-600">
                    (Your monthly expenses will increase annually by{" "}
                    {formData.inflationRate}%)
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Your current investment amount
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(formData.currentSavings)}
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Future value of your current investments
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(results.futureSavingsValue)}
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Future corpus amount needed to meet expenses
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(results.corpusAmount)} - ₹{" "}
                    {formatNumber(results.futureSavingsValue)} (Future value of
                    your current investments) = ₹{" "}
                    {formatNumber(results.targetCorpus)}
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Number of years you need to invest
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    {results.yearsToInvest} Years
                  </p>
                </div>

                <div className="border-b border-gray-300 py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    Lumpsum amount you need to invest
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(results.lumpsumAmount)}
                  </p>
                </div>

                <div className="py-3">
                  <h4 className="text-gray-600 text-sm mb-1">
                    SIP amount you need to invest
                  </h4>
                  <p className="text-[#A91E22] font-semibold text-sm">
                    ₹ {formatNumber(results.sipAmount)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RetirementCalculator;
