/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-gray-950: oklch(13% .028 261.692);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --drop-shadow-sm: 0 1px 2px #00000026;
    --drop-shadow-2xl: 0 25px 25px #00000026;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .\!absolute {
    position: absolute !important;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .-top-8 {
    top: calc(var(--spacing) * -8);
  }

  .-top-\[40px\] {
    top: -40px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-16 {
    top: calc(var(--spacing) * 16);
  }

  .top-18 {
    top: calc(var(--spacing) * 18);
  }

  .top-\[20\%\] {
    top: 20%;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[85px\] {
    top: 85px;
  }

  .top-\[110px\] {
    top: 110px;
  }

  .top-full {
    top: 100%;
  }

  .-right-3 {
    right: calc(var(--spacing) * -3);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-8 {
    right: calc(var(--spacing) * 8);
  }

  .-bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }

  .bottom-3 {
    bottom: calc(var(--spacing) * 3);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }

  .bottom-10 {
    bottom: calc(var(--spacing) * 10);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-\[5\%\] {
    left: 5%;
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .-z-1 {
    z-index: calc(1 * -1);
  }

  .-z-9 {
    z-index: calc(9 * -1);
  }

  .-z-10 {
    z-index: calc(10 * -1);
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[9\] {
    z-index: 9;
  }

  .z-\[10\] {
    z-index: 10;
  }

  .z-\[20\] {
    z-index: 20;
  }

  .z-\[21\] {
    z-index: 21;
  }

  .z-\[22\] {
    z-index: 22;
  }

  .z-\[25\] {
    z-index: 25;
  }

  .z-\[99\] {
    z-index: 99;
  }

  .z-\[999\] {
    z-index: 999;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-3 {
    grid-column: span 3 / span 3;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .\!mx-auto {
    margin-inline: auto !important;
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-8 {
    margin-inline: calc(var(--spacing) * 8);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .\!my-2 {
    margin-block: calc(var(--spacing) * 2) !important;
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-10 {
    margin-block: calc(var(--spacing) * 10);
  }

  .my-auto {
    margin-block: auto;
  }

  .\!mt-4 {
    margin-top: calc(var(--spacing) * 4) !important;
  }

  .\!mt-8 {
    margin-top: calc(var(--spacing) * 8) !important;
  }

  .-mt-\[1px\] {
    margin-top: -1px;
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .\!mb-0 {
    margin-bottom: calc(var(--spacing) * 0) !important;
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-\[50px\] {
    margin-bottom: 50px;
  }

  .mb-\[60px\] {
    margin-bottom: 60px;
  }

  .\!ml-0 {
    margin-left: calc(var(--spacing) * 0) !important;
  }

  .\!ml-2 {
    margin-left: calc(var(--spacing) * 2) !important;
  }

  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }

  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }

  .ml-\[-70vw\] {
    margin-left: -70vw;
  }

  .ml-\[-100vw\] {
    margin-left: -100vw;
  }

  .ml-auto {
    margin-left: auto;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-4 {
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-44 {
    height: calc(var(--spacing) * 44);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-52 {
    height: calc(var(--spacing) * 52);
  }

  .h-56 {
    height: calc(var(--spacing) * 56);
  }

  .h-60 {
    height: calc(var(--spacing) * 60);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-\[0\.5px\] {
    height: .5px;
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[20px\] {
    height: 20px;
  }

  .h-\[20vh\] {
    height: 20vh;
  }

  .h-\[30vh\] {
    height: 30vh;
  }

  .h-\[40vh\] {
    height: 40vh;
  }

  .h-\[50vh\] {
    height: 50vh;
  }

  .h-\[60px\] {
    height: 60px;
  }

  .h-\[100px\] {
    height: 100px;
  }

  .h-\[180px\] {
    height: 180px;
  }

  .h-\[215px\] {
    height: 215px;
  }

  .h-\[220px\] {
    height: 220px;
  }

  .h-\[245px\] {
    height: 245px;
  }

  .h-\[280px\] {
    height: 280px;
  }

  .h-\[350px\] {
    height: 350px;
  }

  .h-\[auto\] {
    height: auto;
  }

  .h-\[calc\(100\%-40px\)\] {
    height: calc(100% - 40px);
  }

  .h-\[calc\(100dvh-90px\)\] {
    height: calc(100dvh - 90px);
  }

  .h-\[calc\(100dvh-120px\)\] {
    height: calc(100dvh - 120px);
  }

  .h-\[calc\(100vh-100px\)\] {
    height: calc(100vh - 100px);
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-max {
    height: max-content;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-52 {
    max-height: calc(var(--spacing) * 52);
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[85vh\] {
    max-height: 85vh;
  }

  .max-h-\[90\%\] {
    max-height: 90%;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[260px\] {
    max-height: 260px;
  }

  .max-h-\[280px\] {
    max-height: 280px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-fit {
    max-height: fit-content;
  }

  .max-h-full {
    max-height: 100%;
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }

  .min-h-20 {
    min-height: calc(var(--spacing) * 20);
  }

  .min-h-32 {
    min-height: calc(var(--spacing) * 32);
  }

  .min-h-\[60px\] {
    min-height: 60px;
  }

  .min-h-\[450px\] {
    min-height: 450px;
  }

  .\!w-\[100\%\] {
    width: 100% !important;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-22 {
    width: calc(var(--spacing) * 22);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-90 {
    width: calc(var(--spacing) * 90);
  }

  .w-\[0\.5px\] {
    width: .5px;
  }

  .w-\[46\%\] {
    width: 46%;
  }

  .w-\[60px\] {
    width: 60px;
  }

  .w-\[90\%\] {
    width: 90%;
  }

  .w-\[97\%\] {
    width: 97%;
  }

  .w-\[100\%\] {
    width: 100%;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[140px\] {
    width: 140px;
  }

  .w-\[240vw\] {
    width: 240vw;
  }

  .w-\[245px\] {
    width: 245px;
  }

  .w-\[calc\(100\%-40px\)\] {
    width: calc(100% - 40px);
  }

  .w-\[calc\(100vw-10px\)\] {
    width: calc(100vw - 10px);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-screen {
    width: 100vw;
  }

  .\!max-w-4xl {
    max-width: var(--container-4xl) !important;
  }

  .\!max-w-6xl {
    max-width: var(--container-6xl) !important;
  }

  .\!max-w-fit {
    max-width: fit-content !important;
  }

  .max-w-2 {
    max-width: calc(var(--spacing) * 2);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-8 {
    max-width: calc(var(--spacing) * 8);
  }

  .max-w-10 {
    max-width: calc(var(--spacing) * 10);
  }

  .max-w-12 {
    max-width: calc(var(--spacing) * 12);
  }

  .max-w-16 {
    max-width: calc(var(--spacing) * 16);
  }

  .max-w-20 {
    max-width: calc(var(--spacing) * 20);
  }

  .max-w-22 {
    max-width: calc(var(--spacing) * 22);
  }

  .max-w-24 {
    max-width: calc(var(--spacing) * 24);
  }

  .max-w-28 {
    max-width: calc(var(--spacing) * 28);
  }

  .max-w-\[60\%\] {
    max-width: 60%;
  }

  .max-w-\[80\%\] {
    max-width: 80%;
  }

  .max-w-\[90\%\] {
    max-width: 90%;
  }

  .max-w-\[98\%\] {
    max-width: 98%;
  }

  .max-w-\[100px\] {
    max-width: 100px;
  }

  .max-w-\[260px\] {
    max-width: 260px;
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-\[520px\] {
    max-width: 520px;
  }

  .max-w-\[700px\] {
    max-width: 700px;
  }

  .max-w-\[786px\] {
    max-width: 786px;
  }

  .max-w-\[992px\] {
    max-width: 992px;
  }

  .max-w-\[1400px\] {
    max-width: 1400px;
  }

  .max-w-fit {
    max-width: fit-content;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-screen {
    max-width: 100vw;
  }

  .min-w-4 {
    min-width: calc(var(--spacing) * 4);
  }

  .min-w-20 {
    min-width: calc(var(--spacing) * 20);
  }

  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }

  .min-w-\[60px\] {
    min-width: 60px;
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .min-w-\[220px\] {
    min-width: 220px;
  }

  .min-w-\[260px\] {
    min-width: 260px;
  }

  .min-w-fit {
    min-width: fit-content;
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .basis-\[50\%\] {
    flex-basis: 50%;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .translate-\[-50\%\] {
    --tw-translate-x: -50%;
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-700px\] {
    --tw-translate-y: -700px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-125 {
    --tw-scale-x: 125%;
    --tw-scale-y: 125%;
    --tw-scale-z: 125%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .-rotate-45 {
    rotate: -45deg;
  }

  .-rotate-90 {
    rotate: -90deg;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-disc {
    list-style-type: disc;
  }

  .appearance-none {
    appearance: none;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-\[4\%\] {
    gap: 4%;
  }

  .gap-\[20px\] {
    gap: 20px;
  }

  .gap-\[30px\] {
    gap: 30px;
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }

  .gap-x-10 {
    column-gap: calc(var(--spacing) * 10);
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .justify-self-center {
    justify-self: center;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .overflow-y-hidden {
    overflow-y: hidden;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }

  .rounded-\[16px\] {
    border-radius: 16px;
  }

  .rounded-\[20px\] {
    border-radius: 20px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .rounded-tl-\[50px\] {
    border-top-left-radius: 50px;
  }

  .rounded-r-4xl {
    border-top-right-radius: var(--radius-4xl);
    border-bottom-right-radius: var(--radius-4xl);
  }

  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .rounded-tr-\[50px\] {
    border-top-right-radius: 50px;
  }

  .rounded-tr-\[100px\] {
    border-top-right-radius: 100px;
  }

  .rounded-br-\[20px\] {
    border-bottom-right-radius: 20px;
  }

  .rounded-bl-\[20px\] {
    border-bottom-left-radius: 20px;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-\[\#101435\] {
    border-color: #101435;
  }

  .border-\[\#b33c337c\] {
    border-color: #b33c337c;
  }

  .border-\[\#ffffff\]\/20 {
    border-color: oklab(100% 0 5.96046e-8 / .2);
  }

  .border-blue-400 {
    border-color: var(--color-blue-400);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-400 {
    border-color: var(--color-gray-400);
  }

  .border-red-400 {
    border-color: var(--color-red-400);
  }

  .border-red-700 {
    border-color: var(--color-red-700);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-b-gray-300 {
    border-bottom-color: var(--color-gray-300);
  }

  .\!bg-white {
    background-color: var(--color-white) !important;
  }

  .bg-\[\#000\]\/20 {
    background-color: oklab(0% none none / .2);
  }

  .bg-\[\#000\]\/40 {
    background-color: oklab(0% none none / .4);
  }

  .bg-\[\#1b1e49\] {
    background-color: #1b1e49;
  }

  .bg-\[\#1e2a5a\] {
    background-color: #1e2a5a;
  }

  .bg-\[\#00000050\] {
    background-color: #00000050;
  }

  .bg-\[\#0058a0\] {
    background-color: #0058a0;
  }

  .bg-\[\#0077b5\] {
    background-color: #0077b5;
  }

  .bg-\[\#689f39\] {
    background-color: #689f39;
  }

  .bg-\[\#024939\] {
    background-color: #024939;
  }

  .bg-\[\#034939\] {
    background-color: #034939;
  }

  .bg-\[\#101435\] {
    background-color: #101435;
  }

  .bg-\[\#101435ee\] {
    background-color: #101435ee;
  }

  .bg-\[\#F9F3F1\] {
    background-color: #f9f3f1;
  }

  .bg-\[\#a91e22\] {
    background-color: #a91e22;
  }

  .bg-\[\#ac272b\] {
    background-color: #ac272b;
  }

  .bg-\[\#ae2f33\] {
    background-color: #ae2f33;
  }

  .bg-\[\#b22222\] {
    background-color: #b22222;
  }

  .bg-\[\#bf360c\] {
    background-color: #bf360c;
  }

  .bg-\[\#cac9ff\] {
    background-color: #cac9ff;
  }

  .bg-\[\#cfd0d750\] {
    background-color: #cfd0d750;
  }

  .bg-\[\#e70b00\] {
    background-color: #e70b00;
  }

  .bg-\[\#e73900\] {
    background-color: #e73900;
  }

  .bg-\[\#e7380315\] {
    background-color: #e7380315;
  }

  .bg-\[\#f0f0f2\] {
    background-color: #f0f0f2;
  }

  .bg-\[\#f0f0fa\] {
    background-color: #f0f0fa;
  }

  .bg-\[\#f7f0ec\] {
    background-color: #f7f0ec;
  }

  .bg-\[\#f9f3f1\] {
    background-color: #f9f3f1;
  }

  .bg-\[\#f5511e\] {
    background-color: #f5511e;
  }

  .bg-\[\#f47321\] {
    background-color: #f47321;
  }

  .bg-\[\#ff8f873a\] {
    background-color: #ff8f873a;
  }

  .bg-\[\#ff917065\] {
    background-color: #ff917065;
  }

  .bg-\[\#fff\] {
    background-color: #fff;
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/20 {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/20 {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .bg-black\/30 {
    background-color: #0000004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/30 {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .bg-black\/40 {
    background-color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/40 {
      background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .bg-black\/80 {
    background-color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/80 {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-700 {
    background-color: var(--color-red-700);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-white\/30 {
    background-color: #ffffff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/30 {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .bg-white\/40 {
    background-color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/40 {
      background-color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .bg-white\/50 {
    background-color: #ffffff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/50 {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .bg-white\/80 {
    background-color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/80 {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[url\(\'\/images\/home\/InsureYourself\.webp\'\)\] {
    background-image: url("/images/home/<USER>");
  }

  .bg-\[url\(\'\/images\/home\/investToday\.jpg\'\)\] {
    background-image: url("/images/home/<USER>");
  }

  .from-\[\#e70b00\] {
    --tw-gradient-from: #e70b00;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#e73900\]\/50 {
    --tw-gradient-from: oklab(60.8656% .178026 .122432 / .5);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-black\/60 {
    --tw-gradient-from: #0009;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .from-black\/90 {
    --tw-gradient-from: #000000e6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/90 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .via-black\/30 {
    --tw-gradient-via: #0000004d;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-black\/30 {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .via-black\/50 {
    --tw-gradient-via: #00000080;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-black\/50 {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .to-\[\#e70b00\] {
    --tw-gradient-to: #e70b00;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#e73900\]\/50 {
    --tw-gradient-to: oklab(60.8656% .178026 .122432 / .5);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-black\/10 {
    --tw-gradient-to: #0000001a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .\!bg-cover {
    background-size: cover !important;
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-clip-text {
    background-clip: text;
  }

  .\!bg-center {
    background-position: center !important;
  }

  .bg-center {
    background-position: center;
  }

  .bg-left {
    background-position: 0;
  }

  .fill-gray-200 {
    fill: var(--color-gray-200);
  }

  .fill-gray-300 {
    fill: var(--color-gray-300);
  }

  .fill-white {
    fill: var(--color-white);
  }

  .fill-yellow-400 {
    fill: var(--color-yellow-400);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .\!py-3 {
    padding-block: calc(var(--spacing) * 3) !important;
  }

  .\!py-4 {
    padding-block: calc(var(--spacing) * 4) !important;
  }

  .\!py-5 {
    padding-block: calc(var(--spacing) * 5) !important;
  }

  .\!py-7 {
    padding-block: calc(var(--spacing) * 7) !important;
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-\[11px\] {
    padding-block: 11px;
  }

  .\!pt-0 {
    padding-top: calc(var(--spacing) * 0) !important;
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-\[60px\] {
    padding-top: 60px;
  }

  .pt-\[80px\] {
    padding-top: 80px;
  }

  .pt-\[100px\] {
    padding-top: 100px;
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .\!pb-0 {
    padding-bottom: calc(var(--spacing) * 0) !important;
  }

  .\!pb-5 {
    padding-bottom: calc(var(--spacing) * 5) !important;
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }

  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }

  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }

  .pb-24 {
    padding-bottom: calc(var(--spacing) * 24);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .text-center {
    text-align: center;
  }

  .text-justify {
    text-align: justify;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-start {
    text-align: start;
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .font-serif {
    font-family: var(--font-serif);
  }

  .\!text-2xl {
    font-size: var(--text-2xl) !important;
    line-height: var(--tw-leading, var(--text-2xl--line-height)) !important;
  }

  .\!text-lg {
    font-size: var(--text-lg) !important;
    line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  }

  .\!text-xl {
    font-size: var(--text-xl) !important;
    line-height: var(--tw-leading, var(--text-xl--line-height)) !important;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-8xl {
    font-size: var(--text-8xl);
    line-height: var(--tw-leading, var(--text-8xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[12px\] {
    font-size: 12px;
  }

  .text-\[16px\] {
    font-size: 16px;
  }

  .text-\[18px\] {
    font-size: 18px;
  }

  .text-\[20px\] {
    font-size: 20px;
  }

  .text-\[22\.4px\] {
    font-size: 22.4px;
  }

  .text-\[36px\] {
    font-size: 36px;
  }

  .leading-\[1\.5\] {
    --tw-leading: 1.5;
    line-height: 1.5;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .\!font-bold {
    --tw-font-weight: var(--font-weight-bold) !important;
    font-weight: var(--font-weight-bold) !important;
  }

  .font-\[500\] {
    --tw-font-weight: 500;
    font-weight: 500;
  }

  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .\!text-\[\#000\] {
    color: #000 !important;
  }

  .\!text-\[\#333\] {
    color: #333 !important;
  }

  .\!text-blue-500 {
    color: var(--color-blue-500) !important;
  }

  .text-\[\#000\] {
    color: #000;
  }

  .text-\[\#2e4765\] {
    color: #2e4765;
  }

  .text-\[\#222\] {
    color: #222;
  }

  .text-\[\#333\] {
    color: #333;
  }

  .text-\[\#931F1D\], .text-\[\#931f1d\] {
    color: #931f1d;
  }

  .text-\[\#040404\] {
    color: #040404;
  }

  .text-\[\#101435\] {
    color: #101435;
  }

  .text-\[\#101435c2\] {
    color: #101435c2;
  }

  .text-\[\#808080\] {
    color: gray;
  }

  .text-\[\#A91E22\], .text-\[\#a91e22\] {
    color: #a91e22;
  }

  .text-\[\#ac2629\] {
    color: #ac2629;
  }

  .text-\[\#ae2c2f\] {
    color: #ae2c2f;
  }

  .text-\[\#ae2f33\] {
    color: #ae2f33;
  }

  .text-\[\#d3b703\] {
    color: #d3b703;
  }

  .text-\[\#fc6634\] {
    color: #fc6634;
  }

  .text-\[\#fc663485\] {
    color: #fc663485;
  }

  .text-\[\#ffffff\] {
    color: #fff;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-50 {
    color: var(--color-gray-50);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-gray-950 {
    color: var(--color-gray-950);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-transparent {
    color: #0000;
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/70 {
    color: #ffffffb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/70 {
      color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .\!underline {
    text-decoration-line: underline !important;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .accent-\[\#A91E22\] {
    accent-color: #a91e22;
  }

  .accent-\[\#\] {
    accent-color: # ;
  }

  .accent-\[\#ac272b\] {
    accent-color: #ac272b;
  }

  .accent-\[\#accent-\[\#ac272b\]\] {
    accent-color: #accent-[#ac272b];
  }

  .accent-\[\#f47321\] {
    accent-color: #f47321;
  }

  .accent-\[\@\] {
    accent-color: @ ;
  }

  .accent-red-600 {
    accent-color: var(--color-red-600);
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-5 {
    opacity: .05;
  }

  .opacity-10 {
    opacity: .1;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-gray-300 {
    --tw-shadow-color: oklch(87.2% .01 258.338);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-gray-300 {
      --tw-shadow-color: color-mix(in oklab, var(--color-gray-300) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-gray-400 {
    --tw-shadow-color: oklch(70.7% .022 261.325);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-gray-400 {
      --tw-shadow-color: color-mix(in oklab, var(--color-gray-400) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-offset-white {
    --tw-ring-offset-color: var(--color-white);
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .drop-shadow-2xl {
    --tw-drop-shadow-size: drop-shadow(0 25px 25px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-2xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-sm {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-sm));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-400 {
    --tw-duration: .4s;
    transition-duration: .4s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-700 {
    --tw-duration: .7s;
    transition-duration: .7s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  @media (hover: hover) {
    .group-hover\:translate-y-0:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-\[\#b12f35\]:is(:where(.group):hover *) {
      color: #b12f35;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-90:is(:where(.group):hover *) {
      opacity: .9;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .placeholder\:text-gray-500::placeholder {
    color: var(--color-gray-500);
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:top-0:before {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .before\:left-0:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .before\:z-10:before {
    content: var(--tw-content);
    z-index: 10;
  }

  .before\:h-full:before {
    content: var(--tw-content);
    height: 100%;
  }

  .before\:w-full:before {
    content: var(--tw-content);
    width: 100%;
  }

  .before\:rotate-180:before {
    content: var(--tw-content);
    rotate: 180deg;
  }

  .before\:bg-gradient-to-l:before {
    content: var(--tw-content);
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .before\:bg-gradient-to-r:before {
    content: var(--tw-content);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .before\:from-\[\#6b1609db\]:before {
    content: var(--tw-content);
    --tw-gradient-from: #6b1609db;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:to-transparent:before {
    content: var(--tw-content);
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:content-\[\'\'\]:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:bottom-0:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 0);
  }

  .after\:left-0:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .after\:h-\[1px\]:after {
    content: var(--tw-content);
    height: 1px;
  }

  .after\:w-0:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 0);
  }

  .after\:bg-\[\#ae2c2f\]:after {
    content: var(--tw-content);
    background-color: #ae2c2f;
  }

  .after\:transition-all:after {
    content: var(--tw-content);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .after\:duration-300:after {
    content: var(--tw-content);
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  @media (hover: hover) {
    .hover\:cursor-pointer:hover {
      cursor: pointer;
    }
  }

  @media (hover: hover) {
    .hover\:border-\[\#b12f35\]:hover {
      border-color: #b12f35;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#0f6bca\]:hover {
      background-color: #0f6bca;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#101435\]:hover {
      background-color: #101435;
    }
  }

  @media (hover: hover) {
    .hover\:bg-black\/50:hover {
      background-color: #00000080;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/50:hover {
        background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-100:hover {
      background-color: var(--color-blue-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100\/80:hover {
      background-color: #f3f4f6cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-gray-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-900\/90:hover {
      background-color: #101828e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-900\/90:hover {
        background-color: color-mix(in oklab, var(--color-gray-900) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-500\/90:hover {
      background-color: #fb2c36e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-red-500\/90:hover {
        background-color: color-mix(in oklab, var(--color-red-500) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/30:hover {
      background-color: #ffffff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/30:hover {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/70:hover {
      background-color: #ffffffb3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/70:hover {
        background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-\[\#f8f8f86e\]:hover {
      color: #f8f8f86e;
    }
  }

  @media (hover: hover) {
    .hover\:text-black:hover {
      color: var(--color-black);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-200:hover {
      color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-300:hover {
      color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:after\:w-full:hover:after {
      content: var(--tw-content);
      width: 100%;
    }
  }

  .focus\:border-blue-400:focus {
    border-color: var(--color-blue-400);
  }

  .focus\:ring:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-300:focus {
    --tw-ring-color: var(--color-blue-300);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-gray-950:focus-visible {
    --tw-ring-color: var(--color-gray-950);
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:scale-95:active {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .data-\[state\=active\]\:bg-white[data-state="active"] {
    background-color: var(--color-white);
  }

  .data-\[state\=active\]\:text-gray-950[data-state="active"] {
    color: var(--color-gray-950);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (width >= 40rem) {
    .sm\:-top-12 {
      top: calc(var(--spacing) * -12);
    }
  }

  @media (width >= 40rem) {
    .sm\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 40rem) {
    .sm\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-6 {
      margin-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:ml-0 {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:h-8 {
      height: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-48 {
      height: calc(var(--spacing) * 48);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-56 {
      height: calc(var(--spacing) * 56);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-64 {
      height: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-72 {
      height: calc(var(--spacing) * 72);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-h-64 {
      max-height: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-h-\[70vh\] {
      max-height: 70vh;
    }
  }

  @media (width >= 40rem) {
    .sm\:min-h-8 {
      min-height: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:min-h-\[500px\] {
      min-height: 500px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-8 {
      width: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-2xl {
      max-width: var(--container-2xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:min-w-8 {
      min-width: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-center {
      justify-content: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-3 {
      gap: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-y-10 {
      row-gap: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-4 {
      padding: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-start {
      text-align: start;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:top-0 {
      top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:right-full {
      right: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:left-full {
      left: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:mx-4 {
      margin-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:mx-8 {
      margin-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:my-auto {
      margin-block: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:\!mt-0 {
      margin-top: calc(var(--spacing) * 0) !important;
    }
  }

  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-5 {
      margin-top: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-12 {
      margin-top: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-20 {
      margin-top: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-\[80px\] {
      margin-bottom: 80px;
    }
  }

  @media (width >= 48rem) {
    .md\:\!ml-0 {
      margin-left: calc(var(--spacing) * 0) !important;
    }
  }

  @media (width >= 48rem) {
    .md\:\!ml-\[-120vw\] {
      margin-left: -120vw !important;
    }
  }

  @media (width >= 48rem) {
    .md\:ml-0 {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:ml-\[-25vw\] {
      margin-left: -25vw;
    }
  }

  @media (width >= 48rem) {
    .md\:ml-auto {
      margin-left: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:grid {
      display: grid;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:h-40 {
      height: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:h-72 {
      height: calc(var(--spacing) * 72);
    }
  }

  @media (width >= 48rem) {
    .md\:h-80 {
      height: calc(var(--spacing) * 80);
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[40vh\] {
      height: 40vh;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[50vh\] {
      height: 50vh;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[90px\] {
      height: 90px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[100px\] {
      height: 100px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[260px\] {
      height: 260px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[340px\] {
      height: 340px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[400px\] {
      height: 400px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-full {
      height: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-h-96 {
      max-height: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 48rem) {
    .md\:max-h-\[300px\] {
      max-height: 300px;
    }
  }

  @media (width >= 48rem) {
    .md\:min-h-24 {
      min-height: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 48rem) {
    .md\:min-h-40 {
      min-height: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:min-h-\[90px\] {
      min-height: 90px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-3\/4 {
      width: 75%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-16 {
      width: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:w-40 {
      width: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:w-48 {
      width: calc(var(--spacing) * 48);
    }
  }

  @media (width >= 48rem) {
    .md\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[30\%\] {
      width: 30%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[40\%\] {
      width: 40%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[46\%\] {
      width: 46%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[50\%\] {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[60\%\] {
      width: 60%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[80\%\] {
      width: 80%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[90px\] {
      width: 90px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[100\%\] {
      width: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[100px\] {
      width: 100px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[200vw\] {
      width: 200vw;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[300px\] {
      width: 300px;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[46\%\] {
      max-width: 46%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[70\%\] {
      max-width: 70%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[90\%\] {
      max-width: 90%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[400px\] {
      max-width: 400px;
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-28 {
      min-width: calc(var(--spacing) * 28);
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-40 {
      min-width: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-\[30\%\] {
      min-width: 30%;
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-\[90px\] {
      min-width: 90px;
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-\[300px\] {
      min-width: 300px;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-1 {
      flex: 1;
    }
  }

  @media (width >= 48rem) {
    .md\:basis-\[50\%\] {
      flex-basis: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (width >= 48rem) {
    .md\:items-center {
      align-items: center;
    }
  }

  @media (width >= 48rem) {
    .md\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:border-t {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
    }
  }

  @media (width >= 48rem) {
    .md\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }

  @media (width >= 48rem) {
    .md\:border-gray-300 {
      border-color: var(--color-gray-300);
    }
  }

  @media (width >= 48rem) {
    .md\:bg-center {
      background-position: center;
    }
  }

  @media (width >= 48rem) {
    .md\:p-0 {
      padding: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:p-4 {
      padding: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:px-10 {
      padding-inline: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:px-12 {
      padding-inline: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:py-5 {
      padding-block: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:py-10 {
      padding-block: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:py-20 {
      padding-block: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-0 {
      padding-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-4 {
      padding-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-\[100px\] {
      padding-top: 100px;
    }
  }

  @media (width >= 48rem) {
    .md\:pr-0 {
      padding-right: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-0 {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-10 {
      padding-bottom: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-16 {
      padding-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-40 {
      padding-bottom: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:pl-10 {
      padding-left: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:text-center {
      text-align: center;
    }
  }

  @media (width >= 48rem) {
    .md\:text-start {
      text-align: start;
    }
  }

  @media (width >= 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[25\.6px\] {
      font-size: 25.6px;
    }
  }

  @media (width >= 48rem) {
    .md\:text-white {
      color: var(--color-white);
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-40 {
      opacity: .4;
    }
  }

  @media (width >= 48rem) {
    .md\:before\:rotate-0:before {
      content: var(--tw-content);
      rotate: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:\!static {
      position: static !important;
    }
  }

  @media (width >= 64rem) {
    .lg\:-right-40 {
      right: calc(var(--spacing) * -40);
    }
  }

  @media (width >= 64rem) {
    .lg\:left-\[50\%\] {
      left: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:z-10 {
      z-index: 10;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-4 {
      grid-column: span 4 / span 4;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-8 {
      grid-column: span 8 / span 8;
    }
  }

  @media (width >= 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:mx-auto {
      margin-inline: auto;
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-4 {
      margin-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-6 {
      margin-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-16 {
      margin-top: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-\[-200px\] {
      margin-top: -200px;
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-\[300px\] {
      margin-top: 300px;
    }
  }

  @media (width >= 64rem) {
    .lg\:mr-0 {
      margin-right: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:mr-8 {
      margin-right: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-2 {
      margin-bottom: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-3 {
      margin-bottom: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-20 {
      margin-bottom: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-\[0px\] {
      margin-bottom: 0;
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-80 {
      height: calc(var(--spacing) * 80);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[30vh\] {
      height: 30vh;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[380px\] {
      height: 380px;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[440px\] {
      height: 440px;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-max {
      height: max-content;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-h-\[320px\] {
      max-height: 320px;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-h-\[400px\] {
      max-height: 400px;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-h-\[480px\] {
      max-height: 480px;
    }
  }

  @media (width >= 64rem) {
    .lg\:min-h-full {
      min-height: 100%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-5\/12 {
      width: 41.6667%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-7\/12 {
      width: 58.3333%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[25\%\] {
      width: 25%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[35\%\] {
      width: 35%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[40\%\] {
      width: 40%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[50\%\] {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[60\%\] {
      width: 60%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[65\%\] {
      width: 65%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[80px\] {
      width: 80px;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[90\%\] {
      width: 90%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[350px\] {
      width: 350px;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[1024px\] {
      width: 1024px;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-auto {
      width: auto;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-fit {
      width: fit-content;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[100\%\] {
      max-width: 100%;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[220px\] {
      max-width: 220px;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[350px\] {
      max-width: 350px;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[1024px\] {
      max-width: 1024px;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[1400px\] {
      max-width: 1400px;
    }
  }

  @media (width >= 64rem) {
    .lg\:min-w-\[200px\] {
      min-width: 200px;
    }
  }

  @media (width >= 64rem) {
    .lg\:translate-x-\[-50\%\] {
      --tw-translate-x: -50%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (width >= 64rem) {
    .lg\:translate-y-0 {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-col {
      flex-direction: column;
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-3 {
      gap: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 64rem) {
    :where(.lg\:space-y-2 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-x-10 {
      column-gap: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:rounded-3xl {
      border-radius: var(--radius-3xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:rounded-l-none {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  @media (width >= 64rem) {
    .lg\:rounded-r-2xl {
      border-top-right-radius: var(--radius-2xl);
      border-bottom-right-radius: var(--radius-2xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:rounded-tr-\[100px\] {
      border-top-right-radius: 100px;
    }
  }

  @media (width >= 64rem) {
    .lg\:border-none {
      --tw-border-style: none;
      border-style: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:p-0 {
      padding: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-32 {
      padding-inline: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-0 {
      padding-block: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-10 {
      padding-block: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:pt-\[150px\] {
      padding-top: 150px;
    }
  }

  @media (width >= 64rem) {
    .lg\:pl-6 {
      padding-left: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:pl-10 {
      padding-left: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-start {
      text-align: start;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-\[14px\] {
      font-size: 14px;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-\[24px\] {
      font-size: 24px;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-\[44px\] {
      font-size: 44px;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-\[\#a91e22\] {
      color: #a91e22;
    }
  }

  @media (width >= 64rem) {
    .lg\:drop-shadow-none {
      --tw-drop-shadow: ;
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  @media (width >= 80rem) {
    .xl\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 80rem) {
    .xl\:\!ml-4 {
      margin-left: calc(var(--spacing) * 4) !important;
    }
  }

  @media (width >= 80rem) {
    .xl\:aspect-square {
      aspect-ratio: 1;
    }
  }

  @media (width >= 80rem) {
    .xl\:w-\[20\%\] {
      width: 20%;
    }
  }

  @media (width >= 80rem) {
    .xl\:w-\[40\%\] {
      width: 40%;
    }
  }

  @media (width >= 80rem) {
    .xl\:w-\[60\%\] {
      width: 60%;
    }
  }

  @media (width >= 80rem) {
    .xl\:w-\[70\%\] {
      width: 70%;
    }
  }

  @media (width >= 80rem) {
    .xl\:max-w-\[70\%\] {
      max-width: 70%;
    }
  }

  @media (width >= 80rem) {
    .xl\:max-w-\[270px\] {
      max-width: 270px;
    }
  }

  @media (width >= 80rem) {
    .xl\:min-w-\[26\%\] {
      min-width: 26%;
    }
  }

  @media (width >= 80rem) {
    .xl\:min-w-\[27\%\] {
      min-width: 27%;
    }
  }

  @media (width >= 80rem) {
    .xl\:min-w-\[70\%\] {
      min-width: 70%;
    }
  }

  @media (width >= 80rem) {
    .xl\:min-w-\[180px\] {
      min-width: 180px;
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 80rem) {
    .xl\:items-center {
      align-items: center;
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-10 {
      gap: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-\[3\%\] {
      gap: 3%;
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-\[4\%\] {
      gap: 4%;
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-x-20 {
      column-gap: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 80rem) {
    .xl\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 80rem) {
    .xl\:text-\[16px\] {
      font-size: 16px;
    }
  }

  @media (width >= 80rem) {
    .xl\:text-\[32px\] {
      font-size: 32px;
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:mt-6 {
      margin-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:text-\[72px\] {
      font-size: 72px;
    }
  }
}

:root {
  --background: #fff;
  --foreground: #fff;
}

.cmn-gradient-bg {
  --tw-gradient-position: to top right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
  --tw-gradient-from: #e70b00;
  --tw-gradient-stops: var(--tw-gradient-via-stops);
  --tw-gradient-via: #e73900;
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-exo2), sans-serif;
  padding-top: 80px;
}

.s_wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 50px 0;
}

@media screen and (width <= 768px) {
  .s_wrapper {
    padding: 30px 0;
  }
}

.btnCommon {
  color: #fff;
  background: linear-gradient(to right, #e70b00, #e73900);
  border-radius: .75rem;
  padding: .5rem 1.5rem;
  font-weight: 600;
  transition: all .3s;
}

.btnCommon:hover {
  opacity: .9;
}

.vignette:before {
  content: "";
  z-index: 1;
  pointer-events: none;
  background: radial-gradient(#0000004d 0% 50%, #0000 100%);
  position: absolute;
  inset: 0;
}

.gradient-button {
  text-align: center;
  cursor: pointer;
  background-size: 200%;
  width: max-content;
  margin: 0 auto;
  text-decoration: none;
  transition: all .5s;
  display: block;
  box-shadow: 0 4px 6px #32325d1c, 0 1px 3px #00000014;
}

.gradient-button:hover {
  background-position: 100%;
}

.gradient-button-light {
  text-align: center;
  cursor: pointer;
  background-image: linear-gradient(45deg, #fff, #fff 50%, #858585);
  background-size: 200%;
  width: max-content;
  margin: 0 auto;
  text-decoration: none;
  transition: all .5s;
  display: block;
  box-shadow: 0 4px 6px #32325d1c, 0 1px 3px #00000014;
  color: #000 !important;
  font-weight: 500 !important;
}

.text-line {
  background: linear-gradient(to right, #fff 50%, #fff3 50%) 100% 0 / 200% 100%;
  color: #0000;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: bold;
  line-height: 1.2;
  display: block;
}

.cmn-shadow {
  box-shadow: 0 3px 8px #0000001a;
}

.bg-gradient_2 {
  background-image: linear-gradient(to right, #e92c25 40%, #db714e 100%);
}

.fake {
  color: #fff;
  background-color: #101435;
}

.gradient-button-rd {
  background-image: linear-gradient(to right, #8e0e00 0%, #470700 70%, #8e0e00 100%);
}

.bg-gradient {
  background-image: linear-gradient(to right, #071b01 0%, #02490e 100%);
}

.gradient-button {
  background-image: linear-gradient(to right, #071b01 0%, #006e12 100% 120%, #071b01 180%);
}

.gradient-button, .gradient-button-rd {
  background-size: 200%;
  transition: all .5s;
  display: block;
}

.gradient-button:hover, .gradient-button-rd:hover {
  background-position: 100%;
  text-decoration: none;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/