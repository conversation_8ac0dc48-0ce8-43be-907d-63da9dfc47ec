import InsuranceMaster from '@/components/Insurance/InsuranceMaster'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
    <Head>
        <title>Insurance Services | Secure Your Future with Winshine Financial</title>
        <meta name="description" content="Explore comprehensive insurance solutions with Winshine Financial Services. Protect your family and assets with life, health, and general insurance plans tailored for you." />
        <meta name="keywords" content="insurance services, life insurance, health insurance, general insurance, Winshine insurance plans, financial protection, secure future" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Insurance Services | Winshine Financial Services" />
        <meta property="og:description" content="Protect what matters most with Winshine’s trusted insurance plans. Get personalized advice on life, health, and general insurance policies." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/insurance/insurance-banner.webp" />
        <meta property="og:url" content="https://winshine.nipralo.com/insurance" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/insurance/insurance-banner.webp" />
        <link rel="canonical" href="https://winshine.nipralo.com/insurance" />
      </Head>
    <InsuranceMaster/>
    </>
  )
}

export default page
