import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import Link from "next/link";
import React from "react";

const AboutWinshine = () => {
  return (
    <section className="bg-white">
      <div className="s_wrapper">
        <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-2 lg:mb-4 text-black text-center">
          <SkewFadeInWords text="About Winshine" />
        </h2>
        <div className="mx-auto gap-4 flex flex-col text-[#333] text-center max-w-4xl">
          <p>
            Winshine has been a cornerstone in the financial services and
            investment industry for over three decades, redefining success,
            credibility, and client trust. As a franchisee, you gain access to
            our complete arsenal - from our esteemed brand name to our extensive
            experience & expertise, state-of-the-art office space, dedicated
            staff, advanced computer systems, telecommunication infrastructure,
            and all the resources needed to kickstart your own financial
            services business.
          </p>
          <Link
            href="/about-us"
            className="mx-auto gradient-button text-start text-[#ffffff] transition duration-300 ease-in-out px-4 py-2 rounded-xl font-semibold"
          >
            LEGACY OF {new Date().getFullYear() - 1995}+ YEARS
          </Link>
        </div>
      </div>
    </section>
  );
};

export default AboutWinshine;
