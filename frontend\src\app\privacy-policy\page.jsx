import PrivacyPolicyMaster from "@/components/privacyPolicy/PrivacyPolicyMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
    <Head>
        <title>Privacy Policy | Winshine Financial Services</title>
        <meta name="description" content="Read Winshine Financial Services’ Privacy Policy to understand how we collect, use, and protect your personal information. Your trust and privacy are our top priorities." />
        <meta name="keywords" content="privacy policy, data protection, personal information, Winshine privacy, GDPR compliance, user data security" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Privacy Policy | Winshine Financial Services" />
        <meta property="og:description" content="Learn about Winshine’s commitment to protecting your data privacy and how we handle your personal information responsibly." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/investment/2148803950.jpg" />
        <meta property="og:url" content="https://winshine.nipralo.com/privacy-policy" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/investment/2148803950.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/privacy-policy" />
      </Head>
      <PrivacyPolicyMaster />
    </>
  );
};

export default page;
