import CashFlowTable from '@/components/calculator/retirement-calculator/CashflowTable'
import RetirementCalculator from '@/components/calculator/retirement-calculator/RetirementCalculator'
import Banner from '@/components/ui/reusable/banner/Banner'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
    <Head>
        <title>Retirement Calculator | Secure Your Future Financially | Winshine</title>
        <meta name="description" content="Plan for a stress-free retirement with Winshine’s Retirement Calculator. Estimate how much you need to save and invest today to achieve your future financial goals." />
        <meta name="keywords" content="retirement calculator, retirement planning, financial future, pension calculator, retirement savings, Winshine financial tools" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Retirement Calculator | Plan Your Financial Freedom | Winshine" />
        <meta property="og:description" content="Use our Retirement Calculator to plan how much you need to retire comfortably. Winshine helps you prepare for a financially secure future." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/calculator/Retirement-calc-banner.jpg" />
        <meta property="og:url" content="https://winshine.nipralo.com/calculator/retirement-calculator" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/calculator/Retirement-calc-banner.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/calculator/retirement-calculator" />
      </Head>
      <Banner
        title="Retirement Calculator"
        imageUrl="/images/calculator/Retirement-calc-banner.jpg"
        subtitle=""
      />
      <RetirementCalculator />
      <CashFlowTable/>
    </>
  )
}

export default page
