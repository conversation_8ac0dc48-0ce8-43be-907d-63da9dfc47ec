// 'use strict';

// /**
//  * contact-enquiry controller
//  */

// const { createCoreController } = require('@strapi/strapi').factories;

// module.exports = createCoreController('api::contact-enquiry.contact-enquiry');




"use strict";

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::contact-enquiry.contact-enquiry",
  ({ strapi }) => ({
    async create(ctx) {
      try {
        // ✅ Extract data properly from request
        const { data } = ctx.request.body;

        if (!data) {
          return ctx.badRequest("Missing 'data' payload in the request.");
        }

        const { name, email, phone, subject, message} = data;

        // ✅ Validate required fields
        if (!name || !email || !phone) {
          return ctx.badRequest("Missing required fields");
        }

        // ✅ Save the data in Strapi
        const enquiry = await strapi.entityService.create(
          "api::contact-enquiry.contact-enquiry",
          {
            data: {
              name,
              email,
              phone,
              subject,
              message,
            },
          }
        );

        // ✅ Send Email to Admin
        // ✅ Send Email to Admin
        await strapi.plugins["email"].services.email.send({
          to: ["<EMAIL>"], // Admin email
          from: '"Winshine Financial Services" <<EMAIL>>',
          subject: `New Enquiry Received from ${name}`,
          text: `Dear Admin,
  
  A new enquiry has been submitted. Here are the details:
  
  Name: ${name}
  Phone: ${phone}
  Email: ${email}
  Subject: ${subject || "No subject provided"}
  Message: ${message || "No message provided"}
  
  Please review the enquiry and respond accordingly.
  
  Best regards,  
  Winshine Financial Services
  `,
          html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">New Enquiry Received</h2>
        <p style="color: #555;">A new enquiry has been submitted. Here are the details:</p>
        <table style="width: 100%; border-collapse: collapse;">
          <tr><td style="padding: 8px; font-weight: bold;">Name:</td><td style="padding: 8px;">${name}</td></tr>
          <tr><td style="padding: 8px; font-weight: bold;">Phone:</td><td style="padding: 8px;">${phone}</td></tr>
          <tr><td style="padding: 8px; font-weight: bold;">Email:</td><td style="padding: 8px;">${email}</td></tr>
          <tr><td style="padding: 8px; font-weight: bold;">Subject:</td><td style="padding: 8px;">${subject}</td></tr>
          <tr><td style="padding: 8px; font-weight: bold;">Message:</td><td style="padding: 8px;">${message || "No message provided"}</td></tr>
        </table>
        <br>
        <p style="color: #555;">Please review the enquiry and respond accordingly.</p>
        <p style="color: #777;">Best regards,</p>
        <p style="font-weight: bold; color: #333;">Winshine Financial Services</p>
      </div>
    `,
        });

        // ✅ Send Confirmation Email to the User
        await strapi.plugins["email"].services.email.send({
          to: email, // User's email
          from: '"Winshine Financial Services" <<EMAIL>>',
          subject:
            "Thank You for Contacting Winshine Financial Services - We've Received Your Message",
          text: `Hi ${name},
          
          Thank you for reaching out to Winshine Financial Services. We’ve successfully received your inquiry and appreciate you taking the time to get in touch.
          
          Our team is reviewing your message and will respond to you within 24 to 48 business hours. In the meantime, here’s a quick summary of your submission:
          
          Submission Details
          Name: ${name}
          
          Phone: ${phone}
          
          Email: ${email}

          Subject: ${subject || "No subject provided"}
          
          Message: ${message || "No message provided"}
          
          If your matter is urgent, feel free to call us directly at +91 98331 35459 or 022 35939918.
          
          Thank you once again for contacting us. We look forward to assisting you!
          
          Best regards,  
          Winshine Financial Services
          B 213, Damji Shamji Corporate Square, Sawali Society, Laxmi Nagar, Ghatkopar East, Mumbai, Maharashtra - 400075
          <EMAIL>
          https://www.winshine.in
          `,
          html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <p style="color: #555;">Hi ${name},</p>
                <p>Thank you for reaching out to <strong>Winshine Financial Services</strong>. We’ve successfully received your inquiry and appreciate you taking the time to get in touch.</p>
                <p>Our team is reviewing your message and will respond to you within 24 to 48 business hours. In the meantime, here’s a quick summary of your submission:</p>
                <h3 style="color: #444;">Submission Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr><td style="padding: 8px; font-weight: bold;">Name:</td><td style="padding: 8px;">${name}</td></tr>
                  <tr><td style="padding: 8px; font-weight: bold;">Phone:</td><td style="padding: 8px;">${phone}</td></tr>
                  <tr><td style="padding: 8px; font-weight: bold;">Email:</td><td style="padding: 8px;">${email}</td></tr>
                  <tr><td style="padding: 8px; font-weight: bold;">Subject:</td><td style="padding: 8px;">${subject}</td></tr>
                  <tr><td style="padding: 8px; font-weight: bold;">Message:</td><td style="padding: 8px;">${message || "No message provided"}</td></tr>
                </table>
                <br>
                <p style="color: #555;">If your matter is urgent, feel free to call us directly at <strong>+91 98331 35459 or 022 35939918</strong>.</p>
                <p style="color: #777;">Thank you once again for contacting us. We look forward to assisting you!</p>
                <p style="font-weight: bold; color: #333;">Best regards,</p>
                <p style="font-weight: bold; color: #444;">Winshine Financial Services</p>
                <hr style="border: none; border-top: 1px solid #eee;">
                <p style="font-size: 14px; color: #999;">
                  107, Manratna Business Park, Junction of Tilak Road, Derasar Ln, Ghatkopar East, Mumbai, Maharashtra - 400077, India<br>
                  <a href="mailto:<EMAIL>" style="color: #007bff;"><EMAIL></a><br>
                  <a href="https://www.winshine.in" target="_blank" style="color: #007bff;">www.winshine.in</a>
                </p>
              </div>
            `,
        });

        return ctx.send({ message: "Contact enquiry submitted successfully!" });
      } catch (error) {
        console.error("❌ Error:", error);
        return ctx.internalServerError("Failed to process enquiry");
      }
    },

    // async export(ctx) {
    //   try {
    //     // Query all fields (you can still limit it by specifying fields if needed)
    //     const entries = await strapi.entityService.findMany('api::contact-enquiry.contact-enquiry', {
    //       populate: [], // Don't auto-populate relations
    //     });
    
    //     // Map the data to only include the desired fields

    //     const uniqueEntries = entries.map(entry => ({
    //       'Id': entry.id,
    //       'Created at': entry.createdAt,
    //       'Full Name': entry.name,
    //       'Phone Number': entry.phone,
    //       'Email Address': entry.email,
    //       'Subject': entry.subject,
    //       'Message': entry.message,
    //     }));
        
    
    //     // Import json2csv to convert JSON to CSV format
    //     const { parse } = require('json2csv');
    //     const csv = parse(uniqueEntries);
    
    //     // Set headers for the CSV download
    //     ctx.set('Content-Type', 'text/csv');
    //     ctx.set('Content-Disposition', 'attachment; filename=contact-enquiries.csv');
    //     ctx.body = csv; // Send the CSV as response
    //   } catch (err) {
    //     console.error('Error in exportCSV method:', err);
    //     ctx.throw(500, 'Internal Server Error');
    //   }
    // }
  })
);
