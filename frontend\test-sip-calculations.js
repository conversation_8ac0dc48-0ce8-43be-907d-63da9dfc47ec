// Test file to verify SIP calculation accuracy
// This file contains test cases to validate the mathematical formulas

// Test case 1: Monthly SIP for target amount
// Target: ₹15,00,000, Duration: 10 years, Rate: 12% p.a.
// Expected monthly SIP: approximately ₹6,435

function testMonthlySIPForTarget() {
  const targetAmount = 1500000;
  const years = 10;
  const annualRate = 0.12;
  const monthlyRate = annualRate / 12;
  const months = years * 12;
  
  // Formula: PMT = FV / (((1 + r)^n - 1) / r) * (1 + r)
  const requiredSIP = targetAmount / (((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) * (1 + monthlyRate));
  
  console.log("Test 1 - Monthly SIP for Target Amount:");
  console.log(`Target Amount: ₹${targetAmount.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Required Monthly SIP: ₹${Math.round(requiredSIP).toLocaleString()}`);
  console.log(`Total Investment: ₹${Math.round(requiredSIP * months).toLocaleString()}`);
  console.log(`Returns: ₹${Math.round(targetAmount - requiredSIP * months).toLocaleString()}`);
  console.log("---");
}

// Test case 2: Future value of monthly SIP
// Monthly SIP: ₹5,000, Duration: 15 years, Rate: 12% p.a.
// Expected future value: approximately ₹25,00,000

function testFutureValueOfSIP() {
  const monthlySIP = 5000;
  const years = 15;
  const annualRate = 0.12;
  const monthlyRate = annualRate / 12;
  const months = years * 12;
  
  // Formula: FV = PMT * (((1 + r)^n - 1) / r) * (1 + r)
  const futureValue = monthlySIP * (((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) * (1 + monthlyRate));
  const totalInvested = monthlySIP * months;
  
  console.log("Test 2 - Future Value of Monthly SIP:");
  console.log(`Monthly SIP: ₹${monthlySIP.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Future Value: ₹${Math.round(futureValue).toLocaleString()}`);
  console.log(`Total Investment: ₹${totalInvested.toLocaleString()}`);
  console.log(`Returns: ₹${Math.round(futureValue - totalInvested).toLocaleString()}`);
  console.log("---");
}

// Test case 3: Lumpsum investment
// Lumpsum: ₹1,00,000, Duration: 10 years, Rate: 12% p.a.
// Expected future value: approximately ₹3,10,585

function testLumpsumInvestment() {
  const lumpsum = 100000;
  const years = 10;
  const annualRate = 0.12;
  
  // Formula: FV = PV * (1 + r)^n
  const futureValue = lumpsum * Math.pow(1 + annualRate, years);
  
  console.log("Test 3 - Lumpsum Investment:");
  console.log(`Lumpsum Amount: ₹${lumpsum.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Future Value: ₹${Math.round(futureValue).toLocaleString()}`);
  console.log(`Returns: ₹${Math.round(futureValue - lumpsum).toLocaleString()}`);
  console.log("---");
}

// Test case 4: Required lumpsum for target
// Target: ₹10,00,000, Duration: 8 years, Rate: 12% p.a.
// Expected lumpsum: approximately ₹4,03,883

function testRequiredLumpsum() {
  const targetAmount = 1000000;
  const years = 8;
  const annualRate = 0.12;
  
  // Formula: PV = FV / (1 + r)^n
  const requiredLumpsum = targetAmount / Math.pow(1 + annualRate, years);
  
  console.log("Test 4 - Required Lumpsum for Target:");
  console.log(`Target Amount: ₹${targetAmount.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Required Lumpsum: ₹${Math.round(requiredLumpsum).toLocaleString()}`);
  console.log(`Returns: ₹${Math.round(targetAmount - requiredLumpsum).toLocaleString()}`);
  console.log("---");
}

// Test case 5: Quarterly SIP
// Quarterly SIP: ₹15,000, Duration: 10 years, Rate: 12% p.a.
// Expected future value

function testQuarterlySIP() {
  const quarterlySIP = 15000;
  const years = 10;
  const annualRate = 0.12;
  const quarterlyRate = annualRate / 4;
  const quarters = years * 4;
  
  // Formula: FV = PMT * (((1 + r)^n - 1) / r) * (1 + r)
  const futureValue = quarterlySIP * (((Math.pow(1 + quarterlyRate, quarters) - 1) / quarterlyRate) * (1 + quarterlyRate));
  const totalInvested = quarterlySIP * quarters;
  
  console.log("Test 5 - Quarterly SIP:");
  console.log(`Quarterly SIP: ₹${quarterlySIP.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Future Value: ₹${Math.round(futureValue).toLocaleString()}`);
  console.log(`Total Investment: ₹${totalInvested.toLocaleString()}`);
  console.log(`Returns: ₹${Math.round(futureValue - totalInvested).toLocaleString()}`);
  console.log("---");
}

// Test case 6: Zero interest rate
// Monthly SIP: ₹10,000, Duration: 5 years, Rate: 0% p.a.

function testZeroInterestRate() {
  const monthlySIP = 10000;
  const years = 5;
  const annualRate = 0;
  const months = years * 12;
  
  // With 0% interest, future value = total invested
  const futureValue = monthlySIP * months;
  const totalInvested = monthlySIP * months;
  
  console.log("Test 6 - Zero Interest Rate:");
  console.log(`Monthly SIP: ₹${monthlySIP.toLocaleString()}`);
  console.log(`Duration: ${years} years`);
  console.log(`Annual Rate: ${annualRate * 100}%`);
  console.log(`Future Value: ₹${futureValue.toLocaleString()}`);
  console.log(`Total Investment: ₹${totalInvested.toLocaleString()}`);
  console.log(`Returns: ₹${(futureValue - totalInvested).toLocaleString()}`);
  console.log("---");
}

// Run all tests
console.log("=== SIP Calculator Mathematical Validation Tests ===\n");

testMonthlySIPForTarget();
testFutureValueOfSIP();
testLumpsumInvestment();
testRequiredLumpsum();
testQuarterlySIP();
testZeroInterestRate();

console.log("=== Test Results Summary ===");
console.log("All calculations use standard financial formulas:");
console.log("1. SIP Future Value: FV = PMT × (((1+r)^n - 1)/r) × (1+r)");
console.log("2. Required SIP: PMT = FV / (((1+r)^n - 1)/r) × (1+r)");
console.log("3. Lumpsum Future Value: FV = PV × (1+r)^n");
console.log("4. Required Lumpsum: PV = FV / (1+r)^n");
console.log("5. All calculations assume beginning-of-period payments");
