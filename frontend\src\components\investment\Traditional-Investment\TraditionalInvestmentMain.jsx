"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import "./TraditionalInvestmentMain.css";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";

const data = [
  {
    title: "Equity (Stocks)",
    description: {
      p1: "Ideal for risk-takers seeking high returns and for long-term wealth creation.",
      p2: "Taxed at 10% above long-term capital gains of one lakh rupees and 15% for short-term.",
      list: [
        "Returns Range: Historically, 12% to 15% over the long term. However, short-term returns can be highly volatile.",
        "Risk: High risk due to market fluctuations and company-specific factors. However, with appropriate research & analysis along with diversification, risk can be mitigated.",
        "Liquidity: Money can be made liquid within 1 day.",
        "Investment Horizon: Ideally, long-term (5 years or more).",
      ],
      disclaimer:
        "Disclaimer: Equity investments carry inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/investment1.jpg",
  },
  {
    title: "F&O Trading",
    description: {
      p1: "Navigate the equity market with strategies commonly used for managing risk. Leverage it for hedging, arbitrage, diversification, income generation, portfolio adjustment or speculative purposes.",
      list: [
        "Returns Range: Returns can vary widely based on strategies and market conditions. Potential for high returns but also significant losses.",
        "Risk: Very high risk due to leverage and market volatility.",
        "Liquidity: Moderate to high liquidity, depending on the specific contract.",
        "Investment Horizon: Short to medium term (months to a few years).",
      ],
      disclaimer:
        "Disclaimer: F&O Trading carries inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/fno.webp",
  },
  {
    title: "Currency Trading",
    description: {
      p1: "Engage in currency market activities for currency exchange purposes and for international trade and diversification.",
      list: [
        "Returns Range: Currency markets are less volatile, with returns typically lower than equities.",
        "Risk: Moderate risk due to exchange rate fluctuations.",
        "Liquidity: High liquidity as forex markets operate 24/7.",
        "Investment Horizon: Short to medium term (months to a few years).",
      ],
      disclaimer:
        "Disclaimer: Currency Trading carries inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/investment3.jpg",
  },
  {
    title: "Commodities Trading",
    description: {
      p1: "Participate in the commodity market for diversifying investments. Hedge against inflation and geopolitical risks.",
      list: [
        "Returns Range: Varies by commodity. Precious metals (gold, silver) may offer stable returns, while energy commodities (crude oil, natural gas) can be more volatile.",
        "Risk: Moderate to high risk, depending on the commodity.",
        "Liquidity: Varies by commodity; some are highly liquid (gold), while others may have lower liquidity.",
        "Investment Horizon: Short to medium term (months to a few years).",
      ],
      disclaimer:
        "Disclaimer: Commodities Trading carries inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/investment4.jpg",
  },
  {
    title: "Mutual Fund",
    description: {
      p1: "Accessible for small investors, managed by professionals. Offers tax-efficient gains, and diversified exposure to stocks and bonds.",
      list: [
        "Returns Range: Depends on the fund type (equity, debt, hybrid). Equity funds historically offer higher returns (10% to 15% annually), while debt funds provide more stable but lower returns (6% to 8%).",
        "Risk: Varies by fund category (equity funds are riskier, debt funds are safer).",
        "Liquidity: High liquidity as mutual fund units can be redeemed daily.",
        "Investment Horizon: Varies by fund type (short-term for debt funds, long-term for equity funds).",
      ],
      disclaimer:
        "Disclaimer: Investments in Mutual Funds carry inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/investment5.jpg",
  },
  {
    title: "Corporate FD",
    description: {
      p1: "Invest in fixed deposits offered by corporate entities such as NBFCs, companies, etc. Suitable for investors who want to earn a fixed and regular income at a higher rate than bank FDs.",
      list: [
        "Returns Range: Returns are fixed and predetermined based on the interest rate and tenure of the corporate FD. Interest rates can vary from 6% to 10% per annum, depending on the credit rating and financial health of the corporate entity.",
        "Risk: Low to moderate risk, depending on the credit rating and financial well being of the corporate entity. Corporate FD’s are usually unsecured.",
        "Liquidity: Low liquidity, as corporate FDs, have a fixed tenure and cannot be withdrawn before maturity, except with a penalty.",
        "Investment Horizon: Short to medium term (1 year to 5 years).",
      ],
    },
    image: "/images/investment/investment6.jpg",
  },
  {
    title: "Tax-Free Bond",
    description: {
      p1: "Tax-free bonds, typically issued by government-backed entities in compliance with government policies, offer fixed returns without tax implications. Ideal for High Net Worth Individuals (HNIs) seeking tax-free income with capital protection, these bonds provide a stable investment avenue aligned with financial objectives.",
      list: [
        "Returns Range: Tax-free bonds provide fixed and predetermined returns determined by the bond’s interest rate and tenure. Historically, issued tax-free bonds have offered returns ranging from 7% to 9% per annum. Enjoy the benefit of interest income exempt from income tax.",
        "Risk: Tax-free bonds are virtually risk-free since they are issued by the government, semi-government companies, or public sector undertakings.",
        "Liquidity: Moderate liquidity, as bonds can be traded in the secondary market, but may not have a high volume or demand.",
        "Investment Horizon: Medium to long term (5 years or more).",
      ],
    },
    image: "/images/investment/investment7.jpg",
  },
  {
    title: "NCD (Non-convertible Debentures)",
    description: {
      p1: "Invest in debt issued by corporate entities or government agencies, offering potentially higher interest rates than tax-free bonds. These debentures may be secured or unsecured, providing investors with the opportunity to earn a fixed and regular income while safeguarding their capital.",
      list: [
        "Returns Range: Returns are fixed and predetermined based on the interest rate and tenure of the bond. Interest rates can vary from 7% to 11% per annum, depending on the credit rating and financial health of the issuer.",
        "Risk: Low to Moderate risk.",
        "Liquidity: Moderate liquidity, as bonds can be traded in the secondary market, but may not have a high volume or demand.",
        "Investment Horizon: Medium to long term (1 to 5 years or more)",
      ],
    },
    image: "/images/investment/investment8.jpg",
  },
  {
    title: "Equity Advisory",
    description: {
      p1: "Invest in expert advisory and wealth management services that offer customised and personalised solutions for your investment needs. Suitable for investors who want to benefit from the guidance, research, and recommendations of professional stock advisors and wealth managers.",
      list: [
        "Returns Range: Returns can vary widely based on the quality, performance, and suitability of the advisory and wealth management services. Potential for high returns but along with risk of loss.",
        "Risk: Moderate to high risk, depending on the quality, performance, and suitability of the advisory and wealth management services.",
        "Liquidity: High liquidity, as the advisory and wealth management services can be availed at any time, and the investments can be liquidated at the investor’s discretion.",
        "Investment Horizon: Short to long term (days to years).",
      ],
      disclaimer:
        "Disclaimer: Equity Advisory carries inherent market risks, and there is no guarantee of capital protection or returns. Investors should carefully consider their risk tolerance before investing.",
    },
    image: "/images/investment/investment9.jpg",
  },
  {
    title: "Global Investments",
    description: {
      p1: "Invest in the securities of foreign companies or markets that offer exposure to different geographies, sectors, and economies. Suitable for investors who want to diversify their portfolio and benefit from the growth potential of emerging or developed markets.",
      list: [
        "Returns Range: Returns can vary widely based on the performance, currency, and market conditions of the foreign securities or markets. Potential for high returns but also significant losses.",
        "Risk: High risk due to currency fluctuations, political and economic instability, and regulatory differences in foreign markets.",
        "Liquidity: Moderate to high liquidity, depending on the availability and accessibility of the foreign securities or markets.",
        "Investment Horizon: Medium to long term (3 years or more).",
      ],
    },
    image: "/images/investment/investment10.jpg",
  },
  {
    title: "Tax Savers",
    description: {
      p1: "Invest in instruments that offer tax benefits under various sections of the Income Tax Act, such as Section 80C (ELSS, Life Insurance Policy), 80D (Mediclaim), etc. Suitable for investors who want to reduce their tax liability and save money.",
      list: [
        "Returns Range: Returns can vary widely based on the type, category, and performance of the tax saver instrument. Historically, the average annualized return of equity-linked savings schemes ELSS was 12.3% for the period from 2008 to 2018, while the average annualized return of other tax-saving instruments such as PPF, NSC, etc. was 7.6% for the same period.",
        "Risk: Moderate to low risk, depending on the type and category of the tax saver instrument. ELSS are more risky than other tax saver instruments, but also offer higher returns. Other tax saver instruments such as public provident fund (PPF), national savings certificate (NSC), etc. offer guaranteed returns but with lower rates.",
        "Liquidity: Low to moderate liquidity, depending on the type and category of the tax saver instrument. ELSS have a lock-in period of 3 years, while other tax saver instruments have longer lock-in periods ranging from 5 to 15 years.",
        "Investment Horizon: Medium to long term (3 years or more).",
      ],
    },
    image: "/images/investment/investment11.jpg",
  },
  {
    title: "Systematic Investment Plan (SIP)",
    description: {
      p1: "",
      list: [
        "A method to invest small, fixed amounts regularly into mutual funds.",
        "Builds discipline and reduces the risk of investing at the wrong time.",
        "Suitable for: Salaried individuals and first-time investors.",
        "💡 How SIPs make investing a habit, not a burden.",
      ],
    },
    image: "/images/investment/investment101.jpeg",
  },
  {
    title: "Portfolio Management Services (PMS)",
    description: {
      p1: "",
      list: [
        "Personalized equity investment strategies managed by professionals.",
        "Requires higher minimum investment (currently ₹50 lakhs).",
        "Suitable for: High-net-worth individuals seeking a custom approach.",
        "💡 PMS or Advisory – What’s better for you?",
      ],
    },
    image: "/images/investment/investment102.jpeg",
  },
  {
    title: "Advisory Services",
    description: {
      p1: "",
      list: [
        "Offers flexibility and lower entry barrier than PMS.",
        "Suitable for: Informed investors who prefer expert support but want to execute investments themselves.",
        "💡 See how advisory services can simplify investing.",
      ],
    },
    image: "/images/investment/investment103.jpeg",
  },
  {
    title: "Bonds",
    description: {
      p1: "",
      list: [
        "Loans you give to governments or companies in return for fixed interest.",
        "Includes Tax-Free Bonds, which offer tax-exempt interest for eligible investors.",
        "Suitable for: Low-risk investors and those in higher tax brackets.",
        "💡 What makes bonds a smart stabilizer in your portfolio?",
      ],
    },
    image: "/images/investment/investment104.jpeg",
  },
  {
    title: "Tax Saving Investments",
    description: {
      p1: "",
      list: [
        "Includes options like ELSS (Equity Linked Savings Schemes), PPF (Public Provident Fund), and 5-Year Bank FDs.",
        "Offers tax deductions under Section 80C.",
        "Suitable for: Salaried and self-employed individuals looking to reduce taxable income.",
        "💡 Which tax-saving instrument fits your goals best?",
      ],
    },
    image: "/images/investment/investment105.jpeg",
  },
  {
    title: "Foreign Investment via GIFT City",
    description: {
      p1: "",
      list: [
        "Explore regulated global opportunities — from US stocks to ETFs — via India's GIFT City framework.",
        "Brings global diversification with tax benefits.",
        "Suitable for: Investors looking to expand beyond Indian markets.",
        "💡 How GIFT City opens global doors for Indian investors.",
      ],
    },
    image: "/images/investment/investment106.jpeg",
  },
  {
    title: "Currency Hedging",
    description: {
      p1: "",
      list: [
        "Helps protect your investments or business from currency fluctuations.",
        "Relevant only if you deal in foreign exchange (exports, imports, or foreign currency loans).",
        "Suitable for: Business owners or individuals with overseas exposure.",
        "💡 Why hedging is risk management, not speculation.",
      ],
    },
    image: "/images/investment/investment107.jpeg",
  },
];

export default function TraditionalInvestmentMain() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [blogs, setBlogs] = useState([]);
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/blogs?populate=*`,
          {
            headers: {
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setBlogs(data?.data); // Strapi response format
      } catch (err) {
        console.error(err.message);
      }
    };

    fetchBlogs();
  }, []);

  const matchedBlog = blogs.find(
    (blog) => blog?.investment_type === data[activeIndex]?.title // match type
  );

  return (
    <section className="bg-[#f9f3f1]">
      <div className="s_wrapper">
        <div className="flex flex-col lg:flex-row">
          {/* Sidebar for desktop */}

          <aside className="w-full lg:w-[25%] xl:w-[20%] bg-white rounded-2xl shadow-md p-4 mb-4 lg:mb-0 hidden lg:block max-h-[90vh] overflow-y-auto custom-scrollbar">

            <ul className="space-y-1 lg:space-y-2 flex flex-wrap lg:flex-col">
              {data.map((item, index) => (
                <li
                  key={index}
                  onClick={() => {
                    setActiveIndex(index);
                    setTimeout(() => {
                      window.scrollTo({
                        top: window.innerHeight * 0.6,
                        behavior: "smooth",
                      });
                    }, 100);
                  }}
                  className={`cursor-pointer p-2 rounded-md border border-gray-400 lg:border-none transition-colors text-sm lg:text-base mr-1 mb-1 lg:mr-0 lg:mb-2 ${
                    index === activeIndex
                      ? "bg-[#101435] text-white"
                      : "hover:bg-blue-100 text-gray-700"
                  }`}
                >
                  {`${index + 1} - `}
                  {item.title}
                </li>
              ))}
            </ul>
          </aside>

          {/* List for Mobile */}
          <div
            className="bg-white rounded-2xl shadow-md p-4 cursor-pointer border border-gray-300 lg:hidden"
            onClick={() => setDropdownOpen(!dropdownOpen)}
          >
            <div className="text-sm font-medium text-gray-700 flex justify-between items-center">
              {`${activeIndex + 1} - ${data[activeIndex]?.title}`}
              <svg
                className={`w-4 h-4 transition-transform ${
                  dropdownOpen ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <AnimatePresence>
            {dropdownOpen && (
              <motion.ul
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.25, ease: "easeOut" }}
                className="mt-2 bg-white rounded-xl shadow-md border border-gray-300 overflow-hidden"
              >
                {data.map((item, index) => (
                  <li
                    key={index}
                    onClick={() => {
                      setActiveIndex(index);
                      setDropdownOpen(false);
                    }}
                    className={`cursor-pointer px-4 py-2 text-sm transition-colors ${
                      index === activeIndex
                        ? "bg-[#101435] text-white"
                        : "hover:bg-blue-100 text-gray-700"
                    }`}
                  >
                    {`${index + 1} - ${item.title}`}
                  </li>
                ))}
              </motion.ul>
            )}
          </AnimatePresence>
          {/* Content Area */}
          <main className="flex-1">
            <div className="bg-transparent flex-col lg:flex-row p-4 lg:p-6 lg:pl-10 my-4 rounded-2xl lg:rounded-l-none lg:rounded-r-2xl flex gap-4 xl:gap-8">
              <div id="scroll-up" className="w-full lg:w-[50%] xl:w-[40%]">
                <Image
                  src={data[activeIndex].image || "/placeholder.svg"}
                  alt={data[activeIndex].title}
                  width={400}
                  height={400}
                  className="max-w-full md:max-w-[400px] w-full h-auto mx-auto lg:mx-0 rounded shadow-sm"
                />
                {matchedBlog?.slug && (
                  <p className="text-gray-500 font-light text-xs lg:text-base mt-3 lg:mt-4 hidden lg:block">
                    Explore in-depth insights into this investment opportunity
                    by clicking{" "}
                    <span className="text-blue-600 hover:text-blue-800 hover:underline">
                      <Link href={`/blogs/${matchedBlog?.slug || ""}`}>
                        here
                      </Link>
                    </span>
                  </p>
                )}

                {data[activeIndex].description.disclaimer && (
                  <p className="text-gray-500 font-light italic text-xs lg:text-base mt-3 lg:mt-4 hidden lg:block">
                    {data[activeIndex].description.disclaimer}
                  </p>
                )}
              </div>
              <div className="w-full lg:w-[50%] xl:w-[60%]">
                <h2 className="text-xl lg:text-2xl font-semibold mb-3 lg:mb-4 text-black mt-3 lg:mt-4 capitalize">
                  {data[activeIndex].title}
                </h2>
                <div>
                  {data[activeIndex].description.p1 && (
                    <p className="text-gray-700">
                      {data[activeIndex].description.p1}
                    </p>
                  )}
                  {data[activeIndex].description.p2 && (
                    <p className="text-gray-700">
                      {data[activeIndex].description.p2}
                    </p>
                  )}
                </div>
                {data[activeIndex].description.list && (
                  <ul className="text-gray-700 list-disc pl-5 lg:pl-6 mt-2 text-sm lg:text-base">
                    {data[activeIndex].description.list.map((li, index) => (
                      <li key={index}>{li}</li>
                    ))}
                  </ul>
                )}
                {matchedBlog?.slug && (
                  <p className="text-gray-500 font-light text-xs mt-3 lg:mt-4 lg:hidden">
                    Explore in-depth insights into this investment opportunity
                    by clicking{" "}
                    <span className="text-blue-600 hover:text-blue-800 hover:underline">
                      <Link href={`/blogs/${matchedBlog?.slug || ""}`}>
                        here
                      </Link>
                    </span>
                  </p>
                )}
                {data[activeIndex].description.disclaimer && (
                  <p className="text-gray-500 font-light italic text-xs mt-3 lg:mt-4 lg:hidden">
                    {data[activeIndex].description.disclaimer}
                  </p>
                )}
              </div>
            </div>
          </main>
          {/* <main className="flex-1">
            <div className="bg-[#e7380315] p-4 lg:p-6 my-4 rounded-2xl lg:rounded-l-none lg:rounded-r-2xl">
              <div className="clearfix">
                <Image
                  src={data[activeIndex].image || "/placeholder.svg"}
                  alt={data[activeIndex].title}
                  width={300}
                  height={300}
                  className="w-full h-[280px] max-h-[280px] lg:max-w-[350px] object-cover rounded-lg shadow-sm clearfix-img lg:mr-8 mb-2"
                />

                <div className="">
                  <h2 className="text-xl lg:text-2xl font-semibold mb-2 lg:mb-3 text-black capitalize">
                    {data[activeIndex].title}
                  </h2>

                  <div className="space-y-2 text-gray-700 text-sm lg:text-base  text-justify lg:text-start">
                    {data[activeIndex].description.p1 && (
                      <p className=" text-justify lg:text-start">{data[activeIndex].description.p1}</p>
                    )}
                    {data[activeIndex].description.p2 && (
                      <p className=" text-justify lg:text-start">{data[activeIndex].description.p2}</p>
                    )}
                  </div>

                  {data[activeIndex].description.list && (
                    <ul className="list-disc pl-5 lg:pl-6 mt-2 text-gray-700 space-y-1 text-sm lg:text-base">
                      {data[activeIndex].description.list.map((li, index) => (
                        <li key={index}>{li}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>

              {/* <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
      
                <div className="w-full lg:w-[350px] flex-shrink-0">
                  <Image
                    src={data[activeIndex].image || "/placeholder.svg"}
                    alt={data[activeIndex].title}
                    width={300}
                    height={300}
                    className="w-full h-full max-h-[400px] object-cover rounded-lg shadow-sm"
                  />
                </div>

      
                <div className="flex flex-col justify-start flex-1">

                  <h2 className="text-xl lg:text-2xl font-semibold mb-2 lg:mb-3 text-black capitalize">
                    {data[activeIndex].title}
                  </h2>

      
                  <div className="space-y-2 text-gray-700 text-sm lg:text-base">
                    {data[activeIndex].description.p1 && (
                      <p>{data[activeIndex].description.p1}</p>
                    )}
                    {data[activeIndex].description.p2 && (
                      <p>{data[activeIndex].description.p2}</p>
                    )}
                  </div>

                  {data[activeIndex].description.list && (
                    <ul className="list-disc pl-5 lg:pl-6 mt-2 text-gray-700 space-y-1 text-sm lg:text-base">
                      {data[activeIndex].description.list.map((li, index) => (
                        <li key={index}>{li}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </div> 
              {data[activeIndex].description.disclaimer && (
                <p className="text-gray-500 font-light italic text-xs lg:text-sm mt-4 leading-relaxed  text-justify lg:text-start">
                  {data[activeIndex].description.disclaimer}
                </p>
              )}
            </div>
          </main> */}
        </div>
      </div>
    </section>
  );
}
