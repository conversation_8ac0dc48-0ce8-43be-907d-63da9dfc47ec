import Banner from "@/components/ui/reusable/banner/Banner";
import React from "react";
import AboutWinshine from "./AboutWinshine";
import FranchiseesAdvantages from "./FranchiseesAdvantages";
import FranshiseesPointsSection from "./FranshiseesPointsSection";
import ServiceCard from "@/components/service/ServiceCard/ServiceCard";

const BusinessAssociateMaster = () => {
  return (
    <div>
      <Banner
        title="Business Opportunities - As A Business Associate"
        imageUrl="/images/business-oppotunity/business-associate2.jpg"
        subtitle=""
      />
      <AboutWinshine />
      <FranchiseesAdvantages />
      <FranshiseesPointsSection />
      <ServiceCard
        title={"Seize this transformative franchise opportunity!"}
        description="Explore the possibilities of being your own boss with Winshine Financial Services. Turn your passion for finance into a thriving business!"
        image={
          "/images/business-oppotunity/business-opportunity1.jpg"
        }
        imageRightSide={true}
      />
      <div className="bg-white">
        <div className="p-0 s_wrapper">
          <div className="p-6 border-2 border-dashed border-[#b33c337c] rounded-2xl bg-[#ff8f873a]">
            <p className="text-[#333] italic text-base  text-justify md:text-start">
              Disclaimer: This opportunity is subject to meeting eligibility criteria and other terms and conditions. Winshine Financial Services reserves the right to modify or withdraw this opportunity at its discretion. Winshine Financial Services is a registered trademark. Franchise opportunities are subject to terms and conditions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessAssociateMaster;
