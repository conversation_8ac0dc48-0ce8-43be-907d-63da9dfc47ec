{"name": "winshine_website", "version": "0.1.0", "private": true, "scripts": {"dev": "node -e \"process.stdout.write('\\x1Bc')\" && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-tabs": "^1.1.12", "@studio-freight/lenis": "^1.0.42", "@tabler/icons-react": "^3.31.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.7.4", "gsap": "^3.12.7", "highcharts": "^12.2.0", "lenis": "^1.2.3", "lucide-react": "^0.488.0", "motion": "^12.7.3", "next": "15.3.0", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.3", "recharts": "^2.15.3", "sass": "^1.87.0", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.2.0", "three": "^0.175.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.0", "npm-run-all": "^4.1.5", "tailwindcss": "^4"}}