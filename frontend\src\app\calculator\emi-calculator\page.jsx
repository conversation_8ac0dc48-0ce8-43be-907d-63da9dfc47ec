import EmiCalculatorMaster from "@/components/calculator/Emi-Calculator/EmiCalculatorMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
      <Head>
        <title>
          EMI Calculator | Estimate Your Loan Repayments Easily | Winshine
        </title>
        <meta
          name="description"
          content="Use Winshine’s free EMI Calculator to calculate your monthly loan payments. Plan better for home, personal, or car loans with accurate financial insights."
        />
        <meta
          name="keywords"
          content="EMI calculator, loan calculator, home loan EMI, personal loan EMI, car loan calculator, financial tools, Winshine EMI tool"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta
          property="og:title"
          content="Free EMI Calculator | Winshine Financial Services"
        />
        <meta
          property="og:description"
          content="Accurately calculate your loan EMIs with our free online tool. Empower your financial planning with Winshine."
        />
        <meta
          property="og:image"
          content="https://winshine.nipralo.com/images/calculator/emi-calculator-banner.png"
        />{" "}
        {/* Replace with actual image */}
        <meta
          property="og:url"
          content="https://winshine.nipralo.com/calculator/emi-calculator"
        />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/calculator/emi-calculator-banner.png" />
        <link
          rel="canonical"
          href="https://winshine.nipralo.com/calculator/emi-calculator"
        />
      </Head>
      <EmiCalculatorMaster />
    </>
  );
};

export default page;
