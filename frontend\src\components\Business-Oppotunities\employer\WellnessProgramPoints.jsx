import React from 'react'
import LeftRightPoints from '../BusinessAssociate/LeftRightPoints'

const WellnessProgramPoints = () => {
  return (
     <div className="bg-white ">
        <div className="!max-w-6xl mx-auto bg-white s_wrapper">
          <LeftRightPoints
            iconOnRight={false}
            icon={"/images/business-oppotunity/FinancialPrudenceWorkshops.png"}
            title={"Financial Prudence Workshops"}
            description={
              "Enhanced understanding of financial concepts. Learn to create and manage a budget effectively. Strategies for managing and reducing debt. Techniques for tracking and controlling expenses."
            }
          />
          <LeftRightPoints
            iconOnRight={true}
            icon={"/images/business-oppotunity/SavingsStrategySessions.png"}
            title={"Savings Strategy Sessions"}
            description={
              "Development of personalized savings plans. Importance and methods of building an emergency fund. Strategies for achieving long-term savings goals, such as retirement/education."
            }
          />
          <LeftRightPoints
            iconOnRight={false}
            icon={"/images/business-oppotunity/CompoundingPrinciples.png"}
            title={"Compounding Principles"}
            description={
              "Importance of early investments for long-term benefits. Understanding how compound interest works. Real-life examples illustrating the impact of compounding on wealth growth"
            }
          />
          <LeftRightPoints
            iconOnRight={true}
            icon={"/images/business-oppotunity/InflationAwarenessManagement.png"}
            title={"Inflation Awareness and Management"}
            description={
              "Understanding the causes and effects of inflation. Impact of inflation on purchasing power and savings. Strategies to mitigate the effects of inflation through informed financial planning and investments."
            }
          />
          <LeftRightPoints
            iconOnRight={false}
            icon={"/images/business-oppotunity/InvestmentEducationPrograms.png"}
            title={"Investment Education Programs"}
            description={
              "Understanding various types of investments (stocks, bonds, mutual funds, etc.). Learning about risk assessment and management. Fundamentals of building and diversifying an investment portfolio."
            }
          />
          <LeftRightPoints
            iconOnRight={true}
            icon={"/images/business-oppotunity/ComprehensiveInsuranceEducation.png"}
            title={"Comprehensive Insurance Education"}
            description={
              "Understanding different types of insurance (health, life, disability, etc.). Evaluating insurance needs and selecting appropriate coverage. Integrating insurance into overall financial planning."
            }
          />
          <LeftRightPoints
            iconOnRight={false}
            icon={"/images/business-oppotunity/RetirementPlanningWorkshops.png"}
            title={"Retirement Planning Workshops"}
            description={
              "Importance of early retirement planning and setting realistic goals. Understanding retirement-specific Investments. Strategies for building a robust retirement fund for long-term financial security."
            }
          />
        </div>
      </div>
  )
}

export default WellnessProgramPoints
