import BlogsMaster from "@/components/blogs/BlogsMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
      <Head>
        <title>
          Latest Insights | Financial Tips & Wealth Creation Ideas from Winshine
        </title>
        <meta
          name="description"
          content="Explore financial insights, investment strategies, tax planning tips, and more from Winshine Financial Services. Stay informed and take charge of your financial journey."
        />
        <meta
          name="keywords"
          content="financial blog, investment tips, savings advice, tax planning, wealth creation blog, Winshine insights, financial literacy, insurance planning"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta
          property="og:title"
          content="Latest Insights | Winshine Financial Services Blog"
        />
        <meta
          property="og:description"
          content="Stay updated with ethical financial advice, investment ideas, and planning tips from Winshine Financial Services."
        />
        <meta
          property="og:image"
          content="https://www.winshine.in/images/home/<USER>"
        />{" "}
        {/* Replace with your blog banner image URL */}
        <meta property="og:url" content="https://www.winshine.in/blogs" />
        <meta name="twitter:card" content="https://www.winshine.in/images/home/<USER>" />
        <link rel="canonical" href="https://www.winshine.in/blogs" />
      </Head>
      <BlogsMaster />
    </>
  );
};

export default page;
