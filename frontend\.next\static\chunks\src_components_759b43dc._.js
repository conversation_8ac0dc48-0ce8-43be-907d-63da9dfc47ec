(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/calculator/sip-calculator/circular-progress.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// "use client"
// import { useEffect, useRef } from "react"
// export function CircularProgress({ progress }) {
//   const fillRef = useRef(null)
//   useEffect(() => {
//     if (fillRef.current) {
//       const max = -219.99078369140625
//       const cappedProgress = progress > 100 ? 100 : progress
//       const dashOffset = ((100 - cappedProgress) / 100) * max
//       fillRef.current.style.strokeDashoffset = dashOffset.toString()
//     }
//   }, [progress])
//   return (
//     <div className="relative w-[245px] h-[215px]">
//       <svg className="progress" x="0px" y="0px" viewBox="0 0 80 80">
//         <path
//           className="track"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#ac272b"
//           strokeWidth="40"
//           style={{ transform: "rotate(90deg) translate(0px, -80px)" }}
//         />
//         <path
//           ref={fillRef}
//           className="fill"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#1B1E49"
//           strokeWidth="40"
//           style={{
//             transform: "rotate(90deg) translate(0px, -80px)",
//             strokeDasharray: "219.9907836914",
//             strokeDashoffset: "-219.9907836914",
//             transition: "stroke-dashoffset 1s",
//           }}
//         />
//       </svg>
//       <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full" />
//     </div>
//   )
// }
// export default CircularProgress
__turbopack_context__.s({
    "CircularProgress": (()=>CircularProgress),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
function CircularProgress({ progress }) {
    _s();
    const fillRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircularProgress.useEffect": ()=>{
            if (fillRef.current) {
                const max = 219.99078369140625;
                const cappedProgress = Math.min(progress, 100);
                const dashOffset = (100 - cappedProgress) / 100 * max;
                fillRef.current.style.strokeDashoffset = dashOffset.toString();
            }
        }
    }["CircularProgress.useEffect"], [
        progress
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative w-[245px] h-[245px] rounded-full overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "progress",
                x: "0px",
                y: "0px",
                viewBox: "0 0 80 80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0",
                        fill: "none",
                        stroke: "#ac272b",
                        strokeWidth: "42",
                        style: {
                            transform: "rotate(90deg) translate(0px, -80px)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                        lineNumber: 72,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        ref: fillRef,
                        d: "M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0",
                        fill: "none",
                        stroke: "#1B1E49",
                        strokeWidth: "40",
                        strokeLinecap: "butt",
                        style: {
                            transform: "rotate(90deg) translate(0px, -80px)",
                            strokeDasharray: "219.9907836914",
                            strokeDashoffset: "219.9907836914",
                            transition: "stroke-dashoffset 1s ease"
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full"
            }, void 0, false, {
                fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_s(CircularProgress, "/bCvsHLZppcYn+l67o1widLIpC4=");
_c = CircularProgress;
const __TURBOPACK__default__export__ = CircularProgress;
var _c;
__turbopack_context__.k.register(_c, "CircularProgress");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/reusable/banner/Banner.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
"use client";
;
const Banner = ({ imageUrl, title, subtitle })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: `relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: imageUrl,
                    alt: title,
                    className: "w-full h-full object-cover"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                    lineNumber: 10,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-[#000]/40 z-0"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 text-center px-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl md:text-5xl font-medium",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this),
                    subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4",
                        children: subtitle
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                        lineNumber: 24,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 21,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
};
_c = Banner;
const __TURBOPACK__default__export__ = Banner;
var _c;
__turbopack_context__.k.register(_c, "Banner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_759b43dc._.js.map