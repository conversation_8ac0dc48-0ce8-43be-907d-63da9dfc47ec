"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Marquee from "react-fast-marquee";

// Register GSAP ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

const ScrollingRows = () => {
  const firstRowRef = useRef(null);
  const secondRowRef = useRef(null);
  const wrapperRef = useRef(null);

  useEffect(() => {
    // Animation for the first row (moves left on scroll down)
    gsap.to(firstRowRef.current, {
      x: "-20%", // Adjust the value for desired scroll effect
      ease: "none",
      scrollTrigger: {
        trigger: wrapperRef.current,
        start: "top center",
        end: "bottom top",
        scrub: true,
      },
    });

    // Animation for the second row (moves right on scroll down)
    gsap.to(secondRowRef.current, {
      x: "20%", // Adjust the value for desired scroll effect
      ease: "none",
      scrollTrigger: {
        trigger: wrapperRef.current,
        start: "top center",
        end: "bottom top",
        scrub: true,
      },
    });

    // Cleanup on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <div
      // ref={wrapperRef}
      className="scrolling-rows-wrapper overflow-hidden lg:py-10 py-6 bg-white"
    >
      {/* First Row */}
      <Marquee speed={80} autoFill={true}>
        <div
          // ref={firstRowRef}
          className="first-row lg:text-[24px] text-base font-medium text-[#222] bg-white text-center py-5 flex justify-center"
        //   style={{ width: "220vw" }} // Centered
        >
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
            <img src="/images/insurance/Motor Insurance.png" alt="Motor Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Motor Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Travel Insurance.png" alt="Travel Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Travel Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Fire Insurance.png" alt="Fire Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Fire Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Theft Insurance.png" alt="Theft Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Theft Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Marine Insurance.png" alt="Marine Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Marine Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Workmen Insurance.png" alt="Workmen Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Workmen’s Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Group Insurance.png" alt="Group Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Group Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Society Insurance.png" alt="Society Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Society Insurance</h4>
          </div>
          <div className="flex justify-center border-l border-y py-2.5 px-5 md:py-5 md:px-10  items-center gap-4">
              <img src="/images/insurance/Asset Insurance.png" alt="Asset Insurance" className="w-12 h-12"/>
            <h4 className="text-nowrap">Asset Insurance</h4>
          </div>
        </div>
      </Marquee>
      {/* Spacer to provide separation between rows */}
      {/* <div className="spacer h-16"></div> */}

      {/* Second Row */}
      <Marquee speed={80} autoFill={true} direction="right">
      <div
        // ref={secondRowRef}
        className="second-row lg:text-[24px] text-base font-medium bg-white text-[#222] text-center py-5 flex justify-center"
        // style={{ width: "210vw", marginLeft: "-90vw" }} // Centered
      >
        <div className="flex justify-center border-l border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Asset Insurance.png" alt="Asset Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Asset Insurance</h4>
        </div>
        <div className="flex justify-center border-l  border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Society Insurance.png" alt="Society Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Society Insurance</h4>
        </div>
        <div className="flex justify-center border-l  border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Group Insurance.png" alt="Group Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Group Insurance</h4>
        </div>
        <div className="flex justify-center border-l  border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Workmen Insurance.png" alt="Workmen’s Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Workmen’s Insurance</h4>
        </div>
        <div className="flex justify-center border-l  border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Marine Insurance.png" alt="Marine Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Marine Insurance</h4>
        </div>
        <div className="flex justify-center border-l border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Theft Insurance.png" alt="Theft Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Theft Insurance</h4>
        </div>
        <div className="flex justify-center border-l border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Fire Insurance.png" alt="Fire Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Fire Insurance</h4>
        </div>
        <div className="flex justify-center border-l border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Travel Insurance.png" alt="Travel Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Travel Insurance</h4>
        </div>
        <div className="flex justify-center border-l border-y px-5 py-2.5 md:px-10 md:py-5 items-center gap-4">
          <img src="/images/insurance/Motor Insurance.png" alt="Motor Insurance" className="w-12 h-12"/>
          <h4 className="text-nowrap">Motor Insurance</h4>
        </div>
      </div>
      </Marquee>
    </div>
  ); 
};

export default ScrollingRows;
