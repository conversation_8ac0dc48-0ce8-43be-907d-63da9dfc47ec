import React from "react";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import { IoLocationOutline } from "react-icons/io5";
import { LuPhone } from "react-icons/lu";
import { MdOutlineMail } from "react-icons/md";
import Link from "next/link";
const Details = () => {
  return (
    <div className="text-[#040404] md:mt-5">
      <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-4 lg:mb-4">
        <SkewFadeInWords text="Get in Touch" />
      </h2>
      <div className="flex flex-col gap-2">
        <p className="mb-2 text-[20px] flex gap-5 xl:w-[70%]">
          <span>
            <IoLocationOutline className="bg-gradient text-white text-[36px] p-2 rounded-lg " />
          </span>{" "}
          <span className="">
            {" "}
            <a
              href="https://maps.app.goo.gl/2opqtYSoibK1dQZV7"
              target="_blank"
              rel="noopener noreferrer"
            >
              107, Manratna Business Park, Junction of Tilak Road, Derasar Ln,
              Ghatkopar East, Mumbai, Maharashtra - 400077, India
            </a>
          </span>
        </p>
        <p className="mb-2 text-[20px] flex gap-5 items-center">
          <span>
            <LuPhone className="bg-gradient text-white text-[36px] p-2 rounded-lg " />
          </span>{" "}
          <div>
            <Link href="tel:+************ ">
              <span className="mt-1">+91 9833135459 </span>
            </Link>{" "}
            <br />
            <Link href="tel:022 35939918 ">
              <span className="mt-1">022 35939918 </span>
            </Link>
          </div>
        </p>
        <p className="text-[20px] flex gap-5">
          <span>
            <MdOutlineMail className="bg-gradient text-white text-[36px] p-2 rounded-lg " />
          </span>
          <Link href="mailto:<EMAIL>">
            <span className="mt-1"><EMAIL></span>
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Details;
