{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nconst Banner = ({ imageUrl, title, subtitle }) => {\r\n  return (\r\n    <section className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}>\r\n      {/* Parallax Fixed Background Image - Only for Banner */}\r\n      <div\r\n        className=\"fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0\"\r\n      >\r\n        <img src={imageUrl} alt={title} className=\"w-full h-full object-cover\"/>\r\n      </div>\r\n      {/* <div\r\n        className=\"fixed top-0 left-0 w-full h-full !bg-cover !bg-center -z-10 pointer-events-none\"\r\n        style={backgroundStyles}\r\n      /> */}\r\n\r\n      {/* Dark Overlay */}\r\n      <div className=\"absolute inset-0 bg-[#000]/40 z-0\" />\r\n\r\n      {/* Banner Content */}\r\n      <div className=\"relative z-10 text-center px-4\">\r\n        <h1 className=\"text-3xl md:text-5xl font-medium\">{title}</h1>\r\n        {subtitle && (\r\n          <p className=\"text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Banner;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,qBACE,6LAAC;QAAQ,WAAW,CAAC,0GAA0G,CAAC;;0BAE9H,6LAAC;gBACC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,KAAK;oBAAU,KAAK;oBAAO,WAAU;;;;;;;;;;;0BAQ5C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/animation/SkewFadeInWords.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef } from \"react\";\r\nimport { motion, useAnimation, useInView } from \"framer-motion\";\r\n\r\nconst containerVariants = {\r\n  hidden: {},\r\n  visible: {\r\n    transition: {\r\n      staggerChildren: 0.12,\r\n    },\r\n  },\r\n};\r\n\r\nconst wordVariants = {\r\n  hidden: {\r\n    opacity: 0,\r\n    y: 40,\r\n    skewY: 12,\r\n  },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    skewY: 0,\r\n    transition: {\r\n      duration: 2,\r\n      ease: [0.22, 1, 0.36, 1],\r\n    },\r\n  },\r\n};\r\n\r\nconst SkewFadeInWords = ({ text, className = \"\" }) => {\r\n  const controls = useAnimation();\r\n  const ref = useRef(null);\r\n  const isInView = useInView(ref, { once: true, margin: \"-50px\" });\r\n\r\n  useEffect(() => {\r\n    if (isInView) controls.start(\"visible\");\r\n  }, [isInView,controls]);\r\n\r\n  // Split lines by <br> or <br/>\r\n  const lines = text.split(/<br\\s*\\/?>/i).filter(Boolean);\r\n\r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      initial=\"hidden\"\r\n      animate={controls}\r\n      variants={containerVariants}\r\n      className={`overflow-hidden flex flex-col gap-y-2 ${className}`}\r\n    >\r\n      {lines.map((line, lineIndex) => {\r\n        const words = line.trim().split(\" \");\r\n        return (\r\n          <div key={lineIndex} className=\"\">\r\n            {words.map((word, wordIndex) => (\r\n              <motion.span\r\n                key={wordIndex}\r\n                variants={wordVariants}\r\n                className=\"inline-block mr-2 whitespace-nowrap pb-2\"\r\n                dangerouslySetInnerHTML={{ __html: word }}\r\n              />\r\n            ))}\r\n          </div>\r\n        );\r\n      })}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default SkewFadeInWords;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,oBAAoB;IACxB,QAAQ,CAAC;IACT,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QACN,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;QAC1B;IACF;AACF;AAEA,MAAM,kBAAkB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE;;IAC/C,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAQ;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU,SAAS,KAAK,CAAC;QAC/B;oCAAG;QAAC;QAAS;KAAS;IAEtB,+BAA+B;IAC/B,MAAM,QAAQ,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC;IAE/C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW,CAAC,sCAAsC,EAAE,WAAW;kBAE9D,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;YAChC,qBACE,6LAAC;gBAAoB,WAAU;0BAC5B,MAAM,GAAG,CAAC,CAAC,MAAM,0BAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBAEV,UAAU;wBACV,WAAU;wBACV,yBAAyB;4BAAE,QAAQ;wBAAK;uBAHnC;;;;;eAHD;;;;;QAWd;;;;;;AAGN;GArCM;;QACa,4LAAA,CAAA,eAAY;QAEZ,gLAAA,CAAA,YAAS;;;KAHtB;uCAuCS", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/lib/FormValidation.js"], "sourcesContent": ["export const handleContactValidtion = (fields) => {\r\n  let errors = {};\r\n\r\n  // Name\r\n  if (!fields.name?.trim()) {\r\n    errors.name = \"Please enter your name\";\r\n  } else if (!/^[A-Za-z]+(?: [A-Za-z]+)*$/.test(fields.name)) {\r\n    errors.name = \"Name can only contain letters\";\r\n  }\r\n  \r\n  // Phone\r\n  if (!fields.phone?.trim()) {\r\n    errors.phone = \"Please enter your phone number\";\r\n  } else if (!/^\\d{10}$/.test(fields.phone)) {\r\n    if (/[a-zA-Z]/.test(fields.phone)) {\r\n      errors.phone = \"Phone number should not contain letters\";\r\n    } else {\r\n      errors.phone = \"Phone number must be exactly 10 digits\";\r\n    }\r\n  }\r\n\r\n  //  Email\r\n  if (!fields.email?.trim()) {\r\n    errors.email = \"Please enter your email\";\r\n  } else {\r\n    const emailRegex =\r\n      /^[a-zA-Z][a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n    if (!emailRegex.test(fields.email)) {\r\n      errors.email = \"Invalid email format\";\r\n    }\r\n  }\r\n\r\n if (fields.message && fields.message.length > 500) {\r\n    errors.message = \"Message cannot exceed 500 characters\";\r\n  }\r\n\r\n  return errors;\r\n};\r\n\r\n\r\n\r\nexport const processFormData = (fields) => {\r\n  if (fields.cv) {\r\n    const formData = new FormData();\r\n    const jsonPayload = {\r\n      name: fields.name,\r\n      phone: fields.phone,\r\n      email: fields.email,\r\n      purpose: fields.purpose,\r\n      message: fields.messages || fields.message || \"\", // handles both\r\n    };\r\n    formData.append(\"data\", JSON.stringify(jsonPayload));\r\n    formData.append(\"files.cv\", fields.cv);\r\n    return formData;\r\n  }\r\n\r\n  return JSON.stringify({\r\n    data: {\r\n      name: fields.name,\r\n      phone: fields.phone,\r\n      email: fields.email,\r\n      purpose: fields.purpose,\r\n      message: fields.message || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const validateFile = (file) => {\r\n  if (!file?.type) return false;\r\n\r\n  const allowedFormats = [\r\n    \"application/pdf\",\r\n    \"application/msword\",\r\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\r\n  ];\r\n\r\n  return file.size <= 1048576 && allowedFormats.includes(file.type);\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,yBAAyB,CAAC;IACrC,IAAI,SAAS,CAAC;IAEd,OAAO;IACP,IAAI,CAAC,OAAO,IAAI,EAAE,QAAQ;QACxB,OAAO,IAAI,GAAG;IAChB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,OAAO,IAAI,GAAG;QAC1D,OAAO,IAAI,GAAG;IAChB;IAEA,QAAQ;IACR,IAAI,CAAC,OAAO,KAAK,EAAE,QAAQ;QACzB,OAAO,KAAK,GAAG;IACjB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,KAAK,GAAG;QACzC,IAAI,WAAW,IAAI,CAAC,OAAO,KAAK,GAAG;YACjC,OAAO,KAAK,GAAG;QACjB,OAAO;YACL,OAAO,KAAK,GAAG;QACjB;IACF;IAEA,SAAS;IACT,IAAI,CAAC,OAAO,KAAK,EAAE,QAAQ;QACzB,OAAO,KAAK,GAAG;IACjB,OAAO;QACL,MAAM,aACJ;QACF,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,KAAK,GAAG;YAClC,OAAO,KAAK,GAAG;QACjB;IACF;IAED,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,KAAK;QAChD,OAAO,OAAO,GAAG;IACnB;IAEA,OAAO;AACT;AAIO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,EAAE,EAAE;QACb,MAAM,WAAW,IAAI;QACrB,MAAM,cAAc;YAClB,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,SAAS,OAAO,OAAO;YACvB,SAAS,OAAO,QAAQ,IAAI,OAAO,OAAO,IAAI;QAChD;QACA,SAAS,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC;QACvC,SAAS,MAAM,CAAC,YAAY,OAAO,EAAE;QACrC,OAAO;IACT;IAEA,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM;YACJ,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,SAAS,OAAO,OAAO;YACvB,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,MAAM,MAAM,OAAO;IAExB,MAAM,iBAAiB;QACrB;QACA;QACA;KACD;IAED,OAAO,KAAK,IAAI,IAAI,WAAW,eAAe,QAAQ,CAAC,KAAK,IAAI;AAClE", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/Form.jsx"], "sourcesContent": ["// app/components/ContactForm.js or wherever you want\r\n\"use client\";\r\n\r\nimport { handleContactValidtion } from \"@/lib/FormValidation\";\r\nimport React, { useState } from \"react\";\r\nimport { Toaster, toast } from \"react-hot-toast\";\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL;\r\nconst API_TOKEN = process.env.NEXT_PUBLIC_API_TOKEN;\r\n\r\nconst Form = () => {\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    phone: \"\",\r\n    email: \"\",\r\n    subject: \"\",\r\n    message: \"\",\r\n  });\r\n  const [errors, setErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n\r\n  const validateField = (name, value) => {\r\n    const fieldData = { ...formData, [name]: value };\r\n\r\n    const allErrors = handleContactValidtion(fieldData);\r\n    setErrors((prev) => ({\r\n      ...prev,\r\n      [name]: allErrors[name] || \"\", // Update only this field's error\r\n    }));\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, files } = e.target;\r\n    const updatedValue = type === \"file\" ? files[0] : value;\r\n\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: updatedValue,\r\n    }));\r\n\r\n    validateField(name, updatedValue); // Real-time validation for only this field\r\n  };\r\n\r\n  const validate = () => {\r\n    const validationErrors = handleContactValidtion(formData);\r\n    setErrors(validationErrors);\r\n    return Object.keys(validationErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setErrors({});\r\n\r\n    if (!validate()) {\r\n      console.log(\"❌ Validation failed\");\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    \r\n    try {\r\n      const response = await fetch(`${API_URL}/api/contact-enquiries`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          Authorization: `Bearer ${API_TOKEN}`,\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          data: {\r\n            name: formData.name,\r\n            email: formData.email,\r\n            phone: formData.phone,\r\n            subject: formData.subject,\r\n            message: formData.message,\r\n          },\r\n        }),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (response.ok) {\r\n        setIsSubmitted(true);\r\n        toast.success(\"Form submitted successfully!\");\r\n        setTimeout(() => {\r\n          setIsSubmitted(false);\r\n          setFormData({\r\n            name: \"\",\r\n            email: \"\",\r\n            phone: \"\",\r\n            subject: \"\",\r\n            message: \"\",\r\n          });\r\n        }, 4000);\r\n      } else {\r\n        toast.error(\r\n          `Failed to send message: ${result.error?.message || \"Unknown error\"}`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"❌ Error submitting form:\", error);\r\n      toast.error(\"An error occurred. Please try again later.\");\r\n    }\r\n    setIsSubmitting(false);\r\n  };\r\n\r\n  return (\r\n    <form\r\n      onSubmit={handleSubmit}\r\n      className=\"md:p-4 rounded-md space-y-4 text-[#040404] w-full max-w-[700px]\"\r\n    >\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Name</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleChange}\r\n            className=\"w-full border border-gray-300 rounded px-3 py-2\"\r\n          />\r\n          {errors.name && (\r\n            <p className=\"text-sm text-red-500 mt-1\">{errors.name}</p>\r\n          )}\r\n        </div>\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Phone</label>\r\n          <input\r\n            type=\"tel\"\r\n            name=\"phone\"\r\n            value={formData.phone}\r\n            onChange={handleChange}\r\n            className=\"w-full border border-gray-300 rounded px-3 py-2\"\r\n          />\r\n          {errors.phone && (\r\n            <p className=\"text-sm text-red-500 mt-1\">{errors.phone}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Email</label>\r\n          <input\r\n            type=\"email\"\r\n            name=\"email\"\r\n            value={formData.email}\r\n            onChange={handleChange}\r\n            className=\"w-full border border-gray-300 rounded px-3 py-2\"\r\n          />\r\n          {errors.email && (\r\n            <p className=\"text-sm text-red-500 mt-1\">{errors.email}</p>\r\n          )}\r\n        </div>\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Subject</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"subject\"\r\n            value={formData.subject}\r\n            onChange={handleChange}\r\n            className=\"w-full border border-gray-300 rounded px-3 py-2\"\r\n          />\r\n          {errors.subject && (\r\n            <p className=\"text-sm text-red-500 mt-1\">{errors.subject}</p>\r\n          )}\r\n        </div>\r\n        {/* <div>\r\n          <label className=\"block font-medium mb-1\">Purpose</label>\r\n          <select\r\n            name=\"purpose\"\r\n            value={formData.purpose}\r\n            onChange={handleChange}\r\n            className=\"w-full border border-gray-300 rounded px-3 py-[11px]\"\r\n          >\r\n            <option value=\"\">Select Purpose</option>\r\n            <option value=\"Nominate\">Nominate a Brand</option>\r\n            <option value=\"Survey\">Participate in Survey</option>\r\n            <option value=\"Recognition\">Apply for Recognition</option>\r\n            <option value=\"Other\">Other</option>\r\n          </select>\r\n          {errors.purpose && (\r\n            <p className=\"text-sm text-red-500 mt-1\">{errors.purpose}</p>\r\n          )}\r\n        </div> */}\r\n      </div>\r\n      <div>\r\n        <label className=\"block font-medium mb-1\">Message</label>\r\n        <textarea\r\n          name=\"message\"\r\n          rows={4}\r\n          value={formData.message}\r\n          onChange={handleChange}\r\n          className=\"w-full border border-gray-300 rounded px-3 py-2 resize-none\"\r\n        />\r\n        {errors.message && (\r\n          <p className=\"text-sm text-red-500 mt-1\">{errors.message}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"text-right\">\r\n        <button\r\n          type=\"submit\"\r\n          className=\"gradient-button px-4 font-semibold py-2 rounded-xl !ml-0 text-white\"\r\n        >\r\n          {isSubmitting ? \"Sending...\" : \"Send\"}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default Form;\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AAMrC;;AAHhB;AACA;AACA;;;AAJA;;;;AAKA,MAAM;AACN,MAAM;AAEN,MAAM,OAAO;;IACX,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,CAAC,MAAM;QAC3B,MAAM,YAAY;YAAE,GAAG,QAAQ;YAAE,CAAC,KAAK,EAAE;QAAM;QAE/C,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE;QACzC,UAAU,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI;YAC7B,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAC7C,MAAM,eAAe,SAAS,SAAS,KAAK,CAAC,EAAE,GAAG;QAElD,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,cAAc,MAAM,eAAe,2CAA2C;IAChF;IAEA,MAAM,WAAW;QACf,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE;QAChD,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAClD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,UAAU,CAAC;QAEX,IAAI,CAAC,YAAY;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,sBAAsB,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,WAAW;oBACpC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;wBACJ,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;wBACzB,SAAS,SAAS,OAAO;oBAC3B;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;oBACT,eAAe;oBACf,YAAY;wBACV,MAAM;wBACN,OAAO;wBACP,OAAO;wBACP,SAAS;wBACT,SAAS;oBACX;gBACF,GAAG;YACL,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,wBAAwB,EAAE,OAAO,KAAK,EAAE,WAAW,iBAAiB;YAEzE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;QACA,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QACC,UAAU;QACV,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAyB;;;;;;0CAC1C,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU;gCACV,WAAU;;;;;;4BAEX,OAAO,IAAI,kBACV,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI;;;;;;;;;;;;kCAGzD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAyB;;;;;;0CAC1C,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAU;;;;;;4BAEX,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;0BAI5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAyB;;;;;;0CAC1C,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAU;;;;;;4BAEX,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAG1D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAyB;;;;;;0CAC1C,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,OAAO;gCACvB,UAAU;gCACV,WAAU;;;;;;4BAEX,OAAO,OAAO,kBACb,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;0BAsB9D,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAAyB;;;;;;kCAC1C,6LAAC;wBACC,MAAK;wBACL,MAAM;wBACN,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,WAAU;;;;;;oBAEX,OAAO,OAAO,kBACb,6LAAC;wBAAE,WAAU;kCAA6B,OAAO,OAAO;;;;;;;;;;;;0BAG5D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,WAAU;8BAET,eAAe,eAAe;;;;;;;;;;;;;;;;;AAKzC;GAtMM;KAAA;uCAwMS", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/framer-motion/dist/es/animation/hooks/animation-controls.mjs"], "sourcesContent": ["import { invariant } from 'motion-utils';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\n\nfunction stopAnimation(visualElement) {\n    visualElement.values.forEach((value) => value.stop());\n}\nfunction setVariants(visualElement, variantLabels) {\n    const reversedLabels = [...variantLabels].reverse();\n    reversedLabels.forEach((key) => {\n        const variant = visualElement.getVariant(key);\n        variant && setTarget(visualElement, variant);\n        if (visualElement.variantChildren) {\n            visualElement.variantChildren.forEach((child) => {\n                setVariants(child, variantLabels);\n            });\n        }\n    });\n}\nfunction setValues(visualElement, definition) {\n    if (Array.isArray(definition)) {\n        return setVariants(visualElement, definition);\n    }\n    else if (typeof definition === \"string\") {\n        return setVariants(visualElement, [definition]);\n    }\n    else {\n        setTarget(visualElement, definition);\n    }\n}\n/**\n * @public\n */\nfunction animationControls() {\n    /**\n     * Track whether the host component has mounted.\n     */\n    let hasMounted = false;\n    /**\n     * A collection of linked component animation controls.\n     */\n    const subscribers = new Set();\n    const controls = {\n        subscribe(visualElement) {\n            subscribers.add(visualElement);\n            return () => void subscribers.delete(visualElement);\n        },\n        start(definition, transitionOverride) {\n            invariant(hasMounted, \"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            const animations = [];\n            subscribers.forEach((visualElement) => {\n                animations.push(animateVisualElement(visualElement, definition, {\n                    transitionOverride,\n                }));\n            });\n            return Promise.all(animations);\n        },\n        set(definition) {\n            invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            return subscribers.forEach((visualElement) => {\n                setValues(visualElement, definition);\n            });\n        },\n        stop() {\n            subscribers.forEach((visualElement) => {\n                stopAnimation(visualElement);\n            });\n        },\n        mount() {\n            hasMounted = true;\n            return () => {\n                hasMounted = false;\n                controls.stop();\n            };\n        },\n    };\n    return controls;\n}\n\nexport { animationControls, setValues };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,SAAS,cAAc,aAAa;IAChC,cAAc,MAAM,CAAC,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;AACtD;AACA,SAAS,YAAY,aAAa,EAAE,aAAa;IAC7C,MAAM,iBAAiB;WAAI;KAAc,CAAC,OAAO;IACjD,eAAe,OAAO,CAAC,CAAC;QACpB,MAAM,UAAU,cAAc,UAAU,CAAC;QACzC,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;QACpC,IAAI,cAAc,eAAe,EAAE;YAC/B,cAAc,eAAe,CAAC,OAAO,CAAC,CAAC;gBACnC,YAAY,OAAO;YACvB;QACJ;IACJ;AACJ;AACA,SAAS,UAAU,aAAa,EAAE,UAAU;IACxC,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,OAAO,YAAY,eAAe;IACtC,OACK,IAAI,OAAO,eAAe,UAAU;QACrC,OAAO,YAAY,eAAe;YAAC;SAAW;IAClD,OACK;QACD,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IAC7B;AACJ;AACA;;CAEC,GACD,SAAS;IACL;;KAEC,GACD,IAAI,aAAa;IACjB;;KAEC,GACD,MAAM,cAAc,IAAI;IACxB,MAAM,WAAW;QACb,WAAU,aAAa;YACnB,YAAY,GAAG,CAAC;YAChB,OAAO,IAAM,KAAK,YAAY,MAAM,CAAC;QACzC;QACA,OAAM,UAAU,EAAE,kBAAkB;YAChC,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,YAAY;YACtB,MAAM,aAAa,EAAE;YACrB,YAAY,OAAO,CAAC,CAAC;gBACjB,WAAW,IAAI,CAAC,CAAA,GAAA,kMAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,YAAY;oBAC5D;gBACJ;YACJ;YACA,OAAO,QAAQ,GAAG,CAAC;QACvB;QACA,KAAI,UAAU;YACV,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,YAAY;YACtB,OAAO,YAAY,OAAO,CAAC,CAAC;gBACxB,UAAU,eAAe;YAC7B;QACJ;QACA;YACI,YAAY,OAAO,CAAC,CAAC;gBACjB,cAAc;YAClB;QACJ;QACA;YACI,aAAa;YACb,OAAO;gBACH,aAAa;gBACb,SAAS,IAAI;YACjB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/framer-motion/dist/es/animation/hooks/use-animation.mjs"], "sourcesContent": ["import { animationControls } from './animation-controls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\n/**\n * Creates `AnimationControls`, which can be used to manually start, stop\n * and sequence animations on one or more components.\n *\n * The returned `AnimationControls` should be passed to the `animate` property\n * of the components you want to animate.\n *\n * These components can then be animated with the `start` method.\n *\n * ```jsx\n * import * as React from 'react'\n * import { motion, useAnimation } from 'framer-motion'\n *\n * export function MyComponent(props) {\n *    const controls = useAnimation()\n *\n *    controls.start({\n *        x: 100,\n *        transition: { duration: 0.5 },\n *    })\n *\n *    return <motion.div animate={controls} />\n * }\n * ```\n *\n * @returns Animation controller with `start` and `stop` methods\n *\n * @public\n */\nfunction useAnimationControls() {\n    const controls = useConstant(animationControls);\n    useIsomorphicLayoutEffect(controls.mount, []);\n    return controls;\n}\nconst useAnimation = useAnimationControls;\n\nexport { useAnimation, useAnimationControls };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS;IACL,MAAM,WAAW,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,iMAAA,CAAA,oBAAiB;IAC9C,CAAA,GAAA,0LAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS,KAAK,EAAE,EAAE;IAC5C,OAAO;AACX;AACA,MAAM,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs"], "sourcesContent": ["import { resolveElements } from 'motion-dom';\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexport { inView };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa;IACf,MAAM;IACN,KAAK;AACT;AACA,SAAS,OAAO,iBAAiB,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,UAAU,EAAE,SAAS,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1F,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,sBAAsB,IAAI;IAChC,MAAM,uBAAuB,CAAC;QAC1B,QAAQ,OAAO,CAAC,CAAC;YACb,MAAM,QAAQ,oBAAoB,GAAG,CAAC,MAAM,MAAM;YAClD;;;aAGC,GACD,IAAI,MAAM,cAAc,KAAK,QAAQ,QACjC;YACJ,IAAI,MAAM,cAAc,EAAE;gBACtB,MAAM,WAAW,QAAQ,MAAM,MAAM,EAAE;gBACvC,IAAI,OAAO,aAAa,YAAY;oBAChC,oBAAoB,GAAG,CAAC,MAAM,MAAM,EAAE;gBAC1C,OACK;oBACD,SAAS,SAAS,CAAC,MAAM,MAAM;gBACnC;YACJ,OACK,IAAI,OAAO,UAAU,YAAY;gBAClC,MAAM;gBACN,oBAAoB,MAAM,CAAC,MAAM,MAAM;YAC3C;QACJ;IACJ;IACA,MAAM,WAAW,IAAI,qBAAqB,sBAAsB;QAC5D;QACA;QACA,WAAW,OAAO,WAAW,WAAW,SAAS,UAAU,CAAC,OAAO;IACvE;IACA,SAAS,OAAO,CAAC,CAAC,UAAY,SAAS,OAAO,CAAC;IAC/C,OAAO,IAAM,SAAS,UAAU;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/framer-motion/dist/es/utils/use-in-view.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\n\nfunction useInView(ref, { root, margin, amount, once = false, initial = false, } = {}) {\n    const [isInView, setInView] = useState(initial);\n    useEffect(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return inView(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\nexport { useInView };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,UAAU,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,KAAK,EAAE,UAAU,KAAK,EAAG,GAAG,CAAC,CAAC;IACjF,MAAM,CAAC,UAAU,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,CAAC,IAAI,OAAO,IAAK,QAAQ,UACzB;YACJ,MAAM;+CAAU;oBACZ,UAAU;oBACV,OAAO,OAAO;uDAAY,IAAM,UAAU;;gBAC9C;;YACA,MAAM,UAAU;gBACZ,MAAM,AAAC,QAAQ,KAAK,OAAO,IAAK;gBAChC;gBACA;YACJ;YACA,OAAO,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,OAAO,EAAE,SAAS;QACxC;8BAAG;QAAC;QAAM;QAAK;QAAQ;QAAM;KAAO;IACpC,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}