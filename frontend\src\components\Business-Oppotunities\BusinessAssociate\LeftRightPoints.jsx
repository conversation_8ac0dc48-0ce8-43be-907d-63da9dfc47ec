import Image from 'next/image'
import React from 'react'

const LeftRightPoints = ({iconOnRight = false, title , description,icon}) => {
  return (
    <div className={`w-full flex flex-col gap-4 items-center max-w-[90%] mx-auto lg:max-w-[100%] md:py-8 py-4 !bg-white ${iconOnRight? "md:flex-row-reverse" : "md:flex-row"}`}>
      <div className='w-32 h-32 min-w-32 min-h-32 md:w-40 md:h-40 md:min-w-40 md:min-h-40 shadow-lg rounded-2xl bg-[#ff917065] flex justify-center items-center md:p-8'>
        <Image src={icon} alt={title || description} width={120} height={120} className='md:min-w-[90px] md:min-h-[90px] min-h-[60px] min-w-[60px] md:w-[90px] md:h-[90px] h-[60px] w-[60px]'/>
      </div>
      <div className='text-black md:flex-1 md:px-8 flex flex-col justify-center text-center md:text-start'>
        <h3 className='font-semibold md:text-start text-center mb-2'>{title}</h3>
        <p className='text-[#333]  text-justify md:text-start'>{description}</p>
      </div>
    </div>
  )
}

export default LeftRightPoints
