{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nconst Banner = ({ imageUrl, title, subtitle }) => {\r\n  return (\r\n    <section className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}>\r\n      {/* Parallax Fixed Background Image - Only for Banner */}\r\n      <div\r\n        className=\"fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0\"\r\n      >\r\n        <img src={imageUrl} alt={title} className=\"w-full h-full object-cover\"/>\r\n      </div>\r\n      {/* <div\r\n        className=\"fixed top-0 left-0 w-full h-full !bg-cover !bg-center -z-10 pointer-events-none\"\r\n        style={backgroundStyles}\r\n      /> */}\r\n\r\n      {/* Dark Overlay */}\r\n      <div className=\"absolute inset-0 bg-[#000]/40 z-0\" />\r\n\r\n      {/* Banner Content */}\r\n      <div className=\"relative z-10 text-center px-4\">\r\n        <h1 className=\"text-3xl md:text-5xl font-medium\">{title}</h1>\r\n        {subtitle && (\r\n          <p className=\"text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Banner;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,qBACE,6LAAC;QAAQ,WAAW,CAAC,0GAA0G,CAAC;;0BAE9H,6LAAC;gBACC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,KAAK;oBAAU,KAAK;oBAAO,WAAU;;;;;;;;;;;0BAQ5C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;KA5BM;uCA8BS", "debugId": null}}]}