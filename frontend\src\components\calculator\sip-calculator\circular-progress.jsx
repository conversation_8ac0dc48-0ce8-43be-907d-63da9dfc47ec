// "use client"

// import { useEffect, useRef } from "react"



// export function CircularProgress({ progress }) {
//   const fillRef = useRef(null)

//   useEffect(() => {
//     if (fillRef.current) {
//       const max = -219.99078369140625
//       const cappedProgress = progress > 100 ? 100 : progress
//       const dashOffset = ((100 - cappedProgress) / 100) * max
//       fillRef.current.style.strokeDashoffset = dashOffset.toString()
//     }
//   }, [progress])

//   return (
//     <div className="relative w-[245px] h-[215px]">
//       <svg className="progress" x="0px" y="0px" viewBox="0 0 80 80">
//         <path
//           className="track"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#ac272b"
//           strokeWidth="40"
//           style={{ transform: "rotate(90deg) translate(0px, -80px)" }}
//         />
//         <path
//           ref={fillRef}
//           className="fill"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#1B1E49"
//           strokeWidth="40"
//           style={{
//             transform: "rotate(90deg) translate(0px, -80px)",
//             strokeDasharray: "219.9907836914",
//             strokeDashoffset: "-219.9907836914",
//             transition: "stroke-dashoffset 1s",
//           }}
//         />
//       </svg>
//       <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full" />
//     </div>
//   )
// }

// export default CircularProgress

"use client"

import { useEffect, useRef } from "react"

export function CircularProgress({ progress }) {
  const fillRef = useRef(null)

  useEffect(() => {
    if (fillRef.current) {
      const max = 219.99078369140625
      const cappedProgress = Math.min(progress, 100)
      const dashOffset = ((100 - cappedProgress) / 100) * max
      fillRef.current.style.strokeDashoffset = dashOffset.toString()
    }
  }, [progress])

  return (
    <div className="relative w-[245px] h-[245px] rounded-full overflow-hidden">
      <svg className="progress" x="0px" y="0px" viewBox="0 0 80 80">
        {/* Red Track */}
        <path
          d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
          fill="none"
          stroke="#ac272b"
          strokeWidth="42"
          style={{ transform: "rotate(90deg) translate(0px, -80px)" }}
        />
        {/* Blue Fill */}
        <path
          ref={fillRef}
          d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
          fill="none"
          stroke="#1B1E49"
          strokeWidth="40"
          strokeLinecap="butt"
          style={{
            transform: "rotate(90deg) translate(0px, -80px)",
            strokeDasharray: "219.9907836914",
            strokeDashoffset: "219.9907836914",
            transition: "stroke-dashoffset 1s ease",
          }}
        />
      </svg>
      {/* Inner white circle */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full" />
    </div>
  )
}

export default CircularProgress
