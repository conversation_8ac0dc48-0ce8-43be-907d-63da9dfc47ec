"use client";

import React, { useState, useEffect, useRef } from "react";
import { CircularProgress } from "./circular-progress";

export default function SipCalculator() {
  // State for form inputs
  const [investmentType, setInvestmentType] = useState("know-investment-amount");
  const [investmentMode, setInvestmentMode] = useState("sip");
  const [targetAmount, setTargetAmount] = useState("1500000");
  const [duration, setDuration] = useState("10");
  const [rateOfReturn, setRateOfReturn] = useState("12");

  // State for calculation results
  const [investedAmount, setInvestedAmount] = useState(0);
  const [returns, setReturns] = useState(0);
  const [totalWealth, setTotalWealth] = useState(0);
  const [monthlyInvestment, setMonthlyInvestment] = useState(0);
  const [graphProgress, setGraphProgress] = useState(0);

  // State for errors
  const [errors, setErrors] = useState({
    targetAmount: "",
    duration: "",
    rateOfReturn: "",
    general: "",
  });

  // ROI array for slider
  const roiArr = useRef([]);

  // Initialize ROI array
  useEffect(() => {
    const tempRoiArr = [];
    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {
      tempRoiArr.push(Number.parseFloat(i).toFixed(2));
    }
    roiArr.current = tempRoiArr;
  }, []);

  // Format number with commas
  const numWithCommas = (num) => {
    return num.toLocaleString("en-IN");
  };

  // Remove commas from number string
  const removeCommas = (number) => {
    return number.toString().replace(/,/g, "");
  };

  // Validate input range
  const validateRangeInput = (value, max, min, field) => {
    const numValue = Number(value);
    if (isNaN(numValue) || numValue === "") {
      setErrors((prev) => ({
        ...prev,
        [field]: "Please enter a valid number",
      }));
      return false;
    }

    if (numValue < min) {
      setErrors((prev) => ({ ...prev, [field]: `Minimum value is ${min}` }));
      return false;
    }

    if (numValue > max) {
      setErrors((prev) => ({ ...prev, [field]: `Maximum value is ${max}` }));
      return false;
    }

    setErrors((prev) => ({ ...prev, [field]: "" }));
    return true;
  };
  // PMT calculation function - calculates payment amount for annuity
  // Standard financial formula: PMT = (FV * r) / (((1 + r)^n - 1) * (1 + r * type))
  const PMT = (rate, nper, pv, fv, type = 0) => {
    if (!fv) fv = 0;
    if (!pv) pv = 0;

    if (rate === 0) {
      return -(fv + pv) / nper;
    }

    const term = Math.pow(1 + rate, nper);
    const numerator = rate * (fv + pv * term);
    const denominator = (term - 1) * (1 + rate * type);

    return -numerator / denominator;
  };

  // Future value calculation for annuity (SIP)
  // Standard formula: FV = PMT * (((1 + r)^n - 1) / r) * (1 + r * type) + PV * (1 + r)^n
  const futureValue = (rate, nper, pmt, pv = 0, type = 0) => {
    if (rate === 0) {
      return -(pv + pmt * nper);
    }

    const term = Math.pow(1 + rate, nper);
    const annuityFV = pmt * ((term - 1) / rate) * (1 + rate * type);
    const presentValueFV = pv * term;

    return -(annuityFV + presentValueFV);
  };

  // Present value calculation
  // Standard formula: PV = (FV / (1 + r)^n) - PMT * (((1 + r)^n - 1) / (r * (1 + r)^n)) * (1 + r * type)
  const presentValue = (rate, nper, pmt, fv, type = 0) => {
    if (!fv) fv = 0;
    if (!pmt) pmt = 0;

    if (rate === 0) {
      return -(fv + pmt * nper);
    }

    const term = Math.pow(1 + rate, nper);
    const futureValuePV = fv / term;
    const annuityPV = pmt * ((term - 1) / (rate * term)) * (1 + rate * type);

    return -(futureValuePV + annuityPV);
  };

  // Calculate results based on inputs
  const calculateResults = () => {
    // Validate inputs
    const targetAmtValid = validateRangeInput(
      removeCommas(targetAmount),
      "10000000000",
      "1",
      "targetAmount"
    );
    const durationValid = validateRangeInput(duration, "50", "1", "duration");
    const roiValid = validateRangeInput(
      rateOfReturn,
      "100",
      "0",
      "rateOfReturn"
    );

    if (!targetAmtValid || !durationValid || !roiValid) {
      setErrors((prev) => ({
        ...prev,
        general:
          "Please enter numeric inputs within the suggested range to get accurate results",
      }));
      return;
    }

    setErrors((prev) => ({ ...prev, general: "" }));

    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));
    const annualRate = Number.parseFloat(rateOfReturn) / 100;
    const years = Number.parseInt(duration);

    if (investmentType === "know-investment-amount") {
      // Calculate future value from known investment amount
      if (investmentMode === "sip") {
        // Monthly SIP calculation
        const monthlyRate = annualRate / 12;
        const months = years * 12;

        let fv;
        if (monthlyRate === 0) {
          // No interest case
          fv = targetAmtValue * months;
        } else {
          // Future value of SIP using standard formula
          // FV = PMT * (((1 + r)^n - 1) / r) * (1 + r) [for beginning of period]
          fv =
            targetAmtValue *
            (((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
              (1 + monthlyRate));
        }

        const totalInvested = targetAmtValue * months;
        const returns = fv - totalInvested;

        setTotalWealth(Math.round(fv));
        setInvestedAmount(Math.round(totalInvested));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(targetAmtValue));
        setGraphProgress(fv > 0 ? (returns / fv) * 100 : 0);
      } else if (investmentMode === "quarterly") {
        // Quarterly investment calculation
        const quarterlyRate = annualRate / 4;
        const quarters = years * 4;

        let fv;
        if (quarterlyRate === 0) {
          // No interest case
          fv = targetAmtValue * quarters;
        } else {
          fv =
            targetAmtValue *
            (((Math.pow(1 + quarterlyRate, quarters) - 1) / quarterlyRate) *
              (1 + quarterlyRate));
        }

        const totalInvested = targetAmtValue * quarters;
        const returns = fv - totalInvested;

        setTotalWealth(Math.round(fv));
        setInvestedAmount(Math.round(totalInvested));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(targetAmtValue));
        setGraphProgress(fv > 0 ? (returns / fv) * 100 : 0);
      } else {
        // Lumpsum calculation
        let fv;
        if (annualRate === 0) {
          // No interest case
          fv = targetAmtValue;
        } else {
          fv = targetAmtValue * Math.pow(1 + annualRate, years);
        }

        const returns = fv - targetAmtValue;

        setTotalWealth(Math.round(fv));
        setInvestedAmount(Math.round(targetAmtValue));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(targetAmtValue));
        setGraphProgress(fv > 0 ? (returns / fv) * 100 : 0);
      }
    } else if (investmentType === "know-target-amount") {
      // Calculate required investment to reach target amount
      if (investmentMode === "sip") {
        // Required monthly SIP calculation
        const monthlyRate = annualRate / 12;
        const months = years * 12;

        let requiredSIP;
        if (monthlyRate === 0) {
          // No interest case
          requiredSIP = targetAmtValue / months;
        } else {
          // PMT = FV / (((1 + r)^n - 1) / r) * (1 + r) [for beginning of period]
          requiredSIP =
            targetAmtValue /
            (((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
              (1 + monthlyRate));
        }

        const totalInvested = requiredSIP * months;
        const returns = targetAmtValue - totalInvested;

        setTotalWealth(Math.round(targetAmtValue));
        setInvestedAmount(Math.round(totalInvested));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(requiredSIP));
        setGraphProgress(
          targetAmtValue > 0 ? (returns / targetAmtValue) * 100 : 0
        );
      } else if (investmentMode === "quarterly") {
        // Required quarterly investment calculation
        const quarterlyRate = annualRate / 4;
        const quarters = years * 4;

        let requiredQuarterly;
        if (quarterlyRate === 0) {
          // No interest case
          requiredQuarterly = targetAmtValue / quarters;
        } else {
          requiredQuarterly =
            targetAmtValue /
            (((Math.pow(1 + quarterlyRate, quarters) - 1) / quarterlyRate) *
              (1 + quarterlyRate));
        }

        const totalInvested = requiredQuarterly * quarters;
        const returns = targetAmtValue - totalInvested;

        setTotalWealth(Math.round(targetAmtValue));
        setInvestedAmount(Math.round(totalInvested));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(requiredQuarterly));
        setGraphProgress(
          targetAmtValue > 0 ? (returns / targetAmtValue) * 100 : 0
        );
      } else {
        // Required lumpsum calculation
        let requiredLumpsum;
        if (annualRate === 0) {
          // No interest case - target amount equals required lumpsum
          requiredLumpsum = targetAmtValue;
        } else {
          requiredLumpsum = targetAmtValue / Math.pow(1 + annualRate, years);
        }

        const returns = targetAmtValue - requiredLumpsum;

        setTotalWealth(Math.round(targetAmtValue));
        setInvestedAmount(Math.round(requiredLumpsum));
        setReturns(Math.round(returns));
        setMonthlyInvestment(Math.round(requiredLumpsum));
        setGraphProgress(
          targetAmtValue > 0 ? (returns / targetAmtValue) * 100 : 0
        );
      }
    }
  };

  // Handle input changes
  const handleInputChange = (e, setter) => {
    const { value } = e.target;

    if (e.target.type === "text") {
      // For text inputs, update the value directly
      setter(value);
    } else if (e.target.type === "range") {
      // For range inputs, update the corresponding text input
      if (e.target.id === "ill_int_rates_value") {
        // Handle ROI slider specially
        const roiValue = roiArr.current[Number.parseInt(value)];
        setter(roiValue);
      } else {
        setter(value);
      }
    }
  };

  // Format target amount with commas
  const formatTargetAmount = (value) => {
    const numValue = Number.parseFloat(removeCommas(value));
    if (isNaN(numValue)) return "0";
    return numWithCommas(numValue);
  };

  // Calculate on input change
  useEffect(() => {
    calculateResults();
  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);

  // Get ROI slider value
  const getRoiSliderValue = () => {
    const index = roiArr.current.indexOf(rateOfReturn);
    return index >= 0 ? index : roiArr.current.indexOf("12.00");
  };

  // Get text for result display
  const getResultText = () => {
    let preText = "";
    let postText = "";

    if (investmentType === "know-target-amount") {
      if (investmentMode === "sip") {
        preText = "Invest";
        postText = "every month to reach your target amount";
      } else if (investmentMode === "quarterly") {
        preText = "Invest";
        postText = "every quarter to reach your target amount";
      } else {
        preText = "Make a one-time investment of";
        postText = "to reach your target amount";
      }
    } else {
      if (investmentMode === "sip") {
        preText = "Your monthly investment of";
        postText = "will grow to the amount shown";
      } else if (investmentMode === "quarterly") {
        preText = "Your quarterly investment of";
        postText = "will grow to the amount shown";
      } else {
        preText = "Your one-time investment of";
        postText = "will grow to the amount shown";
      }
    }

    return { preText, postText };
  };

  const { preText, postText } = getResultText();

  return (
    <section className="bg-[#fff] text-black py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-[#2e4765] mb-6 text-center">
          SIP Calculator: Systematic Investment Plan Calculator Online
        </h1>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Left side - Inputs */}
            <div className="w-full lg:w-1/2 p-6">
              {/* Investment Type Toggle */}
              {/* <div className="mb-6">
                <label className="block text-gray-700 text-lg font-semibold mb-3">
                  What do you want to calculate?
                </label>
                <div className="flex gap-3">
                  <button
                    className={`px-4 py-2 rounded-lg cursor-pointer text-sm ${
                      investmentType === "know-target-amount"
                        ? "bg-[#ac272b] text-white"
                        : "bg-gray-100"
                    }`}
                    onClick={() => setInvestmentType("know-target-amount")}
                  >
                    Required Investment
                  </button>
                  <button
                    className={`px-4 py-2 rounded-lg cursor-pointer text-sm ${
                      investmentType === "know-investment-amount"
                        ? "bg-[#ac272b] text-white"
                        : "bg-gray-100"
                    }`}
                    onClick={() => setInvestmentType("know-investment-amount")}
                  >
                    Future Value
                  </button>
                </div>
              </div> */}

              {/* Investment Mode Tabs */}
              <div className="flex mb-6 gap-3">
                <button
                  className={`px-4 py-2 rounded-lg cursor-pointer ${
                    investmentMode === "sip"
                      ? "bg-[#1b1e49] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => setInvestmentMode("sip")}
                >
                  SIP
                </button>
                <button
                  className={`px-4 py-2 rounded-lg cursor-pointer ${
                    investmentMode === "quarterly"
                      ? "bg-[#1b1e49] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => setInvestmentMode("quarterly")}
                >
                  Quarterly
                </button>
                <button
                  className={`px-4 py-2 rounded-lg cursor-pointer ${
                    investmentMode === "lumpsum"
                      ? "bg-[#1b1e49] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => setInvestmentMode("lumpsum")}
                >
                  Lumpsum
                </button>
              </div>

              {/* Amount Input */}
              <div className="mb-6">
                <div className="flex justify-between items-center w-full">
                  <label className="block text-gray-700 text-xl">
                    {investmentType === "know-target-amount"
                      ? "Target Amount"
                      : investmentMode === "sip"
                      ? "Monthly Investment"
                      : investmentMode === "quarterly"
                      ? "Quarterly Investment"
                      : "Investment Amount"}
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2.5 text-gray-500">
                      ₹
                    </span>
                    <input
                      type="text"
                      className="w-full max-w-28 p-2 pl-8 border border-gray-300 rounded-md"
                      value={formatTargetAmount(targetAmount)}
                      onChange={(e) => handleInputChange(e, setTargetAmount)}
                    />
                  </div>
                </div>
                {errors.targetAmount && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.targetAmount}
                  </p>
                )}

                <div className="mt-2">
                  <input
                    type="range"
                    min="500"
                    max="1000000"
                    step="500"
                    value={removeCommas(targetAmount)}
                    onChange={(e) => handleInputChange(e, setTargetAmount)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>₹ 500</span>
                    <span>₹ 10,00,000</span>
                  </div>
                </div>
              </div>

              {/* Expected Return Rate */}
              <div className="mb-6">
                <div className="flex justify-between items-center w-full">
                  <label className="block text-gray-700 mb-2">
                    Expected Return Rate (p.a)
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      className="w-full max-w-16 py-2 px-4 border border-gray-300 rounded-md "
                      value={rateOfReturn}
                      onChange={(e) => handleInputChange(e, setRateOfReturn)}
                    />
                    <span className="absolute right-3 top-2 text-gray-500">
                      %
                    </span>
                  </div>
                </div>
                {errors.rateOfReturn && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.rateOfReturn}
                  </p>
                )}

                <div className="mt-2">
                  <input
                    type="range"
                    min="5"
                    max="30"
                    step="0.5"
                    value={rateOfReturn}
                    onChange={(e) => handleInputChange(e, setRateOfReturn)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>5%</span>
                    <span>30%</span>
                  </div>
                </div>
              </div>

              {/* Investment Time Period */}
              <div className="mb-6">
                <div className="flex justify-between items-center w-full">
                  <label className="block text-gray-700 mb-2">
                    Investment time period
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      className="w-full max-w-24 py-2 px-4 border border-gray-300 rounded-md "
                      value={duration}
                      onChange={(e) => handleInputChange(e, setDuration)}
                    />
                    <span className="absolute right-3 top-2.5 text-gray-500">
                      Years
                    </span>
                  </div>
                </div>
                {errors.duration && (
                  <p className="text-red-500 text-xs mt-1">{errors.duration}</p>
                )}

                <div className="mt-2">
                  <input
                    type="range"
                    min="1"
                    max="40"
                    step="1"
                    value={duration}
                    onChange={(e) => handleInputChange(e, setDuration)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0 years</span>
                    <span>40 years</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Results */}
            <div className="w-full lg:w-1/2 p-6 bg-gray-50">
              <div className="flex flex-col justify-center items-center mb-8">
                <div className="w-full h-full relative mb-4 flex justify-center">
                  <CircularProgress progress={graphProgress} />
                </div>

                <div className="text-center mt-3">
                  <p className="text-gray-600 mb-1">
                    {investmentMode === "sip"
                      ? "SIP per month"
                      : investmentMode === "quarterly"
                      ? "Investment per quarter"
                      : "Investment amount"}
                  </p>
                  <p className="text-2xl font-bold">
                    ₹ {numWithCommas(monthlyInvestment)}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#f47321] mr-3"></div>
                  <div>
                    <p className="text-gray-600 text-sm">Invested amount</p>
                    <p className="font-semibold">
                      ₹ {numWithCommas(investedAmount)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#ae2f33] mr-3"></div>
                  <div>
                    <p className="text-gray-600 text-sm">Est. returns</p>
                    <p className="font-semibold">₹ {numWithCommas(returns)}</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-4 bg-gray-100 rounded-lg">
                <div className="flex justify-between items-center">
                  <p className="text-gray-600">Total amount:</p>
                  <p className="text-2xl font-bold text-[#2e4765]">
                    ₹ {numWithCommas(totalWealth)}
                  </p>
                </div>
              </div>

              {errors.general && (
                <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {errors.general}
                </div>
              )}

              <div className="mt-6 text-center text-sm text-gray-600">
                <p>
                  {preText}{" "}
                  <span className="font-semibold">
                    ₹ {numWithCommas(monthlyInvestment)}
                  </span>{" "}
                  {postText}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information Section */}
        {/* <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-[#2e4765] mb-4">
            What is SIP Calculator?
          </h2>
          <div className="prose max-w-none">
            <p className="text-gray-700 mb-4">
              A SIP (Systematic Investment Plan) calculator is a financial tool
              that helps you estimate the future value of your mutual fund
              investments made through SIP. It calculates the potential returns
              based on your monthly investment amount, investment duration, and
              expected rate of return.
            </p>

            <h3 className="text-xl font-semibold text-[#2e4765] mb-3">
              How does SIP Calculator work?
            </h3>
            <p className="text-gray-700 mb-4">
              The SIP calculator uses the compound interest formula to calculate
              the maturity amount. It considers the power of compounding, where
              your returns also earn returns over time. The calculator takes
              into account:
            </p>
            <ul className="list-disc list-inside text-gray-700 mb-4 space-y-2">
              <li>Monthly investment amount</li>
              <li>Investment duration (in years)</li>
              <li>Expected annual rate of return</li>
              <li>Frequency of investment (monthly, quarterly, or lumpsum)</li>
            </ul>

            <h3 className="text-xl font-semibold text-[#2e4765] mb-3">
              Benefits of using SIP Calculator
            </h3>
            <ul className="list-disc list-inside text-gray-700 mb-4 space-y-2">
              <li>Plan your financial goals effectively</li>
              <li>Understand the power of compounding</li>
              <li>Compare different investment scenarios</li>
              <li>Make informed investment decisions</li>
              <li>Track your wealth creation journey</li>
            </ul>

            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mt-6">
              <p className="text-blue-800">
                <strong>Disclaimer:</strong> The calculations provided by this
                SIP calculator are for illustrative purposes only. Actual
                returns may vary based on market conditions and fund
                performance. Please consult with a financial advisor before
                making investment decisions.
              </p>
            </div>
          </div>
        </div> */}
      </div>
    </section>
  );
}
