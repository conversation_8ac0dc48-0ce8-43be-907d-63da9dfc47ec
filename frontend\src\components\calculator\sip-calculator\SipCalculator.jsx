import React, { useState, useEffect, useRef } from 'react';
import { CircularProgress } from './circular-progress';

export default function SipCalculator() {
  // State for form inputs
  const [investmentType, setInvestmentType] = useState("know-target-amount");
  const [investmentMode, setInvestmentMode] = useState("sip");
  const [targetAmount, setTargetAmount] = useState("1500000");
  const [duration, setDuration] = useState("10");
  const [rateOfReturn, setRateOfReturn] = useState("12");

  // State for calculation results
  const [investedAmount, setInvestedAmount] = useState(0);
  const [returns, setReturns] = useState(0);
  const [totalWealth, setTotalWealth] = useState(0);
  const [monthlyInvestment, setMonthlyInvestment] = useState(0);
  const [graphProgress, setGraphProgress] = useState(0);

  // State for errors
  const [errors, setErrors] = useState({
    targetAmount: "",
    duration: "",
    rateOfReturn: "",
    general: "",
  });

  // ROI array for slider
  const roiArr = useRef([]);

  // Initialize ROI array
  useEffect(() => {
    const tempRoiArr = [];
    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {
      tempRoiArr.push(Number.parseFloat(i).toFixed(2));
    }
    roiArr.current = tempRoiArr;
  }, []);

  // Format number with commas
  const numWithCommas = (num) => {
    return num.toLocaleString("en-IN");
  };

  // Remove commas from number string
  const removeCommas = (number) => {
    return number.toString().replace(/,/g, "");
  };

  // Validate input range
  const validateRangeInput = (value, max, min, field) => {
    const numValue = Number.parseFloat(value);
    if (isNaN(numValue)) {
      setErrors((prev) => ({
        ...prev,
        [field]: "Please enter a valid number",
      }));
      return false;
    }

    if (numValue < Number.parseFloat(min)) {
      setErrors((prev) => ({
        ...prev,
        [field]: `Value should be at least ${min}`,
      }));
      return false;
    }

    if (numValue > Number.parseFloat(max)) {
      setErrors((prev) => ({
        ...prev,
        [field]: `Value should not exceed ${max}`,
      }));
      return false;
    }

    setErrors((prev) => ({ ...prev, [field]: "" }));
    return true;
  };

  // PMT calculation function
  const PMT = (rate, nper, pv, fv, type) => {
    if (!fv) fv = 0;
    if (!type) type = 0;

    if (rate === 0) return -(pv + fv) / nper;

    const pvif = Math.pow(1 + rate, nper);
    let pmt = (rate / (pvif - 1)) * -(pv * pvif + fv);

    if (type === 1) pmt = pmt / (1 + rate);

    return pmt;
  };

  // Future value calculation
  const futureValue = (rate, nper, pmt, pv, type) => {
    if (!pv) pv = 0;
    if (!type) type = 0;

    const pow = Math.pow(1 + rate, nper);
    let fv;

    if (rate) {
      fv = (pmt * (1 + rate * type) * (pow - 1)) / rate + pv * pow;
    } else {
      fv = -1 * (pv + pmt * nper);
    }

    return fv;
  };

  // Present value calculation
  const presentValue = (rate, nper, pmt, fv, type) => {
    if (!fv) fv = 0;
    if (!type) type = 0;

    const pow = Math.pow(1 + rate, nper);
    let pv;

    if (rate) {
      pv = (-pmt * (1 + rate * type) * (pow - 1)) / (rate * pow) - fv / pow;
    } else {
      pv = -1 * (fv + pmt * nper);
    }

    return pv;
  };

  // Calculate results based on inputs
  const calculateResults = () => {
    // Validate inputs
    const targetAmtValid = validateRangeInput(
      removeCommas(targetAmount),
      "1000000000",
      "1",
      "targetAmount"
    );
    const durationValid = validateRangeInput(duration, "50", "1", "duration");
    const roiValid = validateRangeInput(
      rateOfReturn,
      "100",
      "1",
      "rateOfReturn"
    );

    if (!targetAmtValid || !durationValid || !roiValid) {
      setErrors((prev) => ({
        ...prev,
        general:
          "Please enter numeric inputs within the suggested range to get accurate results",
      }));
      return;
    }

    setErrors((prev) => ({ ...prev, general: "" }));

    let amtValue = 0;
    let investVal = 0;
    let profit = 0;
    let profitPercent = 0;
    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));
    const roi = Number.parseFloat(rateOfReturn) / (100 * 12);
    const timePeriods = Number.parseInt(duration) * 12;

    if (investmentType === "know-investment-amount") {
      if (investmentMode === "sip") {
        amtValue = futureValue(roi, timePeriods, -1 * targetAmtValue, 0, 1);
        investVal = targetAmtValue * timePeriods;
      } else if (investmentMode === "quarterly") {
        const intervals = Number.parseInt(duration) * 4;
        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);
        amtValue =
          (targetAmtValue * (Math.pow(1 + quarterlyRoi, intervals) - 1)) /
          quarterlyRoi;
        investVal = targetAmtValue * intervals;
      } else {
        // lumpsum
        amtValue =
          targetAmtValue *
          Math.pow(
            1 + Number.parseFloat(rateOfReturn) / 100,
            Number.parseInt(duration)
          );
        investVal = targetAmtValue;
      }

      profit = Math.round(amtValue) - Math.round(investVal);
      profitPercent = Math.round((profit / Math.round(investVal)) * 100);
      setGraphProgress((Math.round(profit) / Math.round(amtValue)) * 100);

      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));
      setTotalWealth(Math.round(amtValue));
      setMonthlyInvestment(targetAmtValue);
    } else if (investmentType === "know-target-amount") {
      if (investmentMode === "sip") {
        amtValue = PMT(
          Number.parseFloat(rateOfReturn) / (100 * 12),
          Number.parseInt(duration) * 12,
          0,
          -1 * targetAmtValue,
          1
        );
        investVal = amtValue * Number.parseInt(duration) * 12;
      } else if (investmentMode === "quarterly") {
        const intervals = Number.parseInt(duration) * 4;
        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);
        amtValue =
          targetAmtValue /
          ((Math.pow(1 + quarterlyRoi, intervals) - 1) / quarterlyRoi);
        investVal = amtValue * intervals;
      } else {
        // lumpsum
        amtValue = presentValue(
          Number.parseFloat(rateOfReturn) / 100,
          Number.parseInt(duration),
          0,
          -1 * targetAmtValue
        );
        investVal = amtValue;
      }

      profit = Math.round(targetAmtValue) - Math.round(investVal);
      profitPercent = Math.round((profit / Math.round(investVal)) * 100);
      setGraphProgress((Math.round(profit) / Math.round(targetAmtValue)) * 100);

      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));
      setMonthlyInvestment(Math.round(amtValue > 1 ? amtValue : 0));
    }

    setReturns(Math.round(amtValue < 1 ? profit - 1 : profit));
    setTotalWealth(targetAmtValue);
  };

  // Handle input changes
  const handleInputChange = (e, setter) => {
    const { value } = e.target;

    if (e.target.type === "text") {
      // For text inputs, update the value directly
      setter(value);
    } else if (e.target.type === "range") {
      // For range inputs, update the corresponding text input
      if (e.target.id === "ill_int_rates_value") {
        // Handle ROI slider specially
        const roiValue = roiArr.current[Number.parseInt(value)];
        setter(roiValue);
      } else {
        setter(value);
      }
    }
  };

  // Format target amount with commas
  const formatTargetAmount = (value) => {
    const numValue = Number.parseFloat(removeCommas(value));
    if (isNaN(numValue)) return "0";
    return numWithCommas(numValue);
  };

  // Calculate on input change
  useEffect(() => {
    calculateResults();
  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);

  // Get ROI slider value
  const getRoiSliderValue = () => {
    const index = roiArr.current.indexOf(rateOfReturn);
    return index >= 0 ? index : roiArr.current.indexOf("12.00");
  };

  // Get text for result display
  const getResultText = () => {
    let preText = "";
    let postText = "";

    if (investmentType === "know-target-amount") {
      if (investmentMode === "sip") {
        preText = "Invest";
        postText = "every month to reach your target amount";
      } else if (investmentMode === "quarterly") {
        preText = "Invest";
        postText = "every quarter to reach your target amount";
      } else {
        preText = "Make a one-time investment of";
        postText = "to reach your target amount";
      }
    } else {
      if (investmentMode === "sip") {
        preText = "Your monthly investment of";
        postText = "will grow to the amount shown";
      } else if (investmentMode === "quarterly") {
        preText = "Your quarterly investment of";
        postText = "will grow to the amount shown";
      } else {
        preText = "Your one-time investment of";
        postText = "will grow to the amount shown";
      }
    }

    return { preText, postText };
  };

  const { preText, postText } = getResultText();

  return (
    <section className="bg-[#fff] py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-[#2e4765] mb-6">
          SIP Calculator: Systematic Investment Plan Calculator Online
        </h1>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Left side - Inputs */}
            <div className="w-full lg:w-1/2 p-6">
              {/* Investment Mode Tabs */}
              <div className="flex mb-6">
                <button
                  className={`px-4 py-2 rounded-l-md ${
                    investmentMode === "sip"
                      ? "bg-[#f47321] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => setInvestmentMode("sip")}
                >
                  SIP
                </button>
                <button
                  className={`px-4 py-2 ${
                    investmentMode === "lumpsum"
                      ? "bg-[#f47321] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => setInvestmentMode("lumpsum")}
                >
                  Lumpsum
                </button>
              </div>

              {/* Amount Input */}
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">Amount</label>
                <div className="relative">
                  <span className="absolute left-3 top-3 text-gray-500">₹</span>
                  <input
                    type="text"
                    className="w-full p-2 pl-8 border rounded-md"
                    value={formatTargetAmount(targetAmount)}
                    onChange={(e) =>
                      handleInputChange(e, setTargetAmount)
                    }
                  />
                </div>
                {errors.targetAmount && (
                  <p className="text-red-500 text-xs mt-1">{errors.targetAmount}</p>
                )}
                
                <div className="mt-2">
                  <input
                    type="range"
                    min="500"
                    max="2000000"
                    step="500"
                    value={removeCommas(targetAmount)}
                    onChange={(e) => handleInputChange(e, setTargetAmount)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>₹ 500</span>
                    <span>₹ 2,00,000</span>
                  </div>
                </div>
              </div>

              {/* Expected Return Rate */}
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">
                  Expected Return Rate (p.a)
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={rateOfReturn}
                    onChange={(e) => handleInputChange(e, setRateOfReturn)}
                  />
                  <span className="absolute right-3 top-3 text-gray-500">%</span>
                </div>
                {errors.rateOfReturn && (
                  <p className="text-red-500 text-xs mt-1">{errors.rateOfReturn}</p>
                )}
                
                <div className="mt-2">
                  <input
                    type="range"
                    min="5"
                    max="30"
                    step="0.5"
                    value={rateOfReturn}
                    onChange={(e) => handleInputChange(e, setRateOfReturn)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>5%</span>
                    <span>30%</span>
                  </div>
                </div>
              </div>

              {/* SIP Time Period */}
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">
                  SIP time period
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={duration}
                    onChange={(e) => handleInputChange(e, setDuration)}
                  />
                  <span className="absolute right-3 top-3 text-gray-500">Years</span>
                </div>
                {errors.duration && (
                  <p className="text-red-500 text-xs mt-1">{errors.duration}</p>
                )}
                
                <div className="mt-2">
                  <input
                    type="range"
                    min="1"
                    max="40"
                    step="1"
                    value={duration}
                    onChange={(e) => handleInputChange(e, setDuration)}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0 years</span>
                    <span>40 years</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Results */}
            <div className="w-full lg:w-1/2 p-6 bg-gray-50">
              <div className="flex flex-col items-center mb-8">
                <div className="w-48 h-48 relative mb-4">
                  <CircularProgress progress={graphProgress} />
                </div>
                
                <div className="text-center">
                  <p className="text-gray-600 mb-1">
                    {investmentMode === "sip" ? "SIP per month" : "Investment amount"}
                  </p>
                  <p className="text-2xl font-bold">
                    ₹ {numWithCommas(monthlyInvestment)}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#f47321] mr-3"></div>
                  <div>
                    <p className="text-gray-600 text-sm">Invested amount</p>
                    <p className="font-semibold">₹ {numWithCommas(investedAmount)}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#ae2f33] mr-3"></div>
                  <div>
                    <p className="text-gray-600 text-sm">Est. returns</p>
                    <p className="font-semibold">₹ {numWithCommas(returns)}</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-4 bg-gray-100 rounded-lg">
                <div className="flex justify-between items-center">
                  <p className="text-gray-600">Total amount:</p>
                  <p className="text-2xl font-bold text-[#2e4765]">
                    ₹ {numWithCommas
