"use client";
import React, { useEffect, useState } from 'react'
import Banner from '../ui/reusable/banner/Banner'
import LifeInsurance from './LifeInsurance'
import HealthInsurance from './HealthInsurance'
import ScrollingRows from './ScrollingRows'
import ScrollingRowsMobile from './ScrollingRowsMobile'

const InsuranceMaster = () => {
    const [scrolled, setScrolled] = useState(false);
    const handleScroll = () => {
      setScrolled(window.scrollY > 150);
    };
    useEffect(() => {
      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
    }, []);
  return (
    <div>
      {/* <Banner
        title="Insurance"
        imageUrl="/images/insurance/insurance-banner.webp"
        subtitle=""
      /> */}
       <section
        className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}
      >
        {/* Parallax Fixed Background Image - Only for Banner */}
        <div
          className={`fixed top-0 left-0 w-full h-full -z-10 pointer-events-none  ${
            scrolled ? "pt-[60px]" : "pt-[80px]"
          }`}
        >
          <img
            src="/images/insurance/insurance-scaled Banner.jpg"
            alt="Traditional Investments"
            className="w-full max-w-screen h-[40vh] md:h-[50vh] object-cover"
          />
        </div>
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-[#000]/20 z-0" />

        {/* Banner Content */}
        <div className="relative z-10 text-center px-4">
          <h1 className="text-3xl md:text-5xl font-medium">
            Traditional Investments
          </h1>
        </div>
      </section>
      <LifeInsurance/>
      <HealthInsurance/>
      <section>
        <div className=''>
          <ScrollingRows />
        </div>
        {/* <div className='block md:hidden'>
          <ScrollingRowsMobile />
        </div> */}
      </section>
    </div>
  )
}

export default InsuranceMaster
