"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "./components/Tabs";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "./components/Charts";
import { Card } from "./components/Card";
import { Input } from "./components/Input";
import { Button } from "./components/Button";
import Banner from "@/components/ui/reusable/banner/Banner";
import { ChevronDown, ChevronUp } from "lucide-react";

export default function EmiCalculatorMaster() {
  // State for loan parameters
  const [loanType, setLoanType] = useState("home");
  const [loanAmount, setLoanAmount] = useState(2500000);
  const [interestRate, setInterestRate] = useState(10.5);
  const [loanTenure, setLoanTenure] = useState(20);
  const [tenureType, setTenureType] = useState("year");
  const [startDate, setStartDate] = useState(new Date());
  const [yearFormat, setYearFormat] = useState("calendar");

  // State for calculated values
  const [emi, setEmi] = useState(0);
  const [totalInterest, setTotalInterest] = useState(0);
  const [totalPayment, setTotalPayment] = useState(0);
  const [amortizationSchedule, setAmortizationSchedule] = useState([]);
  // State for expanded rows
  const [expandedRows, setExpandedRows] = useState({});

  // Toggle row expansion
  const toggleRowExpansion = (year) => {
    setExpandedRows((prev) => ({
      ...prev,
      [year]: !prev[year],
    }));
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-IN", {
      style: "decimal",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate EMI and related values
  useEffect(() => {
    // Convert tenure to months if in years
    const tenureInMonths = tenureType === "year" ? loanTenure * 12 : loanTenure;

    // Monthly interest rate
    const monthlyRate = interestRate / 12 / 100;

    // Calculate EMI
    const emiValue =
      (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, tenureInMonths)) /
      (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    setEmi(Math.round(emiValue));

    // Calculate total payment and interest
    const totalPaymentValue = emiValue * tenureInMonths;
    const totalInterestValue = totalPaymentValue - loanAmount;

    setTotalPayment(Math.round(totalPaymentValue));
    setTotalInterest(Math.round(totalInterestValue));

    // Generate amortization schedule
    generateAmortizationSchedule(emiValue, monthlyRate, tenureInMonths);
  }, [loanAmount, interestRate, loanTenure, tenureType]);

  // Generate amortization schedule
  const generateAmortizationSchedule = (
    emiValue,
    monthlyRate,
    tenureInMonths
  ) => {
    let balance = loanAmount;
    let totalInterestPaid = 0;
    const yearlyData = {};
    const currentDate = new Date(startDate);

    for (let i = 1; i <= tenureInMonths; i++) {
      const interestForMonth = balance * monthlyRate;
      const principalForMonth = emiValue - interestForMonth;

      balance -= principalForMonth;
      totalInterestPaid += interestForMonth;

      const year = currentDate.getFullYear();

      if (!yearlyData[year]) {
        yearlyData[year] = {
          principal: 0,
          interest: 0,
          balance: balance > 0 ? balance : 0,
          totalPayment: 0,
          paidPercentage: ((loanAmount - balance) / loanAmount) * 100,
        };
      }

      yearlyData[year].principal += principalForMonth;
      yearlyData[year].interest += interestForMonth;
      yearlyData[year].totalPayment += emiValue;
      yearlyData[year].balance = balance > 0 ? balance : 0;
      yearlyData[year].paidPercentage =
        ((loanAmount - balance) / loanAmount) * 100;

      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Convert to array and sort by year
    const schedule = Object.keys(yearlyData).map((year) => ({
      year,
      ...yearlyData[year],
      principal: Math.round(yearlyData[year].principal),
      interest: Math.round(yearlyData[year].interest),
      totalPayment: Math.round(yearlyData[year].totalPayment),
      balance: Math.round(yearlyData[year].balance),
      paidPercentage: Math.round(yearlyData[year].paidPercentage * 100) / 100,
    }));

    setAmortizationSchedule(schedule);
  };

  // Handle loan type change
  const handleLoanTypeChange = (value) => {
    setLoanType(value);

    // Set default values based on loan type
    switch (value) {
      case "home":
        setLoanAmount(2500000);
        setInterestRate(10.5);
        setLoanTenure(20);
        break;
      case "personal":
        setLoanAmount(500000);
        setInterestRate(14);
        setLoanTenure(5);
        break;
      case "car":
        setLoanAmount(1000000);
        setInterestRate(12);
        setLoanTenure(7);
        break;
    }
  };

  // Get loan type label
  const getLoanTypeLabel = () => {
    switch (loanType) {
      case "home":
        return "Home";
      case "personal":
        return "Personal";
      case "car":
        return "Car";
      default:
        return "Home";
    }
  };

  // Format date for display
  const formatDate = (date) => {
    const month = date.toLocaleString("default", { month: "long" });
    const year = date.getFullYear();
    return `${month} ${year}`;
  };

  // Prepare chart data
  const pieChartData = [
    { name: "Principal", value: loanAmount, color: "#1e2a5a" },
    { name: "Interest", value: totalInterest, color: "#b22222" },
  ];

  const barChartData = amortizationSchedule.map((item) => ({
    year: item.year,
    principal: item.principal,
    interest: item.interest,
    balance: item.balance,
  }));

  return (
    <div>
      <Banner
        title="EMI Calculator"
      imageUrl="/images/calculator/emi-calculator-banner.png"
        subtitle=""
      />
      <section className="bg-white">
        <div className="s_wrapper !w-[100%]">
          <div className="max-w-5xl mx-auto p-4 bg-white rounded-lg shadow">
            <div className="md:mb-6 mb-2">
              <Tabs value={loanType} onValueChange={handleLoanTypeChange}>
                <TabsList className="grid w-full grid-cols-3 max-w-md">
                  <TabsTrigger value="home">Home Loan</TabsTrigger>
                  <TabsTrigger value="personal">Personal Loan</TabsTrigger>
                  <TabsTrigger value="car">Car Loan</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 md:gap-6 gap-4">
              <div className="lg:col-span-2">
                <Card className="md:p-6 p-4">
                  <div className="space-y-6">
                    {/* Loan Amount */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="text-sm font-medium">
                          {getLoanTypeLabel()} Loan Amount
                        </label>
                        <div className="flex items-center">
                          <Input
                            type="text"
                            value={formatCurrency(loanAmount)}
                            onChange={(e) => {
                              const value = e.target.value.replace(
                                /[^\d]/g,
                                ""
                              );
                              setLoanAmount(value ? Number.parseInt(value) : 0);
                            }}
                            className="w-32 text-right"
                          />
                          <span className="ml-2">₹</span>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="range"
                          min="0"
                          max="20000000"
                          step="100000"
                          value={loanAmount}
                          onChange={(e) =>
                            setLoanAmount(Number.parseInt(e.target.value))
                          }
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>0</span>
                          <span>25L</span>
                          <span>50L</span>
                          <span>75L</span>
                          <span>100L</span>
                          <span>125L</span>
                          <span>150L</span>
                          <span>175L</span>
                          <span>200L</span>
                        </div>
                      </div>
                    </div>

                    {/* Interest Rate */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="text-sm font-medium">
                          Interest Rate
                        </label>
                        <div className="flex items-center">
                          <Input
                            type="text"
                            value={interestRate}
                            onChange={(e) => {
                              const value = e.target.value.replace(
                                /[^\d.]/g,
                                ""
                              );
                              setInterestRate(
                                value ? Number.parseFloat(value) : 0
                              );
                            }}
                            className="w-32 text-right"
                          />
                          <span className="ml-2">%</span>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="range"
                          min="5"
                          max="20"
                          step="0.1"
                          value={interestRate}
                          onChange={(e) =>
                            setInterestRate(Number.parseFloat(e.target.value))
                          }
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>5</span>
                          <span>7.5</span>
                          <span>10</span>
                          <span>12.5</span>
                          <span>15</span>
                          <span>17.5</span>
                          <span>20</span>
                        </div>
                      </div>
                    </div>

                    {/* Loan Tenure */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="text-sm font-medium">
                          Loan Tenure
                        </label>
                        <div className="flex items-center">
                          <Input
                            type="text"
                            value={loanTenure}
                            onChange={(e) => {
                              const value = e.target.value.replace(
                                /[^\d]/g,
                                ""
                              );
                              setLoanTenure(value ? Number.parseInt(value) : 0);
                            }}
                            className="w-32 text-right"
                          />
                          <div className="ml-2 flex">
                            <Button
                              variant={
                                tenureType === "year" ? "default" : "outline"
                              }
                              size="sm"
                              onClick={() => setTenureType("year")}
                              className="rounded-r-none"
                            >
                              Yr
                            </Button>
                            <Button
                              variant={
                                tenureType === "month" ? "default" : "outline"
                              }
                              size="sm"
                              onClick={() => setTenureType("month")}
                              className="rounded-l-none"
                            >
                              Mo
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="range"
                          min="0"
                          max="30"
                          step="1"
                          value={loanTenure}
                          onChange={(e) =>
                            setLoanTenure(Number.parseInt(e.target.value))
                          }
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>0</span>
                          <span>5</span>
                          <span>10</span>
                          <span>15</span>
                          <span>20</span>
                          <span>25</span>
                          <span>30</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-3 md:gap-4 gap-2 mt-6">
                  <Card className="px-4 py-2 md:p-4">
                    <h3 className="text-sm font-medium text-gray-500">
                      Loan EMI
                    </h3>
                    <p className="md:text-2xl text-lg font-semibold mt-1">
                      ₹ {formatCurrency(emi)}
                    </p>
                  </Card>
                  <Card className="px-4 py-2 md:p-4">
                    <h3 className="text-sm font-medium text-gray-500">
                      Total Interest Payable
                    </h3>
                    <p className="md:text-2xl text-lg font-semibold mt-1">
                      ₹ {formatCurrency(totalInterest)}
                    </p>
                  </Card>
                  <Card className="px-4 py-2 md:p-4">
                    <h3 className="text-sm font-medium text-gray-500">
                      Total Payment
                      <br />
                      (Principal + Interest)
                    </h3>
                    <p className="md:text-2xl text-lg font-semibold mt-1">
                      ₹ {formatCurrency(totalPayment)}
                    </p>
                  </Card>
                </div>
              </div>

              <div className="lg:col-span-1">
                <Card className="p-4 h-full flex flex-col justify-center">
                  <h3 className="text-sm font-medium text-gray-500 mb-4 text-center">
                    Break-up of Total Payment
                  </h3>
                  <PieChart data={pieChartData} />
                  <div className="flex justify-center gap-6 mt-4">
                    <div className="flex items-center">
                      <span className="w-3 h-3 rounded-full bg-[#1e2a5a] inline-block mr-2"></span>
                      <span className="text-xs">Principal Loan Amount</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-3 h-3 rounded-full bg-[#b22222] inline-block mr-2"></span>
                      <span className="text-xs">Total Interest</span>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="mt-8">
              <Card className="p-2 md:p-6">
                <div className="p-2 md:p-0 flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                  <label className="text-sm font-medium mb-2 md:mb-0">
                    Schedule showing EMI payments starting from
                  </label>
                  <div className="flex flex-col md:flex-row gap-4">
                    <Input
                      type="month"
                      value={`${startDate.getFullYear()}-${String(
                        startDate.getMonth() + 1
                      ).padStart(2, "0")}`}
                      onChange={(e) => {
                        const [year, month] = e.target.value.split("-");
                        const newDate = new Date(startDate);
                        newDate.setFullYear(Number.parseInt(year));
                        newDate.setMonth(Number.parseInt(month) - 1);
                        setStartDate(newDate);
                      }}
                      className="w-40 min-w-fit"
                    />
                    <select
                      value={yearFormat}
                      onChange={(e) => setYearFormat(e.target.value)}
                      className="border rounded p-2 text-sm"
                    >
                      <option value="calendar">Calendar Year wise</option>
                      <option value="financial">Financial Year wise</option>
                    </select>
                  </div>
                </div>

                <div className="mb-6">
                  <BarChart data={barChartData} />
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Year
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Principal (A)
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Interest (B)
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Payment (A + B)
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Loan Paid To Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Details
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {dummyData.map((item, index) => (
                        <React.Fragment key={index}>
                          <tr
                            className={
                              index % 2 === 0 ? "bg-white" : "bg-gray-50"
                            }
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {item.year}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ₹ {formatCurrency(item.principal)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ₹ {formatCurrency(item.interest)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ₹ {formatCurrency(item.totalPayment)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ₹ {formatCurrency(item.balance)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {item.paidPercentage.toFixed(2)}%
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button
                                onClick={() => toggleRowExpansion(item.year)}
                                className="flex items-center cursor-pointer text-blue-600 hover:text-blue-800 transition-colors"
                              >
                                {expandedRows[item.year] ? (
                                  <>
                                    <span className="mr-1">Hide</span>
                                    <ChevronUp size={16} />
                                  </>
                                ) : (
                                  <>
                                    <span className="mr-1">Show</span>
                                    <ChevronDown size={16} />
                                  </>
                                )}
                              </button>
                            </td>
                          </tr>
                          {expandedRows[item.year] && (
                            <tr>
                              <td colSpan={7} className="px-6 py-4 bg-gray-50">
                                <div className="overflow-x-auto rounded-md border border-gray-200">
                                  <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-100">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          Month
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          EMI
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          Principal
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          Interest
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          Balance
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          Loan Paid To Date
                                        </th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                      {item.months.map((month, monthIndex) => (
                                        <tr
                                          key={monthIndex}
                                          className={
                                            monthIndex % 2 === 0
                                              ? "bg-white"
                                              : "bg-gray-50"
                                          }
                                        >
                                          <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {month.month}
                                          </td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                            ₹ {formatCurrency(month.emi)}
                                          </td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                            ₹ {formatCurrency(month.principal)}
                                          </td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                            ₹ {formatCurrency(month.interest)}
                                          </td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                            ₹ {formatCurrency(month.balance)}
                                          </td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                           {month.paidPercentage} %
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </td>
                            </tr>
                          )}
                        </React.Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

const dummyData = [
 {
      "year": "2025",
      "principal": 61486,
      "interest": 298404,
      "totalPayment": 359890,
      "balance": 4938514,
      "paidPercentage": 1.23,
      "months": [
        {
          "month": "May",
          "emi": 44986,
          "principal": 7486,
          "interest": 37500,
          "balance": 4992514,
          "paidPercentage": 0.15
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 7542,
          "interest": 37444,
          "balance": 4984971,
          "paidPercentage": 0.30
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 7599,
          "interest": 37387,
          "balance": 4977372,
          "paidPercentage": 0.45
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 7656,
          "interest": 37330,
          "balance": 4969716,
          "paidPercentage": 0.61
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 7713,
          "interest": 37273,
          "balance": 4962003,
          "paidPercentage": 0.76
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 7771,
          "interest": 37215,
          "balance": 4954232,
          "paidPercentage": 0.92
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 7830,
          "interest": 37157,
          "balance": 4946402,
          "paidPercentage": 1.07
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 7888,
          "interest": 37098,
          "balance": 4938514,
          "paidPercentage": 1.23
        }
      ]
    },
    {
      "year": "2026",
      "principal": 99403,
      "interest": 440432,
      "totalPayment": 539836,
      "balance": 4839110,
      "paidPercentage": 3.22,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 7947,
          "interest": 37039,
          "balance": 4930566,
          "paidPercentage": 1.39
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 8007,
          "interest": 36979,
          "balance": 4922559,
          "paidPercentage": 1.55
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 8067,
          "interest": 36919,
          "balance": 4914492,
          "paidPercentage": 1.71
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 8128,
          "interest": 36859,
          "balance": 4906364,
          "paidPercentage": 1.87
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 8189,
          "interest": 36798,
          "balance": 4898176,
          "paidPercentage": 2.04
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 8250,
          "interest": 36736,
          "balance": 4889926,
          "paidPercentage": 2.20
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 8312,
          "interest": 36674,
          "balance": 4881614,
          "paidPercentage": 2.37
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 8374,
          "interest": 36612,
          "balance": 4873240,
          "paidPercentage": 2.54
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 8437,
          "interest": 36549,
          "balance": 4864803,
          "paidPercentage": 2.70
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 8500,
          "interest": 36486,
          "balance": 4856303,
          "paidPercentage": 2.87
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 8564,
          "interest": 36422,
          "balance": 4847739,
          "paidPercentage": 3.05
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 8628,
          "interest": 36358,
          "balance": 4839110,
          "paidPercentage": 3.22
        }
      ]
    },
    {
      "year": "2027",
      "principal": 108728,
      "interest": 431107,
      "totalPayment": 539836,
      "balance": 4730382,
      "paidPercentage": 5.39,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 8693,
          "interest": 36293,
          "balance": 4830417,
          "paidPercentage": 3.39
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 8758,
          "interest": 36228,
          "balance": 4821659,
          "paidPercentage": 3.57
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 8824,
          "interest": 36162,
          "balance": 4812835,
          "paidPercentage": 3.74
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 8890,
          "interest": 36096,
          "balance": 4803945,
          "paidPercentage": 3.92
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 8957,
          "interest": 36030,
          "balance": 4794989,
          "paidPercentage": 4.10
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 9024,
          "interest": 35962,
          "balance": 4785965,
          "paidPercentage": 4.28
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 9092,
          "interest": 35895,
          "balance": 4776873,
          "paidPercentage": 4.46
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 9160,
          "interest": 35827,
          "balance": 4767713,
          "paidPercentage": 4.65
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 9228,
          "interest": 35758,
          "balance": 4758485,
          "paidPercentage": 4.83
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 9298,
          "interest": 35689,
          "balance": 4749187,
          "paidPercentage": 5.02
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 9367,
          "interest": 35619,
          "balance": 4739820,
          "paidPercentage": 5.20
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 9438,
          "interest": 35549,
          "balance": 4730382,
          "paidPercentage": 5.39
        }
      ]
    },
    {
      "year": "2028",
      "principal": 118928,
      "interest": 420908,
      "totalPayment": 539836,
      "balance": 4611455,
      "paidPercentage": 7.77,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 9508,
          "interest": 35478,
          "balance": 4720874,
          "paidPercentage": 5.58
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 9580,
          "interest": 35407,
          "balance": 4711294,
          "paidPercentage": 5.77
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 9652,
          "interest": 35335,
          "balance": 4701642,
          "paidPercentage": 5.97
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 9724,
          "interest": 35262,
          "balance": 4691919,
          "paidPercentage": 6.16
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 9797,
          "interest": 35189,
          "balance": 4682122,
          "paidPercentage": 6.36
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 9870,
          "interest": 35116,
          "balance": 4672251,
          "paidPercentage": 6.55
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 9944,
          "interest": 35042,
          "balance": 4662307,
          "paidPercentage": 6.75
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 10019,
          "interest": 34967,
          "balance": 4652288,
          "paidPercentage": 6.95
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 10094,
          "interest": 34892,
          "balance": 4642194,
          "paidPercentage": 7.16
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 10170,
          "interest": 34816,
          "balance": 4632024,
          "paidPercentage": 7.36
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 10246,
          "interest": 34740,
          "balance": 4621778,
          "paidPercentage": 7.56
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 10323,
          "interest": 34663,
          "balance": 4611455,
          "paidPercentage": 7.77
        }
      ]
    },
    {
      "year": "2029",
      "principal": 130084,
      "interest": 409752,
      "totalPayment": 539836,
      "balance": 4481371,
      "paidPercentage": 10.37,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 10400,
          "interest": 34586,
          "balance": 4601054,
          "paidPercentage": 7.98
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 10478,
          "interest": 34508,
          "balance": 4590576,
          "paidPercentage": 8.19
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 10557,
          "interest": 34429,
          "balance": 4580019,
          "paidPercentage": 8.40
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 10636,
          "interest": 34350,
          "balance": 4569383,
          "paidPercentage": 8.61
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 10716,
          "interest": 34270,
          "balance": 4558667,
          "paidPercentage": 8.83
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 10796,
          "interest": 34190,
          "balance": 4547871,
          "paidPercentage": 9.04
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 10877,
          "interest": 34109,
          "balance": 4536993,
          "paidPercentage": 9.26
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 10959,
          "interest": 34027,
          "balance": 4526034,
          "paidPercentage": 9.48
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 11041,
          "interest": 33945,
          "balance": 4514993,
          "paidPercentage": 9.70
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 11124,
          "interest": 33862,
          "balance": 4503870,
          "paidPercentage": 9.92
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 11207,
          "interest": 33779,
          "balance": 4492662,
          "paidPercentage": 10.15
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 11291,
          "interest": 33695,
          "balance": 4481371,
          "paidPercentage": 10.37
        }
      ]
    },
    {
      "year": "2030",
      "principal": 142286,
      "interest": 397549,
      "totalPayment": 539836,
      "balance": 4339085,
      "paidPercentage": 13.22,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 11376,
          "interest": 33610,
          "balance": 4469995,
          "paidPercentage": 10.60
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 11461,
          "interest": 33525,
          "balance": 4458534,
          "paidPercentage": 10.83
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 11547,
          "interest": 33439,
          "balance": 4446986,
          "paidPercentage": 11.06
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 11634,
          "interest": 33352,
          "balance": 4435352,
          "paidPercentage": 11.29
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 11721,
          "interest": 33265,
          "balance": 4423631,
          "paidPercentage": 11.53
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 11809,
          "interest": 33177,
          "balance": 4411822,
          "paidPercentage": 11.76
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 11898,
          "interest": 33089,
          "balance": 4399925,
          "paidPercentage": 12.00
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 11987,
          "interest": 32999,
          "balance": 4387938,
          "paidPercentage": 12.24
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 12077,
          "interest": 32910,
          "balance": 4375861,
          "paidPercentage": 12.48
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 12167,
          "interest": 32819,
          "balance": 4363694,
          "paidPercentage": 12.73
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 12259,
          "interest": 32728,
          "balance": 4351435,
          "paidPercentage": 12.97
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 12351,
          "interest": 32636,
          "balance": 4339085,
          "paidPercentage": 13.22
        }
      ]
    },
    {
      "year": "2031",
      "principal": 155634,
      "interest": 384202,
      "totalPayment": 539836,
      "balance": 4183451,
      "paidPercentage": 16.33,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 12443,
          "interest": 32543,
          "balance": 4326641,
          "paidPercentage": 13.47
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 12536,
          "interest": 32450,
          "balance": 4314105,
          "paidPercentage": 13.72
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 12631,
          "interest": 32356,
          "balance": 4301474,
          "paidPercentage": 13.97
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 12725,
          "interest": 32261,
          "balance": 4288749,
          "paidPercentage": 14.23
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 12821,
          "interest": 32166,
          "balance": 4275928,
          "paidPercentage": 14.48
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 12917,
          "interest": 32069,
          "balance": 4263012,
          "paidPercentage": 14.74
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 13014,
          "interest": 31973,
          "balance": 4249998,
          "paidPercentage": 15.00
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 13111,
          "interest": 31875,
          "balance": 4236887,
          "paidPercentage": 15.26
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 13210,
          "interest": 31777,
          "balance": 4223677,
          "paidPercentage": 15.53
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 13309,
          "interest": 31678,
          "balance": 4210368,
          "paidPercentage": 15.79
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 13409,
          "interest": 31578,
          "balance": 4196960,
          "paidPercentage": 16.06
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 13509,
          "interest": 31477,
          "balance": 4183451,
          "paidPercentage": 16.33
        }
      ]
    },
    {
      "year": "2032",
      "principal": 170233,
      "interest": 369602,
      "totalPayment": 539836,
      "balance": 4013217,
      "paidPercentage": 19.74,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 13610,
          "interest": 31376,
          "balance": 4169840,
          "paidPercentage": 16.60
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 13712,
          "interest": 31274,
          "balance": 4156128,
          "paidPercentage": 16.88
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 13815,
          "interest": 31171,
          "balance": 4142312,
          "paidPercentage": 17.15
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 13919,
          "interest": 31067,
          "balance": 4128393,
          "paidPercentage": 17.43
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 14023,
          "interest": 30963,
          "balance": 4114370,
          "paidPercentage": 17.71
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 14129,
          "interest": 30858,
          "balance": 4100241,
          "paidPercentage": 18.00
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 14234,
          "interest": 30752,
          "balance": 4086007,
          "paidPercentage": 18.28
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 14341,
          "interest": 30645,
          "balance": 4071666,
          "paidPercentage": 18.57
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 14449,
          "interest": 30537,
          "balance": 4057217,
          "paidPercentage": 18.86
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 14557,
          "interest": 30429,
          "balance": 4042660,
          "paidPercentage": 19.15
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 14666,
          "interest": 30320,
          "balance": 4027993,
          "paidPercentage": 19.44
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 14776,
          "interest": 30210,
          "balance": 4013217,
          "paidPercentage": 19.74
        }
      ]
    },
    {
      "year": "2033",
      "principal": 186203,
      "interest": 353633,
      "totalPayment": 539836,
      "balance": 3827015,
      "paidPercentage": 23.46,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 14887,
          "interest": 30099,
          "balance": 3998330,
          "paidPercentage": 20.03
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 14999,
          "interest": 29987,
          "balance": 3983331,
          "paidPercentage": 20.33
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 15111,
          "interest": 29875,
          "balance": 3968220,
          "paidPercentage": 20.64
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 15225,
          "interest": 29762,
          "balance": 3952995,
          "paidPercentage": 20.94
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 15339,
          "interest": 29647,
          "balance": 3937656,
          "paidPercentage": 21.25
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 15454,
          "interest": 29532,
          "balance": 3922202,
          "paidPercentage": 21.56
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 15570,
          "interest": 29417,
          "balance": 3906633,
          "paidPercentage": 21.87
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 15687,
          "interest": 29300,
          "balance": 3890946,
          "paidPercentage": 22.18
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 15804,
          "interest": 29182,
          "balance": 3875142,
          "paidPercentage": 22.50
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 15923,
          "interest": 29064,
          "balance": 3859219,
          "paidPercentage": 22.82
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 16042,
          "interest": 28944,
          "balance": 3843177,
          "paidPercentage": 23.14
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 16162,
          "interest": 28824,
          "balance": 3827015,
          "paidPercentage": 23.46
        }
      ]
    },
    {
      "year": "2034",
      "principal": 203670,
      "interest": 336166,
      "totalPayment": 539836,
      "balance": 3623345,
      "paidPercentage": 27.53,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 16284,
          "interest": 28703,
          "balance": 3810731,
          "paidPercentage": 23.79
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 16406,
          "interest": 28580,
          "balance": 3794325,
          "paidPercentage": 24.11
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 16529,
          "interest": 28457,
          "balance": 3777796,
          "paidPercentage": 24.44
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 16653,
          "interest": 28333,
          "balance": 3761143,
          "paidPercentage": 24.78
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 16778,
          "interest": 28209,
          "balance": 3744366,
          "paidPercentage": 25.11
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 16904,
          "interest": 28083,
          "balance": 3727462,
          "paidPercentage": 25.45
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 17030,
          "interest": 27956,
          "balance": 3710432,
          "paidPercentage": 25.79
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 17158,
          "interest": 27828,
          "balance": 3693274,
          "paidPercentage": 26.13
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 17287,
          "interest": 27700,
          "balance": 3675987,
          "paidPercentage": 26.48
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 17416,
          "interest": 27570,
          "balance": 3658571,
          "paidPercentage": 26.83
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 17547,
          "interest": 27439,
          "balance": 3641023,
          "paidPercentage": 27.18
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 17679,
          "interest": 27308,
          "balance": 3623345,
          "paidPercentage": 27.53
        }
      ]
    },
    {
      "year": "2035",
      "principal": 222775,
      "interest": 317060,
      "totalPayment": 539836,
      "balance": 3400570,
      "paidPercentage": 31.99,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 17811,
          "interest": 27175,
          "balance": 3605534,
          "paidPercentage": 27.89
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 17945,
          "interest": 27042,
          "balance": 3587589,
          "paidPercentage": 28.25
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 18079,
          "interest": 26907,
          "balance": 3569509,
          "paidPercentage": 28.61
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 18215,
          "interest": 26771,
          "balance": 3551294,
          "paidPercentage": 28.97
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 18352,
          "interest": 26635,
          "balance": 3532943,
          "paidPercentage": 29.34
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 18489,
          "interest": 26497,
          "balance": 3514454,
          "paidPercentage": 29.71
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 18628,
          "interest": 26358,
          "balance": 3495826,
          "paidPercentage": 30.08
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 18768,
          "interest": 26219,
          "balance": 3477058,
          "paidPercentage": 30.46
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 18908,
          "interest": 26078,
          "balance": 3458150,
          "paidPercentage": 30.84
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 19050,
          "interest": 25936,
          "balance": 3439100,
          "paidPercentage": 31.22
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 19193,
          "interest": 25793,
          "balance": 3419907,
          "paidPercentage": 31.60
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 19337,
          "interest": 25649,
          "balance": 3400570,
          "paidPercentage": 31.99
        }
      ]
    },
    {
      "year": "2036",
      "principal": 243673,
      "interest": 296162,
      "totalPayment": 539836,
      "balance": 3156896,
      "paidPercentage": 36.86,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 19482,
          "interest": 25504,
          "balance": 3381088,
          "paidPercentage": 32.38
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 19628,
          "interest": 25358,
          "balance": 3361459,
          "paidPercentage": 32.77
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 19775,
          "interest": 25211,
          "balance": 3341684,
          "paidPercentage": 33.17
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 19924,
          "interest": 25063,
          "balance": 3321760,
          "paidPercentage": 33.56
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 20073,
          "interest": 24913,
          "balance": 3301687,
          "paidPercentage": 33.97
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 20224,
          "interest": 24763,
          "balance": 3281464,
          "paidPercentage": 34.37
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 20375,
          "interest": 24611,
          "balance": 3261088,
          "paidPercentage": 34.78
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 20528,
          "interest": 24458,
          "balance": 3240560,
          "paidPercentage": 35.19
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 20682,
          "interest": 24304,
          "balance": 3219878,
          "paidPercentage": 35.60
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 20837,
          "interest": 24149,
          "balance": 3199041,
          "paidPercentage": 36.02
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 20993,
          "interest": 23993,
          "balance": 3178047,
          "paidPercentage": 36.44
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 21151,
          "interest": 23835,
          "balance": 3156896,
          "paidPercentage": 36.86
        }
      ]
    },
    {
      "year": "2037",
      "principal": 266531,
      "interest": 273304,
      "totalPayment": 539836,
      "balance": 2890365,
      "paidPercentage": 42.19,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 21310,
          "interest": 23677,
          "balance": 3135587,
          "paidPercentage": 37.29
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 21469,
          "interest": 23517,
          "balance": 3114118,
          "paidPercentage": 37.72
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 21630,
          "interest": 23356,
          "balance": 3092487,
          "paidPercentage": 38.15
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 21793,
          "interest": 23194,
          "balance": 3070694,
          "paidPercentage": 38.59
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 21956,
          "interest": 23030,
          "balance": 3048738,
          "paidPercentage": 39.03
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 22121,
          "interest": 22866,
          "balance": 3026618,
          "paidPercentage": 39.47
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 22287,
          "interest": 22700,
          "balance": 3004331,
          "paidPercentage": 39.91
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 22454,
          "interest": 22532,
          "balance": 2981877,
          "paidPercentage": 40.36
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 22622,
          "interest": 22364,
          "balance": 2959255,
          "paidPercentage": 40.81
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 22792,
          "interest": 22194,
          "balance": 2936463,
          "paidPercentage": 41.27
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 22963,
          "interest": 22023,
          "balance": 2913500,
          "paidPercentage": 41.73
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 23135,
          "interest": 21851,
          "balance": 2890365,
          "paidPercentage": 42.19
        }
      ]
    },
    {
      "year": "2038",
      "principal": 291534,
      "interest": 248302,
      "totalPayment": 539836,
      "balance": 2598831,
      "paidPercentage": 48.02,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 23309,
          "interest": 21678,
          "balance": 2867057,
          "paidPercentage": 42.66
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 23483,
          "interest": 21503,
          "balance": 2843573,
          "paidPercentage": 43.13
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 23659,
          "interest": 21327,
          "balance": 2819914,
          "paidPercentage": 43.60
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 23837,
          "interest": 21149,
          "balance": 2796077,
          "paidPercentage": 44.08
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 24016,
          "interest": 20971,
          "balance": 2772061,
          "paidPercentage": 44.56
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 24196,
          "interest": 20790,
          "balance": 2747865,
          "paidPercentage": 45.04
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 24377,
          "interest": 20609,
          "balance": 2723488,
          "paidPercentage": 45.53
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 24560,
          "interest": 20426,
          "balance": 2698928,
          "paidPercentage": 46.02
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 24744,
          "interest": 20242,
          "balance": 2674183,
          "paidPercentage": 46.52
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 24930,
          "interest": 20056,
          "balance": 2649253,
          "paidPercentage": 47.01
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 25117,
          "interest": 19869,
          "balance": 2624137,
          "paidPercentage": 47.52
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 25305,
          "interest": 19681,
          "balance": 2598831,
          "paidPercentage": 48.02
        }
      ]
    },
    {
      "year": "2039",
      "principal": 318882,
      "interest": 220954,
      "totalPayment": 539836,
      "balance": 2279950,
      "paidPercentage": 54.40,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 25495,
          "interest": 19491,
          "balance": 2573336,
          "paidPercentage": 48.53
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 25686,
          "interest": 19300,
          "balance": 2547650,
          "paidPercentage": 49.05
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 25879,
          "interest": 19107,
          "balance": 2521771,
          "paidPercentage": 49.56
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 26073,
          "interest": 18913,
          "balance": 2495698,
          "paidPercentage": 50.09
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 26269,
          "interest": 18718,
          "balance": 2469429,
          "paidPercentage": 50.61
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 26466,
          "interest": 18521,
          "balance": 2442964,
          "paidPercentage": 51.14
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 26664,
          "interest": 18322,
          "balance": 2416300,
          "paidPercentage": 51.67
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 26864,
          "interest": 18122,
          "balance": 2389436,
          "paidPercentage": 52.21
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 27066,
          "interest": 17921,
          "balance": 2362370,
          "paidPercentage": 52.75
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 27269,
          "interest": 17718,
          "balance": 2335102,
          "paidPercentage": 53.30
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 27473,
          "interest": 17513,
          "balance": 2307629,
          "paidPercentage": 53.85
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 27679,
          "interest": 17307,
          "balance": 2279950,
          "paidPercentage": 54.40
        }
      ]
    },
    {
      "year": "2040",
      "principal": 348795,
      "interest": 191041,
      "totalPayment": 539836,
      "balance": 1931155,
      "paidPercentage": 61.38,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 27887,
          "interest": 17100,
          "balance": 2252063,
          "paidPercentage": 54.96
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 28096,
          "interest": 16890,
          "balance": 2223967,
          "paidPercentage": 55.52
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 28307,
          "interest": 16680,
          "balance": 2195661,
          "paidPercentage": 56.09
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 28519,
          "interest": 16467,
          "balance": 2167142,
          "paidPercentage": 56.66
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 28733,
          "interest": 16254,
          "balance": 2138409,
          "paidPercentage": 57.23
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 28948,
          "interest": 16038,
          "balance": 2109461,
          "paidPercentage": 57.81
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 29165,
          "interest": 15821,
          "balance": 2080295,
          "paidPercentage": 58.39
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 29384,
          "interest": 15602,
          "balance": 2050911,
          "paidPercentage": 58.98
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 29604,
          "interest": 15382,
          "balance": 2021307,
          "paidPercentage": 59.57
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 29826,
          "interest": 15160,
          "balance": 1991480,
          "paidPercentage": 60.17
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 30050,
          "interest": 14936,
          "balance": 1961430,
          "paidPercentage": 60.77
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 30276,
          "interest": 14711,
          "balance": 1931155,
          "paidPercentage": 61.38
        }
      ]
    },
    {
      "year": "2041",
      "principal": 381514,
      "interest": 158321,
      "totalPayment": 539836,
      "balance": 1549640,
      "paidPercentage": 69.01,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 30503,
          "interest": 14484,
          "balance": 1900652,
          "paidPercentage": 61.99
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 30731,
          "interest": 14255,
          "balance": 1869921,
          "paidPercentage": 62.60
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 30962,
          "interest": 14024,
          "balance": 1838959,
          "paidPercentage": 63.22
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 31194,
          "interest": 13792,
          "balance": 1807765,
          "paidPercentage": 63.84
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 31428,
          "interest": 13558,
          "balance": 1776337,
          "paidPercentage": 64.47
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 31664,
          "interest": 13323,
          "balance": 1744673,
          "paidPercentage": 65.11
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 31901,
          "interest": 13085,
          "balance": 1712771,
          "paidPercentage": 65.74
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 32141,
          "interest": 12846,
          "balance": 1680631,
          "paidPercentage": 66.39
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 32382,
          "interest": 12605,
          "balance": 1648249,
          "paidPercentage": 67.04
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 32624,
          "interest": 12362,
          "balance": 1615625,
          "paidPercentage": 67.69
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 32869,
          "interest": 12117,
          "balance": 1582756,
          "paidPercentage": 68.34
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 33116,
          "interest": 11871,
          "balance": 1549640,
          "paidPercentage": 69.01
        }
      ]
    },
    {
      "year": "2042",
      "principal": 417303,
      "interest": 122533,
      "totalPayment": 539836,
      "balance": 1132337,
      "paidPercentage": 77.35,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 33364,
          "interest": 11622,
          "balance": 1516276,
          "paidPercentage": 69.67
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 33614,
          "interest": 11372,
          "balance": 1482662,
          "paidPercentage": 70.35
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 33866,
          "interest": 11120,
          "balance": 1448796,
          "paidPercentage": 71.02
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 34120,
          "interest": 10866,
          "balance": 1414675,
          "paidPercentage": 71.71
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 34376,
          "interest": 10610,
          "balance": 1380299,
          "paidPercentage": 72.39
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 34634,
          "interest": 10352,
          "balance": 1345665,
          "paidPercentage": 73.09
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 34894,
          "interest": 10092,
          "balance": 1310771,
          "paidPercentage": 73.78
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 35156,
          "interest": 9831,
          "balance": 1275616,
          "paidPercentage": 74.49
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 35419,
          "interest": 9567,
          "balance": 1240197,
          "paidPercentage": 75.20
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 35685,
          "interest": 9301,
          "balance": 1204512,
          "paidPercentage": 75.91
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 35952,
          "interest": 9034,
          "balance": 1168559,
          "paidPercentage": 76.63
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 36222,
          "interest": 8764,
          "balance": 1132337,
          "paidPercentage": 77.35
        }
      ]
    },
    {
      "year": "2043",
      "principal": 456449,
      "interest": 83387,
      "totalPayment": 539836,
      "balance": 675888,
      "paidPercentage": 86.48,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 36494,
          "interest": 8493,
          "balance": 1095843,
          "paidPercentage": 78.08
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 36767,
          "interest": 8219,
          "balance": 1059076,
          "paidPercentage": 78.82
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 37043,
          "interest": 7943,
          "balance": 1022033,
          "paidPercentage": 79.56
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 37321,
          "interest": 7665,
          "balance": 984712,
          "paidPercentage": 80.31
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 37601,
          "interest": 7385,
          "balance": 947111,
          "paidPercentage": 81.06
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 37883,
          "interest": 7103,
          "balance": 909228,
          "paidPercentage": 81.82
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 38167,
          "interest": 6819,
          "balance": 871061,
          "paidPercentage": 82.58
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 38453,
          "interest": 6533,
          "balance": 832607,
          "paidPercentage": 83.35
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 38742,
          "interest": 6245,
          "balance": 793866,
          "paidPercentage": 84.12
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 39032,
          "interest": 5954,
          "balance": 754833,
          "paidPercentage": 84.90
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 39325,
          "interest": 5661,
          "balance": 715508,
          "paidPercentage": 85.69
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 39620,
          "interest": 5366,
          "balance": 675888,
          "paidPercentage": 86.48
        }
      ]
    },
    {
      "year": "2044",
      "principal": 499267,
      "interest": 40569,
      "totalPayment": 539836,
      "balance": 176621,
      "paidPercentage": 96.47,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 39917,
          "interest": 5069,
          "balance": 635971,
          "paidPercentage": 87.28
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 40217,
          "interest": 4770,
          "balance": 595755,
          "paidPercentage": 88.08
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 40518,
          "interest": 4468,
          "balance": 555236,
          "paidPercentage": 88.90
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 40822,
          "interest": 4164,
          "balance": 514414,
          "paidPercentage": 89.71
        },
        {
          "month": "May",
          "emi": 44986,
          "principal": 41128,
          "interest": 3858,
          "balance": 473286,
          "paidPercentage": 90.53
        },
        {
          "month": "Jun",
          "emi": 44986,
          "principal": 41437,
          "interest": 3550,
          "balance": 431850,
          "paidPercentage": 91.36
        },
        {
          "month": "Jul",
          "emi": 44986,
          "principal": 41747,
          "interest": 3239,
          "balance": 390102,
          "paidPercentage": 92.20
        },
        {
          "month": "Aug",
          "emi": 44986,
          "principal": 42061,
          "interest": 2926,
          "balance": 348042,
          "paidPercentage": 93.04
        },
        {
          "month": "Sep",
          "emi": 44986,
          "principal": 42376,
          "interest": 2610,
          "balance": 305666,
          "paidPercentage": 93.89
        },
        {
          "month": "Oct",
          "emi": 44986,
          "principal": 42694,
          "interest": 2292,
          "balance": 262972,
          "paidPercentage": 94.74
        },
        {
          "month": "Nov",
          "emi": 44986,
          "principal": 43014,
          "interest": 1972,
          "balance": 219958,
          "paidPercentage": 95.60
        },
        {
          "month": "Dec",
          "emi": 44986,
          "principal": 43337,
          "interest": 1650,
          "balance": 176621,
          "paidPercentage": 96.47
        }
      ]
    },
    {
      "year": "2045",
      "principal": 176621,
      "interest": 3324,
      "totalPayment": 179945,
      "balance": 0,
      "paidPercentage": 100.00,
      "months": [
        {
          "month": "Jan",
          "emi": 44986,
          "principal": 43662,
          "interest": 1325,
          "balance": 132960,
          "paidPercentage": 97.34
        },
        {
          "month": "Feb",
          "emi": 44986,
          "principal": 43989,
          "interest": 997,
          "balance": 88970,
          "paidPercentage": 98.22
        },
        {
          "month": "Mar",
          "emi": 44986,
          "principal": 44319,
          "interest": 667,
          "balance": 44651,
          "paidPercentage": 99.11
        },
        {
          "month": "Apr",
          "emi": 44986,
          "principal": 44651,
          "interest": 335,
          "balance": 0,
          "paidPercentage": 100.00
        }
      ]
    }
];
