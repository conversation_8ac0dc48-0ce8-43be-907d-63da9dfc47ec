import TraditionalInvestmentMaster from "@/components/investment/Traditional-Investment/TraditionalInvestmentMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
      <Head>
        <title>
          Traditional Investment Options | Secure & Proven Wealth Building |
          Winshine
        </title>
        <meta
          name="description"
          content="Discover Winshine’s traditional investment options including fixed deposits, bonds, and government schemes. Secure your financial future with trusted, time-tested investment solutions."
        />
        <meta
          name="keywords"
          content="traditional investments, fixed deposits, bonds, government schemes, safe investments, Winshine financial planning"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta
          property="og:title"
          content="Traditional Investment Options | Winshine Financial Services"
        />
        <meta
          property="og:description"
          content="Build wealth securely with Winshine’s range of traditional investments like fixed deposits, bonds, and government-backed schemes. Trusted financial planning for all."
        />
        <meta
          property="og:image"
          content="https://winshine.nipralo.com/images/investment/2148803950.jpg"
        />
        <meta
          property="og:url"
          content="https://winshine.nipralo.com/investment/traditional-investment"
        />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/investment/2148803950.jpg" />
        <link
          rel="canonical"
          href="https://winshine.nipralo.com/investment/traditional-investment"
        />
      </Head>
      <TraditionalInvestmentMaster />
    </>
  );
};

export default page;
