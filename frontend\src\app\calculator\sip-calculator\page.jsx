import SipCalculatorMaster from '@/components/calculator/sip-calculator/SipCalculatorMaster'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
    <Head>
        <title>SIP Calculator | Plan Your Mutual Fund Investments | Winshine</title>
        <meta name="description" content="Use Winshine’s SIP Calculator to estimate the future value of your mutual fund investments. Plan your wealth creation journey with accurate and easy projections." />
        <meta name="keywords" content="SIP calculator, mutual fund calculator, investment calculator, monthly investment planning, wealth creation, Winshine SIP tool" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="SIP Calculator | Mutual Fund Investment Planner | Winshine" />
        <meta property="og:description" content="Estimate returns on your SIP investments with our easy-to-use calculator. Winshine helps you make smarter, ethical investment decisions." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg" /> 
        <meta property="og:url" content="https://winshine.nipralo.com/calculator/sip-calculator" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg" />
        <link rel="canonical" href="https://winshine.nipralo.com/calculator/sip-calculator" />
      </Head>
    <SipCalculatorMaster/>
    </>
  )
}

export default page
