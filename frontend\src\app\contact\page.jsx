import ContactMaster from "@/components/contact/ContactMaster";
import Head from "next/head";
import React from "react";

const ContactPage = () => {
  return (
    <>
      <Head>
        <title>
          Contact Winshine | Get in Touch with Our Financial Experts
        </title>
        <meta
          name="description"
          content="Reach out to Winshine Financial Services for expert guidance on investments, insurance, savings, and tax planning. We're here to help you achieve financial wellness."
        />
        <meta
          name="keywords"
          content="contact Winshine, financial advisor contact, get in touch, Winshine Financial Services, investment help, insurance support, tax planning contact"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta
          property="og:title"
          content="Contact Us | Winshine Financial Services"
        />
        <meta
          property="og:description"
          content="Have questions or need assistance? Contact Winshine's expert financial team and start your journey to secure, ethical wealth creation."
        />
        <meta
          property="og:image"
          content="https://winshine.nipralo.com/images/contact/contact-banner.webp"
        />{" "}
        {/* Replace with actual image */}
        <meta
          property="og:url"
          content="https://winshine.nipralo.com/contact"
        />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/contact/contact-banner.webp" />
        <link rel="canonical" href="https://winshine.nipralo.com/contact" />
      </Head>
      <ContactMaster />
    </>
  );
};

export default ContactPage;
