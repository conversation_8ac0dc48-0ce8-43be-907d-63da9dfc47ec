"use client";

import { useState, useEffect, useRef } from "react";
import CircularProgress from "./circular-progress";

export default function SipCalculator() {
  // State for form inputs
  const [investmentType, setInvestmentType] = useState("know-target-amount");
  const [investmentMode, setInvestmentMode] = useState("sip");
  const [targetAmount, setTargetAmount] = useState("1500000");
  const [duration, setDuration] = useState("10");
  const [rateOfReturn, setRateOfReturn] = useState("12");

  // State for calculation results
  const [investedAmount, setInvestedAmount] = useState(0);
  const [returns, setReturns] = useState(0);
  const [totalWealth, setTotalWealth] = useState(0);
  const [monthlyInvestment, setMonthlyInvestment] = useState(0);
  const [graphProgress, setGraphProgress] = useState(0);

  // State for errors
  const [errors, setErrors] = useState({
    targetAmount: "",
    duration: "",
    rateOfReturn: "",
    general: "",
  });

  // ROI array for slider
  const roiArr = useRef([]);

  // Initialize ROI array
  useEffect(() => {
    const tempRoiArr = [];
    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {
      tempRoiArr.push(Number.parseFloat(i).toFixed(2));
    }
    roiArr.current = tempRoiArr;
  }, []);

  // Format number with commas
  const numWithCommas = (num) => {
    return num.toLocaleString("en-IN");
  };

  // Remove commas from number string
  const removeCommas = (number) => {
    return number.toString().replace(/,/g, "");
  };

  // Calculate PMT - payment amount for loan or investment
  const PMT = (
    rate_per_period,
    number_of_payments,
    present_value,
    future_value,
    type
  ) => {
    if (rate_per_period !== 0.0) {
      // Interest rate exists
      const q = Math.pow(1 + rate_per_period, number_of_payments);
      return (
        -(rate_per_period * (future_value + q * present_value)) /
        ((-1 + q) * (1 + rate_per_period * type))
      );
    } else if (number_of_payments !== 0.0) {
      // No interest rate, but number of payments exists
      return -(future_value + present_value) / number_of_payments;
    }
    return 0;
  };

  // Calculate present value
  const presentValue = (rate, nper, pmt, fv) => {
    if (nper === 0) {
      return 0;
    }

    let pv_value;
    if (rate === 0) {
      // Interest rate is 0
      pv_value = -(fv + pmt * nper);
    } else {
      const x = Math.pow(1 + rate, -nper);
      const y = Math.pow(1 + rate, nper);
      pv_value = -(x * (fv * rate - pmt + y * pmt)) / rate;
    }

    return Number.parseFloat(pv_value.toFixed(2));
  };

  // Calculate future value
  const futureValue = (rate, nper, pmt, pv, type) => {
    const pow = Math.pow(1 + rate, nper);
    let fv;

    if (rate) {
      fv = (pmt * (1 + rate * type) * (1 - pow)) / rate - pv * pow;
    } else {
      fv = -1 * (pv + pmt * nper);
    }

    return Number.parseFloat(fv.toFixed(2));
  };

  // Validate range input
  const validateRangeInput = (value, max, min, errorKey) => {
    const numValue = Number.parseFloat(value);
    const numMax = Number.parseFloat(max);
    const numMin = Number.parseFloat(min);

    let validated = true;
    let errorMessage = "";

    if (numValue > numMax || isNaN(numValue)) {
      validated = false;
      errorMessage = `Please enter a value between ${numWithCommas(
        Number.parseInt(min)
      )} and ${numWithCommas(Number.parseInt(max))}.`;
    } else if (numValue < numMin || isNaN(numValue)) {
      validated = false;
      errorMessage = `Please enter a value between ${numWithCommas(
        Number.parseInt(min)
      )} and ${numWithCommas(Number.parseInt(max))}.`;
    }

    setErrors((prev) => ({ ...prev, [errorKey]: errorMessage }));
    return validated;
  };

  // Calculate results based on inputs
  const calculateResults = () => {
    // Validate inputs
    const targetAmtValid = validateRangeInput(
      removeCommas(targetAmount),
      "1000000000",
      "1",
      "targetAmount"
    );
    const durationValid = validateRangeInput(duration, "50", "1", "duration");
    const roiValid = validateRangeInput(
      rateOfReturn,
      "100",
      "1",
      "rateOfReturn"
    );

    if (!targetAmtValid || !durationValid || !roiValid) {
      setErrors((prev) => ({
        ...prev,
        general:
          "Please enter numeric inputs within the suggested range to get accurate results",
      }));
      return;
    }

    setErrors((prev) => ({ ...prev, general: "" }));

    const roi = Math.pow(1 + Number.parseFloat(rateOfReturn) / 100, 1 / 12) - 1;
    const timePeriods = Number.parseInt(duration) * 12;
    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));

    let amtValue = 0;
    let investVal = 0;
    let profit = 0;
    let profitPercent = 0;

    if (investmentType === "know-investment-amount") {
      if (investmentMode === "sip") {
        amtValue = futureValue(roi, timePeriods, -1 * targetAmtValue, 0, 1);
        investVal = targetAmtValue * timePeriods;
      } else if (investmentMode === "quarterly") {
        const intervals = Number.parseInt(duration) * 4;
        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);
        amtValue =
          (targetAmtValue * (Math.pow(1 + quarterlyRoi, intervals) - 1)) /
          quarterlyRoi;
        investVal = targetAmtValue * intervals;
      } else {
        // lumpsum
        amtValue =
          targetAmtValue *
          Math.pow(
            1 + Number.parseFloat(rateOfReturn) / 100,
            Number.parseInt(duration)
          );
        investVal = targetAmtValue;
      }

      profit = Math.round(amtValue) - Math.round(investVal);
      profitPercent = Math.round((profit / Math.round(investVal)) * 100);
      setGraphProgress((Math.round(profit) / Math.round(amtValue)) * 100);

      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));
      setTotalWealth(Math.round(amtValue));
      setMonthlyInvestment(targetAmtValue);
    } else if (investmentType === "know-target-amount") {
      if (investmentMode === "sip") {
        amtValue = PMT(
          Number.parseFloat(rateOfReturn) / (100 * 12),
          Number.parseInt(duration) * 12,
          0,
          -1 * targetAmtValue,
          1
        );
        investVal = amtValue * Number.parseInt(duration) * 12;
      } else if (investmentMode === "quarterly") {
        const intervals = Number.parseInt(duration) * 4;
        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);
        amtValue =
          targetAmtValue /
          ((Math.pow(1 + quarterlyRoi, intervals) - 1) / quarterlyRoi);
        investVal = amtValue * intervals;
      } else {
        // lumpsum
        amtValue = presentValue(
          Number.parseFloat(rateOfReturn) / 100,
          Number.parseInt(duration),
          0,
          -1 * targetAmtValue
        );
        investVal = amtValue;
      }

      profit = Math.round(targetAmtValue) - Math.round(investVal);
      profitPercent = Math.round((profit / Math.round(investVal)) * 100);
      setGraphProgress((Math.round(profit) / Math.round(targetAmtValue)) * 100);

      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));
      setMonthlyInvestment(Math.round(amtValue > 1 ? amtValue : 0));
    }

    setReturns(Math.round(amtValue < 1 ? profit - 1 : profit));
    setTotalWealth(targetAmtValue);
  };

  // Handle input changes
  const handleInputChange = (e, setter) => {
    const { value } = e.target;

    if (e.target.type === "text") {
      // For text inputs, update the value directly
      setter(value);
    } else if (e.target.type === "range") {
      // For range inputs, update the corresponding text input
      if (e.target.id === "ill_int_rates_value") {
        // Handle ROI slider specially
        const roiValue = roiArr.current[Number.parseInt(value)];
        setter(roiValue);
      } else {
        setter(value);
      }
    }
  };

  // Format target amount with commas
  const formatTargetAmount = (value) => {
    const numValue = Number.parseFloat(removeCommas(value));
    if (isNaN(numValue)) return "0";
    return numWithCommas(numValue);
  };

  // Calculate on input change
  useEffect(() => {
    calculateResults();
  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);

  // Get ROI slider value
  const getRoiSliderValue = () => {
    const index = roiArr.current.indexOf(rateOfReturn);
    return index >= 0 ? index : roiArr.current.indexOf("12.00");
  };

  // Get display text based on investment type and mode
  const getDisplayText = () => {
    if (investmentType === "know-target-amount") {
      if (investmentMode === "sip") {
        return {
          preText: "You will have to invest",
          postText: "per month to achieve your goal.",
        };
      } else if (investmentMode === "quarterly") {
        return {
          preText: "You will have to invest",
          postText: "quarterly to achieve your goal.",
        };
      } else {
        return {
          preText: "You will have to invest a lumpsum of",
          postText: "to achieve your goal.",
        };
      }
    } else {
      if (investmentMode === "sip") {
        return {
          preText: "You will earn",
          postText: "on your monthly investment.",
        };
      } else if (investmentMode === "quarterly") {
        return {
          preText: "You will earn",
          postText: "on your quarterly investment.",
        };
      } else {
        return {
          preText: "You will earn",
          postText: "on your lumpsum investment.",
        };
      }
    }
  };

  const { preText, postText } = getDisplayText();

  return (
    <section className="bg-[#fff]">
      <div className="s_wrapper">
        <div className="max-w-6xl mx-auto border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="flex flex-col md:flex-row">
            {/* Left side - Inputs */}
            <div className="w-full md:w-1/2 md:p-8 p-4 bg-white">
              {/* Investment Type */}
              <div className="mb-4">
                <label className="block text-black mb-2">I know my</label>
                <div className="flex gap-x-6 gap-y-2 flex-wrap">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="target_amount"
                      name="inv_type"
                      className="w-4 h-4 text-red-600 mr-2 accent-red-600"
                      value="know-target-amount"
                      checked={investmentType === "know-target-amount"}
                      onChange={() => setInvestmentType("know-target-amount")}
                    />
                    <label htmlFor="target_amount" className="text-black">
                      Target Amount
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="current-inv-amt"
                      name="inv_type"
                      className="w-4 h-4 text-red-600 mr-2 accent-red-600"
                      value="know-investment-amount"
                      checked={investmentType === "know-investment-amount"}
                      onChange={() =>
                        setInvestmentType("know-investment-amount")
                      }
                    />
                    <label htmlFor="current-inv-amt" className="text-black">
                      Current Investment Amount
                    </label>
                  </div>
                </div>
              </div>

              {/* Target Amount */}
              <div className="mb-4">
                <label className="block text-black mb-2">
                  {investmentType === "know-target-amount"
                    ? "My target amount is"
                    : "Investment Amount"}
                </label>
                <div className="relative mb-2">
                  <div className="flex items-center bg-gray-100 rounded p-2 w-full md:w-64">
                    <span className="text-black mr-1">₹</span>
                    <input
                      id="target_input_value"
                      value={targetAmount}
                      onChange={(e) => setTargetAmount(e.target.value)}
                      onBlur={() =>
                        setTargetAmount(formatTargetAmount(targetAmount))
                      }
                      maxLength={11}
                      type="text"
                      className="bg-transparent text-black border-none w-full focus:outline-none font-medium"
                    />
                  </div>
                </div>
                <div className="mb-1">
                  <input
                    id="target_amt"
                    type="range"
                    min="1"
                    max="1000000"
                    value={removeCommas(targetAmount)}
                    onChange={(e) => handleInputChange(e, setTargetAmount)}
                    className="w-full h-1  bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>₹ 1</span>
                  <span>₹ 10 Lakhs</span>
                </div>
                {errors.targetAmount && (
                  <p className="text-[#ac2629] text-xs mt-1">
                    {errors.targetAmount}
                  </p>
                )}
              </div>

              {/* Investment Mode */}
              <div className="mb-4">
                <label className="block text-black mb-2">
                  I want to invest
                </label>
                <div className="flex flex-wrap gap-6">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="sip_inv"
                      name="choice_investment"
                      className="w-4 h-4 text-red-600 mr-2 accent-red-600"
                      value="sip"
                      checked={investmentMode === "sip"}
                      onChange={() => setInvestmentMode("sip")}
                    />
                    <label htmlFor="sip_inv" className="text-black">
                      Monthly (SIP)
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="quarterly_inv"
                      name="choice_investment"
                      className="w-4 h-4 text-red-600 mr-2 accent-red-600"
                      value="quarterly"
                      checked={investmentMode === "quarterly"}
                      onChange={() => setInvestmentMode("quarterly")}
                    />
                    <label htmlFor="quarterly_inv" className="text-black">
                      Quarterly
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="lumsum_amt"
                      name="choice_investment"
                      className="w-4 h-4 text-red-600 mr-2 accent-red-600"
                      value="lumpsum"
                      checked={investmentMode === "lumpsum"}
                      onChange={() => setInvestmentMode("lumpsum")}
                    />
                    <label htmlFor="lumsum_amt" className="text-black">
                      At Once (Lumpsum)
                    </label>
                  </div>
                </div>
              </div>

              {/* Duration */}
              <div className="mb-4">
                <label className="block text-black mb-2">
                  for a duration of
                </label>
                <div className="relative mb-2">
                  <div className="flex items-center bg-gray-100 rounded p-2 w-full md:w-64">
                    <input
                      id="inv_ret_durations"
                      value={duration}
                      onChange={(e) => setDuration(e.target.value)}
                      maxLength={2}
                      type="text"
                      className="bg-transparent text-black border-none w-5 max-w-fit focus:outline-none font-medium"
                    />
                    <span className="text-black">Years</span>
                  </div>
                </div>
                <div className="mb-1">
                  <input
                    id="inv_ret_dur_value"
                    type="range"
                    min="1"
                    max="50"
                    value={duration}
                    onChange={(e) => handleInputChange(e, setDuration)}
                    className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>1 Year</span>
                  <span>50 Years</span>
                </div>
                {errors.duration && (
                  <p className="text-[#ac2629] text-xs mt-1">{errors.duration}</p>
                )}
              </div>

              {/* Rate of Return */}
              <div className="mb-4">
                <label className="block text-black mb-2">
                  At the Rate of return of
                </label>
                <div className="relative mb-2">
                  <div className="flex items-center bg-gray-100 rounded p-2 w-full md:w-64">
                    <input
                      id="ill_int_rates"
                      value={rateOfReturn}
                      onChange={(e) => setRateOfReturn(e.target.value)}
                      maxLength={5}
                      type="text"
                      className="bg-transparent text-black border-none w-10 focus:outline-none font-medium"
                    />
                    <span className="text-black">%</span>
                  </div>
                </div>
                <div className="mb-1">
                  <input
                    id="ill_int_rates_value"
                    type="range"
                    min="0"
                    max={roiArr.current.length - 1}
                    value={getRoiSliderValue()}
                    onChange={(e) => handleInputChange(e, setRateOfReturn)}
                    className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600"
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>1%</span>
                  <span>100%</span>
                </div>
                {errors.rateOfReturn && (
                  <p className="text-[#ac2629] text-xs mt-1">
                    {errors.rateOfReturn}
                  </p>
                )}
              </div>
            </div>

            {/* Right side - Results */}
            <div className="w-full md:w-1/2 md:p-8 p-4 bg-[#f0f0fa]">
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="text-black w-full md:w-1/2">
                  <div className="mb-4">
                    <p className="flex items-center text-sm font-medium">
                      <span className="inline-block w-3 h-3 bg-[#ae2f33] mr-2"></span>
                      Total amount invested
                    </p>
                    <p className="text-lg font-semibold ml-5">
                      ₹ {numWithCommas(investedAmount)}
                    </p>
                  </div>
                  <div className="mb-4">
                    <p className="flex items-center text-sm font-medium">
                      <span className="inline-block w-3 h-3 bg-[#101435] mr-2"></span>
                      Returns
                    </p>
                    <p className="text-lg font-semibold ml-5">
                      ₹ {numWithCommas(returns)}
                    </p>
                  </div>
                  <hr className="my-4 border-gray-300" />
                  <div>
                    <p className="text-sm font-medium">
                      Total wealth accumulated
                    </p>
                    <p className="text-lg font-semibold">
                      ₹{" "}
                      {numWithCommas(
                        Number.parseInt(removeCommas(targetAmount))
                      )}
                    </p>
                  </div>
                </div>

                <div className="mt-6 md:mt-0">
                  <CircularProgress progress={graphProgress} />
                </div>
              </div>

              {errors.general && (
                <p className="text-[#ae2f33] text-xs mt-6">{errors.general}</p>
              )}

              <div className="mt-6 md:mt-20">
                <div className="bg-[#ae2f33] text-white p-4 rounded-lg text-sm md:text-base text-center">
                  <p>
                    {preText}{" "}
                    <span className="font-medium text-sm md:text-base">
                      ₹{numWithCommas(monthlyInvestment)}
                    </span>{" "}
                    {postText}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
