{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/circular-progress.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CircularProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call CircularProgress() from the server but CircularProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/calculator/sip-calculator/circular-progress.jsx <module evaluation>\",\n    \"CircularProgress\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/calculator/sip-calculator/circular-progress.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/calculator/sip-calculator/circular-progress.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gGACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkU,GAC/V,gGACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/circular-progress.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CircularProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call CircularProgress() from the server but CircularProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/calculator/sip-calculator/circular-progress.jsx\",\n    \"CircularProgress\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/calculator/sip-calculator/circular-progress.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/calculator/sip-calculator/circular-progress.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,4EACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/SipCalculator.jsx"], "sourcesContent": ["\"use cliewn\"\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { CircularProgress } from \"./circular-progress\";\r\n\r\nexport default function SipCalculator() {\r\n  // State for form inputs\r\n  const [investmentType, setInvestmentType] = useState(\"know-target-amount\");\r\n  const [investmentMode, setInvestmentMode] = useState(\"sip\");\r\n  const [targetAmount, setTargetAmount] = useState(\"1500000\");\r\n  const [duration, setDuration] = useState(\"10\");\r\n  const [rateOfReturn, setRateOfReturn] = useState(\"12\");\r\n\r\n  // State for calculation results\r\n  const [investedAmount, setInvestedAmount] = useState(0);\r\n  const [returns, setReturns] = useState(0);\r\n  const [totalWealth, setTotalWealth] = useState(0);\r\n  const [monthlyInvestment, setMonthlyInvestment] = useState(0);\r\n  const [graphProgress, setGraphProgress] = useState(0);\r\n\r\n  // State for errors\r\n  const [errors, setErrors] = useState({\r\n    targetAmount: \"\",\r\n    duration: \"\",\r\n    rateOfReturn: \"\",\r\n    general: \"\",\r\n  });\r\n\r\n  // ROI array for slider\r\n  const roiArr = useRef([]);\r\n\r\n  // Initialize ROI array\r\n  useEffect(() => {\r\n    const tempRoiArr = [];\r\n    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {\r\n      tempRoiArr.push(Number.parseFloat(i).toFixed(2));\r\n    }\r\n    roiArr.current = tempRoiArr;\r\n  }, []);\r\n\r\n  // Format number with commas\r\n  const numWithCommas = (num) => {\r\n    return num.toLocaleString(\"en-IN\");\r\n  };\r\n\r\n  // Remove commas from number string\r\n  const removeCommas = (number) => {\r\n    return number.toString().replace(/,/g, \"\");\r\n  };\r\n\r\n  // Validate input range\r\n  const validateRangeInput = (value, max, min, field) => {\r\n    const numValue = Number.parseFloat(value);\r\n    if (isNaN(numValue)) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        [field]: \"Please enter a valid number\",\r\n      }));\r\n      return false;\r\n    }\r\n\r\n    if (numValue < Number.parseFloat(min)) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        [field]: `Value should be at least ${min}`,\r\n      }));\r\n      return false;\r\n    }\r\n\r\n    if (numValue > Number.parseFloat(max)) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        [field]: `Value should not exceed ${max}`,\r\n      }));\r\n      return false;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, [field]: \"\" }));\r\n    return true;\r\n  };\r\n\r\n  // PMT calculation function\r\n  const PMT = (rate, nper, pv, fv, type) => {\r\n    if (!fv) fv = 0;\r\n    if (!type) type = 0;\r\n\r\n    if (rate === 0) return -(pv + fv) / nper;\r\n\r\n    const pvif = Math.pow(1 + rate, nper);\r\n    let pmt = (rate / (pvif - 1)) * -(pv * pvif + fv);\r\n\r\n    if (type === 1) pmt = pmt / (1 + rate);\r\n\r\n    return pmt;\r\n  };\r\n\r\n  // Future value calculation\r\n  const futureValue = (rate, nper, pmt, pv, type) => {\r\n    if (!pv) pv = 0;\r\n    if (!type) type = 0;\r\n\r\n    const pow = Math.pow(1 + rate, nper);\r\n    let fv;\r\n\r\n    if (rate) {\r\n      fv = (pmt * (1 + rate * type) * (pow - 1)) / rate + pv * pow;\r\n    } else {\r\n      fv = -1 * (pv + pmt * nper);\r\n    }\r\n\r\n    return fv;\r\n  };\r\n\r\n  // Present value calculation\r\n  const presentValue = (rate, nper, pmt, fv, type) => {\r\n    if (!fv) fv = 0;\r\n    if (!type) type = 0;\r\n\r\n    const pow = Math.pow(1 + rate, nper);\r\n    let pv;\r\n\r\n    if (rate) {\r\n      pv = (-pmt * (1 + rate * type) * (pow - 1)) / (rate * pow) - fv / pow;\r\n    } else {\r\n      pv = -1 * (fv + pmt * nper);\r\n    }\r\n\r\n    return pv;\r\n  };\r\n\r\n  // Calculate results based on inputs\r\n  const calculateResults = () => {\r\n    // Validate inputs\r\n    const targetAmtValid = validateRangeInput(\r\n      removeCommas(targetAmount),\r\n      \"1000000000\",\r\n      \"1\",\r\n      \"targetAmount\"\r\n    );\r\n    const durationValid = validateRangeInput(duration, \"50\", \"1\", \"duration\");\r\n    const roiValid = validateRangeInput(\r\n      rateOfReturn,\r\n      \"100\",\r\n      \"1\",\r\n      \"rateOfReturn\"\r\n    );\r\n\r\n    if (!targetAmtValid || !durationValid || !roiValid) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        general:\r\n          \"Please enter numeric inputs within the suggested range to get accurate results\",\r\n      }));\r\n      return;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, general: \"\" }));\r\n\r\n    let amtValue = 0;\r\n    let investVal = 0;\r\n    let profit = 0;\r\n    let profitPercent = 0;\r\n    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));\r\n    const roi = Number.parseFloat(rateOfReturn) / (100 * 12);\r\n    const timePeriods = Number.parseInt(duration) * 12;\r\n\r\n    if (investmentType === \"know-investment-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        amtValue = futureValue(roi, timePeriods, -1 * targetAmtValue, 0, 1);\r\n        investVal = targetAmtValue * timePeriods;\r\n      } else if (investmentMode === \"quarterly\") {\r\n        const intervals = Number.parseInt(duration) * 4;\r\n        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);\r\n        amtValue =\r\n          (targetAmtValue * (Math.pow(1 + quarterlyRoi, intervals) - 1)) /\r\n          quarterlyRoi;\r\n        investVal = targetAmtValue * intervals;\r\n      } else {\r\n        // lumpsum\r\n        amtValue =\r\n          targetAmtValue *\r\n          Math.pow(\r\n            1 + Number.parseFloat(rateOfReturn) / 100,\r\n            Number.parseInt(duration)\r\n          );\r\n        investVal = targetAmtValue;\r\n      }\r\n\r\n      profit = Math.round(amtValue) - Math.round(investVal);\r\n      profitPercent = Math.round((profit / Math.round(investVal)) * 100);\r\n      setGraphProgress((Math.round(profit) / Math.round(amtValue)) * 100);\r\n\r\n      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));\r\n      setTotalWealth(Math.round(amtValue));\r\n      setMonthlyInvestment(targetAmtValue);\r\n    } else if (investmentType === \"know-target-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        amtValue = PMT(\r\n          Number.parseFloat(rateOfReturn) / (100 * 12),\r\n          Number.parseInt(duration) * 12,\r\n          0,\r\n          -1 * targetAmtValue,\r\n          1\r\n        );\r\n        investVal = amtValue * Number.parseInt(duration) * 12;\r\n      } else if (investmentMode === \"quarterly\") {\r\n        const intervals = Number.parseInt(duration) * 4;\r\n        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);\r\n        amtValue =\r\n          targetAmtValue /\r\n          ((Math.pow(1 + quarterlyRoi, intervals) - 1) / quarterlyRoi);\r\n        investVal = amtValue * intervals;\r\n      } else {\r\n        // lumpsum\r\n        amtValue = presentValue(\r\n          Number.parseFloat(rateOfReturn) / 100,\r\n          Number.parseInt(duration),\r\n          0,\r\n          -1 * targetAmtValue\r\n        );\r\n        investVal = amtValue;\r\n      }\r\n\r\n      profit = Math.round(targetAmtValue) - Math.round(investVal);\r\n      profitPercent = Math.round((profit / Math.round(investVal)) * 100);\r\n      setGraphProgress((Math.round(profit) / Math.round(targetAmtValue)) * 100);\r\n\r\n      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));\r\n      setMonthlyInvestment(Math.round(amtValue > 1 ? amtValue : 0));\r\n    }\r\n\r\n    setReturns(Math.round(amtValue < 1 ? profit - 1 : profit));\r\n    setTotalWealth(targetAmtValue);\r\n  };\r\n\r\n  // Handle input changes\r\n  const handleInputChange = (e, setter) => {\r\n    const { value } = e.target;\r\n\r\n    if (e.target.type === \"text\") {\r\n      // For text inputs, update the value directly\r\n      setter(value);\r\n    } else if (e.target.type === \"range\") {\r\n      // For range inputs, update the corresponding text input\r\n      if (e.target.id === \"ill_int_rates_value\") {\r\n        // Handle ROI slider specially\r\n        const roiValue = roiArr.current[Number.parseInt(value)];\r\n        setter(roiValue);\r\n      } else {\r\n        setter(value);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Format target amount with commas\r\n  const formatTargetAmount = (value) => {\r\n    const numValue = Number.parseFloat(removeCommas(value));\r\n    if (isNaN(numValue)) return \"0\";\r\n    return numWithCommas(numValue);\r\n  };\r\n\r\n  // Calculate on input change\r\n  useEffect(() => {\r\n    calculateResults();\r\n  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);\r\n\r\n  // Get ROI slider value\r\n  const getRoiSliderValue = () => {\r\n    const index = roiArr.current.indexOf(rateOfReturn);\r\n    return index >= 0 ? index : roiArr.current.indexOf(\"12.00\");\r\n  };\r\n\r\n  // Get text for result display\r\n  const getResultText = () => {\r\n    let preText = \"\";\r\n    let postText = \"\";\r\n\r\n    if (investmentType === \"know-target-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        preText = \"Invest\";\r\n        postText = \"every month to reach your target amount\";\r\n      } else if (investmentMode === \"quarterly\") {\r\n        preText = \"Invest\";\r\n        postText = \"every quarter to reach your target amount\";\r\n      } else {\r\n        preText = \"Make a one-time investment of\";\r\n        postText = \"to reach your target amount\";\r\n      }\r\n    } else {\r\n      if (investmentMode === \"sip\") {\r\n        preText = \"Your monthly investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      } else if (investmentMode === \"quarterly\") {\r\n        preText = \"Your quarterly investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      } else {\r\n        preText = \"Your one-time investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      }\r\n    }\r\n\r\n    return { preText, postText };\r\n  };\r\n\r\n  const { preText, postText } = getResultText();\r\n\r\n  return (\r\n    <section className=\"bg-[#fff] py-8\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <h1 className=\"text-3xl font-bold text-[#2e4765] mb-6\">\r\n          SIP Calculator: Systematic Investment Plan Calculator Online\r\n        </h1>\r\n\r\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\r\n          <div className=\"flex flex-col lg:flex-row\">\r\n            {/* Left side - Inputs */}\r\n            <div className=\"w-full lg:w-1/2 p-6\">\r\n              {/* Investment Mode Tabs */}\r\n              <div className=\"flex mb-6\">\r\n                <button\r\n                  className={`px-4 py-2 rounded-l-md ${\r\n                    investmentMode === \"sip\"\r\n                      ? \"bg-[#f47321] text-white\"\r\n                      : \"bg-gray-100\"\r\n                  }`}\r\n                  onClick={() => setInvestmentMode(\"sip\")}\r\n                >\r\n                  SIP\r\n                </button>\r\n                <button\r\n                  className={`px-4 py-2 ${\r\n                    investmentMode === \"lumpsum\"\r\n                      ? \"bg-[#f47321] text-white\"\r\n                      : \"bg-gray-100\"\r\n                  }`}\r\n                  onClick={() => setInvestmentMode(\"lumpsum\")}\r\n                >\r\n                  Lumpsum\r\n                </button>\r\n              </div>\r\n\r\n              {/* Amount Input */}\r\n              <div className=\"mb-6\">\r\n                <label className=\"block text-gray-700 mb-2\">Amount</label>\r\n                <div className=\"relative\">\r\n                  <span className=\"absolute left-3 top-3 text-gray-500\">₹</span>\r\n                  <input\r\n                    type=\"text\"\r\n                    className=\"w-full p-2 pl-8 border rounded-md\"\r\n                    value={formatTargetAmount(targetAmount)}\r\n                    onChange={(e) => handleInputChange(e, setTargetAmount)}\r\n                  />\r\n                </div>\r\n                {errors.targetAmount && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">\r\n                    {errors.targetAmount}\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"500\"\r\n                    max=\"2000000\"\r\n                    step=\"500\"\r\n                    value={removeCommas(targetAmount)}\r\n                    onChange={(e) => handleInputChange(e, setTargetAmount)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>₹ 500</span>\r\n                    <span>₹ 2,00,000</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Expected Return Rate */}\r\n              <div className=\"mb-6\">\r\n                <label className=\"block text-gray-700 mb-2\">\r\n                  Expected Return Rate (p.a)\r\n                </label>\r\n                <div className=\"relative\">\r\n                  <input\r\n                    type=\"text\"\r\n                    className=\"w-full p-2 border rounded-md\"\r\n                    value={rateOfReturn}\r\n                    onChange={(e) => handleInputChange(e, setRateOfReturn)}\r\n                  />\r\n                  <span className=\"absolute right-3 top-3 text-gray-500\">\r\n                    %\r\n                  </span>\r\n                </div>\r\n                {errors.rateOfReturn && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">\r\n                    {errors.rateOfReturn}\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"5\"\r\n                    max=\"30\"\r\n                    step=\"0.5\"\r\n                    value={rateOfReturn}\r\n                    onChange={(e) => handleInputChange(e, setRateOfReturn)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>5%</span>\r\n                    <span>30%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* SIP Time Period */}\r\n              <div className=\"mb-6\">\r\n                <label className=\"block text-gray-700 mb-2\">\r\n                  SIP time period\r\n                </label>\r\n                <div className=\"relative\">\r\n                  <input\r\n                    type=\"text\"\r\n                    className=\"w-full p-2 border rounded-md\"\r\n                    value={duration}\r\n                    onChange={(e) => handleInputChange(e, setDuration)}\r\n                  />\r\n                  <span className=\"absolute right-3 top-3 text-gray-500\">\r\n                    Years\r\n                  </span>\r\n                </div>\r\n                {errors.duration && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">{errors.duration}</p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"1\"\r\n                    max=\"40\"\r\n                    step=\"1\"\r\n                    value={duration}\r\n                    onChange={(e) => handleInputChange(e, setDuration)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#f47321]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>0 years</span>\r\n                    <span>40 years</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side - Results */}\r\n            <div className=\"w-full lg:w-1/2 p-6 bg-gray-50\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                <div className=\"w-48 h-48 relative mb-4\">\r\n                  <CircularProgress progress={graphProgress} />\r\n                </div>\r\n\r\n                <div className=\"text-center\">\r\n                  <p className=\"text-gray-600 mb-1\">\r\n                    {investmentMode === \"sip\"\r\n                      ? \"SIP per month\"\r\n                      : \"Investment amount\"}\r\n                  </p>\r\n                  <p className=\"text-2xl font-bold\">\r\n                    ₹ {numWithCommas(monthlyInvestment)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-4 h-4 bg-[#f47321] mr-3\"></div>\r\n                  <div>\r\n                    <p className=\"text-gray-600 text-sm\">Invested amount</p>\r\n                    <p className=\"font-semibold\">\r\n                      ₹ {numWithCommas(investedAmount)}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-4 h-4 bg-[#ae2f33] mr-3\"></div>\r\n                  <div>\r\n                    <p className=\"text-gray-600 text-sm\">Est. returns</p>\r\n                    <p className=\"font-semibold\">₹ {numWithCommas(returns)}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-8 p-4 bg-gray-100 rounded-lg\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <p className=\"text-gray-600\">Total amount:</p>\r\n                  <p className=\"text-2xl font-bold text-[#2e4765]\">\r\n                    ₹ {numWithCommas(totalWealth)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {errors.general && (\r\n                <div className=\"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n                  {errors.general}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"mt-6 text-center text-sm text-gray-600\">\r\n                <p>\r\n                  {preText}{\" \"}\r\n                  <span className=\"font-semibold\">\r\n                    ₹ {numWithCommas(monthlyInvestment)}\r\n                  </span>{\" \"}\r\n                  {postText}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Additional Information Section */}\r\n        <div className=\"mt-8 bg-white rounded-lg shadow-md p-6\">\r\n          <h2 className=\"text-2xl font-bold text-[#2e4765] mb-4\">\r\n            What is SIP Calculator?\r\n          </h2>\r\n          <div className=\"prose max-w-none\">\r\n            <p className=\"text-gray-700 mb-4\">\r\n              A SIP (Systematic Investment Plan) calculator is a financial tool\r\n              that helps you estimate the future value of your mutual fund\r\n              investments made through SIP. It calculates the potential returns\r\n              based on your monthly investment amount, investment duration, and\r\n              expected rate of return.\r\n            </p>\r\n\r\n            <h3 className=\"text-xl font-semibold text-[#2e4765] mb-3\">\r\n              How does SIP Calculator work?\r\n            </h3>\r\n            <p className=\"text-gray-700 mb-4\">\r\n              The SIP calculator uses the compound interest formula to calculate\r\n              the maturity amount. It considers the power of compounding, where\r\n              your returns also earn returns over time. The calculator takes\r\n              into account:\r\n            </p>\r\n            <ul className=\"list-disc list-inside text-gray-700 mb-4 space-y-2\">\r\n              <li>Monthly investment amount</li>\r\n              <li>Investment duration (in years)</li>\r\n              <li>Expected annual rate of return</li>\r\n              <li>Frequency of investment (monthly, quarterly, or lumpsum)</li>\r\n            </ul>\r\n\r\n            <h3 className=\"text-xl font-semibold text-[#2e4765] mb-3\">\r\n              Benefits of using SIP Calculator\r\n            </h3>\r\n            <ul className=\"list-disc list-inside text-gray-700 mb-4 space-y-2\">\r\n              <li>Plan your financial goals effectively</li>\r\n              <li>Understand the power of compounding</li>\r\n              <li>Compare different investment scenarios</li>\r\n              <li>Make informed investment decisions</li>\r\n              <li>Track your wealth creation journey</li>\r\n            </ul>\r\n\r\n            <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4 mt-6\">\r\n              <p className=\"text-blue-800\">\r\n                <strong>Disclaimer:</strong> The calculations provided by this\r\n                SIP calculator are for illustrative purposes only. Actual\r\n                returns may vary based on market conditions and fund\r\n                performance. Please consult with a financial advisor before\r\n                making investment decisions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gCAAgC;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,cAAc;QACd,UAAU;QACV,cAAc;QACd,SAAS;IACX;IAEA,uBAAuB;IACvB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAExB,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,EAAE;QACrB,IAAK,IAAI,IAAI,GAAG,OAAO,UAAU,CAAC,EAAE,OAAO,CAAC,MAAM,QAAQ,KAAK,KAAM;YACnE,WAAW,IAAI,CAAC,OAAO,UAAU,CAAC,GAAG,OAAO,CAAC;QAC/C;QACA,OAAO,OAAO,GAAG;IACnB,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,OAAO,IAAI,cAAc,CAAC;IAC5B;IAEA,mCAAmC;IACnC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,QAAQ,GAAG,OAAO,CAAC,MAAM;IACzC;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,OAAO,KAAK,KAAK;QAC3C,MAAM,WAAW,OAAO,UAAU,CAAC;QACnC,IAAI,MAAM,WAAW;YACnB,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;YACD,OAAO;QACT;QAEA,IAAI,WAAW,OAAO,UAAU,CAAC,MAAM;YACrC,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE,CAAC,yBAAyB,EAAE,KAAK;gBAC5C,CAAC;YACD,OAAO;QACT;QAEA,IAAI,WAAW,OAAO,UAAU,CAAC,MAAM;YACrC,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE,CAAC,wBAAwB,EAAE,KAAK;gBAC3C,CAAC;YACD,OAAO;QACT;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAG,CAAC;QAC7C,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,MAAM,CAAC,MAAM,MAAM,IAAI,IAAI;QAC/B,IAAI,CAAC,IAAI,KAAK;QACd,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,SAAS,GAAG,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI;QAEpC,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,MAAM;QAChC,IAAI,MAAM,AAAC,OAAO,CAAC,OAAO,CAAC,IAAK,CAAC,CAAC,KAAK,OAAO,EAAE;QAEhD,IAAI,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,IAAI;QAErC,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC,MAAM,MAAM,KAAK,IAAI;QACxC,IAAI,CAAC,IAAI,KAAK;QACd,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM;QAC/B,IAAI;QAEJ,IAAI,MAAM;YACR,KAAK,AAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAK,OAAO,KAAK;QAC3D,OAAO;YACL,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI;QAC5B;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,eAAe,CAAC,MAAM,MAAM,KAAK,IAAI;QACzC,IAAI,CAAC,IAAI,KAAK;QACd,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM;QAC/B,IAAI;QAEJ,IAAI,MAAM;YACR,KAAK,AAAC,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,GAAG,IAAI,KAAK;QACpE,OAAO;YACL,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI;QAC5B;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,MAAM,iBAAiB,mBACrB,aAAa,eACb,cACA,KACA;QAEF,MAAM,gBAAgB,mBAAmB,UAAU,MAAM,KAAK;QAC9D,MAAM,WAAW,mBACf,cACA,OACA,KACA;QAGF,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,UAAU;YAClD,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,SACE;gBACJ,CAAC;YACD;QACF;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAG,CAAC;QAE7C,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,gBAAgB;QACpB,MAAM,iBAAiB,OAAO,UAAU,CAAC,aAAa;QACtD,MAAM,MAAM,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE;QACvD,MAAM,cAAc,OAAO,QAAQ,CAAC,YAAY;QAEhD,IAAI,mBAAmB,0BAA0B;YAC/C,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,YAAY,KAAK,aAAa,CAAC,IAAI,gBAAgB,GAAG;gBACjE,YAAY,iBAAiB;YAC/B,OAAO,IAAI,mBAAmB,aAAa;gBACzC,MAAM,YAAY,OAAO,QAAQ,CAAC,YAAY;gBAC9C,MAAM,eAAe,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAC/D,WACE,AAAC,iBAAiB,CAAC,KAAK,GAAG,CAAC,IAAI,cAAc,aAAa,CAAC,IAC5D;gBACF,YAAY,iBAAiB;YAC/B,OAAO;gBACL,UAAU;gBACV,WACE,iBACA,KAAK,GAAG,CACN,IAAI,OAAO,UAAU,CAAC,gBAAgB,KACtC,OAAO,QAAQ,CAAC;gBAEpB,YAAY;YACd;YAEA,SAAS,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC;YAC3C,gBAAgB,KAAK,KAAK,CAAC,AAAC,SAAS,KAAK,KAAK,CAAC,aAAc;YAC9D,iBAAiB,AAAC,KAAK,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,YAAa;YAE/D,kBAAkB,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI;YACjD,eAAe,KAAK,KAAK,CAAC;YAC1B,qBAAqB;QACvB,OAAO,IAAI,mBAAmB,sBAAsB;YAClD,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,IACT,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAC3C,OAAO,QAAQ,CAAC,YAAY,IAC5B,GACA,CAAC,IAAI,gBACL;gBAEF,YAAY,WAAW,OAAO,QAAQ,CAAC,YAAY;YACrD,OAAO,IAAI,mBAAmB,aAAa;gBACzC,MAAM,YAAY,OAAO,QAAQ,CAAC,YAAY;gBAC9C,MAAM,eAAe,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAC/D,WACE,iBACA,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,cAAc,aAAa,CAAC,IAAI,YAAY;gBAC7D,YAAY,WAAW;YACzB,OAAO;gBACL,UAAU;gBACV,WAAW,aACT,OAAO,UAAU,CAAC,gBAAgB,KAClC,OAAO,QAAQ,CAAC,WAChB,GACA,CAAC,IAAI;gBAEP,YAAY;YACd;YAEA,SAAS,KAAK,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC;YACjD,gBAAgB,KAAK,KAAK,CAAC,AAAC,SAAS,KAAK,KAAK,CAAC,aAAc;YAC9D,iBAAiB,AAAC,KAAK,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,kBAAmB;YAErE,kBAAkB,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI;YACjD,qBAAqB,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW;QAC5D;QAEA,WAAW,KAAK,KAAK,CAAC,WAAW,IAAI,SAAS,IAAI;QAClD,eAAe;IACjB;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC,GAAG;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAE1B,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC5B,6CAA6C;YAC7C,OAAO;QACT,OAAO,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,SAAS;YACpC,wDAAwD;YACxD,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,uBAAuB;gBACzC,8BAA8B;gBAC9B,MAAM,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,CAAC,OAAO;gBACvD,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,OAAO,UAAU,CAAC,aAAa;QAChD,IAAI,MAAM,WAAW,OAAO;QAC5B,OAAO,cAAc;IACvB;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;QAAU;QAAc;QAAgB;KAAe;IAEzE,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;QACrC,OAAO,SAAS,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;IACrD;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI,UAAU;QACd,IAAI,WAAW;QAEf,IAAI,mBAAmB,sBAAsB;YAC3C,IAAI,mBAAmB,OAAO;gBAC5B,UAAU;gBACV,WAAW;YACb,OAAO,IAAI,mBAAmB,aAAa;gBACzC,UAAU;gBACV,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;QACF,OAAO;YACL,IAAI,mBAAmB,OAAO;gBAC5B,UAAU;gBACV,WAAW;YACb,OAAO,IAAI,mBAAmB,aAAa;gBACzC,UAAU;gBACV,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;QACF;QAEA,OAAO;YAAE;YAAS;QAAS;IAC7B;IAEA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IAE9B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAIvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,uBAAuB,EACjC,mBAAmB,QACf,4BACA,eACJ;gDACF,SAAS,IAAM,kBAAkB;0DAClC;;;;;;0DAGD,8OAAC;gDACC,WAAW,CAAC,UAAU,EACpB,mBAAmB,YACf,4BACA,eACJ;gDACF,SAAS,IAAM,kBAAkB;0DAClC;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA2B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;kEACtD,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,mBAAmB;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;;;;;;;4CAGzC,OAAO,YAAY,kBAClB,8OAAC;gDAAE,WAAU;0DACV,OAAO,YAAY;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,aAAa;wDACpB,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA2B;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;kEAExC,8OAAC;wDAAK,WAAU;kEAAuC;;;;;;;;;;;;4CAIxD,OAAO,YAAY,kBAClB,8OAAC;gDAAE,WAAU;0DACV,OAAO,YAAY;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA2B;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;kEAExC,8OAAC;wDAAK,WAAU;kEAAuC;;;;;;;;;;;;4CAIxD,OAAO,QAAQ,kBACd,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ;;;;;;0DAG3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6KAAA,CAAA,mBAAgB;oDAAC,UAAU;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,mBAAmB,QAChB,kBACA;;;;;;kEAEN,8OAAC;wDAAE,WAAU;;4DAAqB;4DAC7B,cAAc;;;;;;;;;;;;;;;;;;;kDAKvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;;oEAAgB;oEACxB,cAAc;;;;;;;;;;;;;;;;;;;0DAKvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;;oEAAgB;oEAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;;wDAAoC;wDAC5C,cAAc;;;;;;;;;;;;;;;;;;oCAKtB,OAAO,OAAO,kBACb,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO;;;;;;kDAInB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;gDACE;gDAAS;8DACV,8OAAC;oDAAK,WAAU;;wDAAgB;wDAC3B,cAAc;;;;;;;gDACX;gDACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAQlC,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAMlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/SipCalculatorMaster.jsx"], "sourcesContent": ["import React from 'react'\r\nimport SipCalculator from './SipCalculator'\r\nimport Banner from '@/components/ui/reusable/banner/Banner'\r\n\r\nconst SipCalculatorMaster = () => {\r\n  return (\r\n    <div>\r\n        <Banner\r\n        title=\"SIP Calculator\"\r\n        imageUrl=\"/images/calculator/calculator-banner.jpeg\"\r\n        subtitle=\"\"\r\n      />\r\n      <SipCalculator/>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default SipCalculatorMaster\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,sBAAsB;IAC1B,qBACE,8OAAC;;0BACG,8OAAC,wJAAA,CAAA,UAAM;gBACP,OAAM;gBACN,UAAS;gBACT,UAAS;;;;;;0BAEX,8OAAC,sKAAA,CAAA,UAAa;;;;;;;;;;;AAGpB;uCAEe", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/app/calculator/sip-calculator/page.jsx"], "sourcesContent": ["import SipCalculatorMaster from '@/components/calculator/sip-calculator/SipCalculatorMaster'\r\nimport Head from 'next/head'\r\nimport React from 'react'\r\n\r\nconst page = () => {\r\n  return (\r\n    <>\r\n    <Head>\r\n        <title>SIP Calculator | Plan Your Mutual Fund Investments | Winshine</title>\r\n        <meta name=\"description\" content=\"Use Winshine’s SIP Calculator to estimate the future value of your mutual fund investments. Plan your wealth creation journey with accurate and easy projections.\" />\r\n        <meta name=\"keywords\" content=\"SIP calculator, mutual fund calculator, investment calculator, monthly investment planning, wealth creation, Winshine SIP tool\" />\r\n        <meta name=\"author\" content=\"Winshine Financial Services\" />\r\n        <meta property=\"og:title\" content=\"SIP Calculator | Mutual Fund Investment Planner | Winshine\" />\r\n        <meta property=\"og:description\" content=\"Estimate returns on your SIP investments with our easy-to-use calculator. Winshine helps you make smarter, ethical investment decisions.\" />\r\n        <meta property=\"og:image\" content=\"https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg\" /> \r\n        <meta property=\"og:url\" content=\"https://winshine.nipralo.com/calculator/sip-calculator\" />\r\n        <meta name=\"twitter:card\" content=\"https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg\" />\r\n        <link rel=\"canonical\" href=\"https://winshine.nipralo.com/calculator/sip-calculator\" />\r\n      </Head>\r\n    <SipCalculatorMaster/>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default page\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO;IACX,qBACE;;0BACA,8OAAC,oKAAA,CAAA,UAAI;;kCACD,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,UAAS;wBAAiB,SAAQ;;;;;;kCACxC,8OAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,UAAS;wBAAS,SAAQ;;;;;;kCAChC,8OAAC;wBAAK,MAAK;wBAAe,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;;;;;;;0BAE/B,8OAAC,4KAAA,CAAA,UAAmB;;;;;;;AAGxB;uCAEe", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA;wBAEzG,YAAA;4BAAA;4BAAA,CACA,kCAD4D;4BAC5D,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kCAChDY,QAAAA,CAAAA,CAAY;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACVC,MAAMZ,UAAUa,QAAQ;;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACV,2CAA2C;sBAC3CC,IAAAA,CAAAA;YAAAA;SAAAA,CAAY;;SACZC,UAAU;cACVC,IAAAA;YAAAA,CAAU,EAAE,GAAA;gBACd,OAAA,QAAA;wBAAA;4BACAC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,CAAU,qBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACRC,OAAAA,GAAAA,6SAAAA,CAAAA,EAAYnB,QAAAA,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,MAAAA,EAAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACA;qBAAA", "ignoreList": [0], "debugId": null}}]}