"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import ReactMarkdown from "react-markdown";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import "./SingleBlogMaster.css";

const SingleBlogMaster = () => {
  const [blogs, setBlogs] = useState();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState();

  const { slug } = useParams();
  const blogId = slug;
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/blogs?filters[slug][$eq]=${blogId}&populate[cover][fields]=url`,
          {
            headers: {
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const data = await response.json();
        const blog = data?.data?.[0];
        setBlogs(blog);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (blogId) fetchBlogs();
  }, [blogId]);

  if (loading)
    return (
      <div className="text-black h-[50vh] w-screen flex justify-center items-center bg-white">
        <p>Loading...</p>
      </div>
    );
  if (error)
    return (
      <div className="text-red-600 h-[50vh] w-screen flex justify-center items-center bg-white">
        <p>Error: {error}</p>
      </div>
    );
  if (!blogs)
    return (
      <div className="text-black h-[50vh] w-screen flex justify-center items-center bg-white">
        <p>No blog found</p>
      </div>
    );

  function formatDate(dateInput) {
    const date = new Date(dateInput);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }

  return (
    <>
      {blogs && (
        <section className="bg-white">
          <div className="s_wrapper">
            <div className="text-black bg-white">
              {/* <img
                src={blogs?.cover?.url}
                alt={blogs?.title}
                className="rounded-2xl max-h-52 sm:max-h-64 md:max-h-96 lg:max-h-[480px] lg:rounded-3xl mb-6 lg:mb-12 w-full object-cover "
              /> */}
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4 text-[#040404]">
                <SkewFadeInWords text={blogs?.title} />
              </h2>
              <p className="text-gray-500 mb-6">
                {formatDate(blogs?.published_on)}
              </p>
              <ReactMarkdown
                components={{
                  p: ({ node, ...props }) => (
                    <p className="my-paragraph !text-[#333] my-2" {...props} />
                  ),
                  h1: ({ node, ...props }) => (
                    <p
                      className="!text-[#000] !font-bold !text-2xl my-2"
                      {...props}
                    />
                  ),
                  h2: ({ node, ...props }) => (
                    <p
                      className="!text-[#000] !font-bold !text-xl my-2"
                      {...props}
                    />
                  ),
                  h3: ({ node, ...props }) => (
                    <p
                      className="!text-[#000] !font-bold !text-lg my-2"
                      {...props}
                    />
                  ),
                  h4: ({ node, ...props }) => (
                    <p
                      className="!text-[#000] !font-bold !text-lg my-2"
                      {...props}
                    />
                  ),
                  a: ({ node, ...props }) => (
                    <a className="!text-blue-500 !underline" {...props} />
                  ),
                  ul: ({ node, ...props }) => (
                    <ul className="!my-2 !text-[#333]" {...props} />
                  ),
                  li: ({ node, ...props }) => (
                    <li className="!ml-2 custom-list !text-[#333]" {...props} />
                  ),
                }}
              >
                {blogs?.description}
              </ReactMarkdown>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default SingleBlogMaster;
