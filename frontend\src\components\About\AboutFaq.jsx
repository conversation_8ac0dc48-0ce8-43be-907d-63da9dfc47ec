"use client";
// 
import React, { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";

const faqList = [
  {
    question: "Decades of Industry Experience",
    answer:
      "With over 30 years in the financial services industry, Winshine brings a wealth of experience and expertise to the table. Our longevity reflects our commitment to building lasting relationships and providing reliable financial guidance.",
  },
  {
    question: "Experienced and Stable Team",
    answer:
      "Our team comprises dedicated professionals with decades of collective experience, fostering a stable and trusted environment for our clients. This longevity builds a strong rapport and ensures enduring client-advisor relationships.",
  },
  {
    question: "Diverse Financial Products",
    answer:
      "We prioritize our client's needs by recommending suitable financial products from a diverse range of options beyond stocks, ensuring a holistic approach to financial planning.",
  },
  {
    question: "Ethics, Transparency, and Impeccable Service",
    answer:
      "At Winshine, we uphold the highest standards of ethics, transparency, and professionalism. Our commitment to absolute transparency ensures that clients have a clear understanding of every financial decision and transaction. We go above and beyond to provide impeccable service that exceeds industry norms.",
  },
  {
    question: "One-Stop Investment Solution",
    answer:
      "From equity to insurance, commodities to mutual funds, we’ve got your financial aspirations covered. Experience the convenience and peace of mind of managing your diverse investments under one roof, with cost-efficient solutions, personalised service, and streamlined communication for holistic financial planning.",
  },
  {
    question: "Personalized Service",
    answer:
      "We understand that each client has unique financial goals and needs. With a dedicated team comprising experienced dealers, back office staff, and a managing partner, we ensure that every client receives personalized attention and tailored financial solutions.",
  },
  {
    question: "Real-Time Market Updates",
    answer:
      "Stay ahead of the curve with real-time market updates and convenient execution of trades. Our seamless trading experience ensures that clients can capitalize on market opportunities swiftly and efficiently.",
  },
  {
    question: "IPO, Rights issue and Buyback Assistance.",
    answer:
      "From IPO subscriptions to rights issues and buybacks, we offer comprehensive support, guiding clients through every step of the process. Our expertise ensures informed investment decisions and maximised returns.",
  },
];

const AboutFaq = () => {
  const [openIndex, setOpenIndex] = useState(``);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  return (
    <div>
      {/* <Faq  items={faqList}/> */}
      <section className="w-full bg-white text-black ">
        <div className="s_wrapper">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4">
              <SkewFadeInWords text="Here's What Makes Us Unique" />
            </h2>
            {/* <p className="text-gray-600">
              Have doubts? We're here to help. Explore common questions below or
              reach out to us directly.
            </p> */}
          </div>
          <div className="mx-auto flex flex-col md:flex-row md:gap-12">
            <div className="space-y-6 text-left md:basis-[50%]">
              {faqList
                .slice(0, Math.ceil(faqList.length / 2))
                .map((faq, index) => (
                  <div
                    key={index}
                    className={`border-b pb-4 border-gray-300 ${
                      index === 0 ? "border-t pt-4" : "border-b"
                    }`}
                  >
                    <button
                      onClick={() => toggleFAQ(index)}
                      className="w-full text-left text-lg font-medium cursor-pointer flex justify-between items-center"
                    >
                      {faq.question}
                      <span className="ml-4 text-xl">
                        {openIndex === index ? "−" : "+"}
                      </span>
                    </button>

                    <AnimatePresence initial={false}>
                      {openIndex === index && (
                        <motion.div
                          key="content"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          style={{ overflow: "hidden" }}
                        >
                          <p className="mt-3 text-gray-600  text-justify md:text-start">{faq.answer}</p>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
            </div>
            {/* Right Side */}
            <div className="space-y-6 text-left md:basis-[50%]">
              {faqList
                .slice(Math.ceil(faqList.length / 2))
                .map((faq, index) => {
                  const actualIndex = index + Math.ceil(faqList.length / 2);
                  return (
                    <div
                      key={index}
                      className={`border-b pb-4 border-gray-300 ${
                        index === 0 ? "md:border-t pt-4" : "border-b"
                      }`}
                    >
                      <button
                        onClick={() => toggleFAQ(actualIndex)}
                        className="w-full text-left text-lg font-medium cursor-pointer flex justify-between items-center"
                      >
                        {faq.question}
                        <span className="ml-4 text-xl">
                          {openIndex === actualIndex ? "−" : "+"}
                        </span>
                      </button>

                      <AnimatePresence initial={false}>
                        {openIndex === actualIndex && (
                          <motion.div
                            key="content"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            style={{ overflow: "hidden" }}
                          >
                            <p className="mt-3 text-gray-600  text-justify md:text-start">{faq.answer}</p>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutFaq;
