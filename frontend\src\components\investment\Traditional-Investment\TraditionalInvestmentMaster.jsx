"use client";
import Banner from "@/components/ui/reusable/banner/Banner";
import React, { useEffect, useState } from "react";
import TraditionalInvestmentMain from "./TraditionalInvestmentMain";

const TraditionalInvestmentMaster = () => {
  const [scrolled, setScrolled] = useState(false);
  const handleScroll = () => {
    setScrolled(window.scrollY > 150);
  };
  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return (
    <div>
      <section
        className={`relative h-[30vh] md:h-[40vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}
      >
        {/* Parallax Fixed Background Image - Only for Banner */}
        <div
          className={`fixed top-0 left-0 w-full h-full -z-10 pointer-events-none  ${
            scrolled ? "pt-[60px]" : "pt-[80px]"
          }`}
        >
          <img
            src="/images/investment/traditional-investment-Banner2.jpg"
            alt="Traditional Investments"
            className="w-full max-w-screen h-[30vh] md:h-[40vh] object-cover"
          />
        </div>
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-[#000]/20 z-0" />

        {/* Banner Content */}
        <div className="relative z-10 text-center px-4">
          <h1 className="text-3xl md:text-5xl font-medium">
            Traditional Investments
          </h1>
        </div>
      </section>
      <section className="bg-[#f9f3f1]">
        <div className="s_wrapper !mb-0 !pb-0">
          <p className="text-gray-700 text-sm md:text-base  text-justify md:text-center max-w-4xl mx-auto ">
            When it comes to traditional investment avenues, Winshine Financial
            Services offers a diverse range of options to suit your financial
            goals and risk appetite. Here’s a comprehensive overview:
          </p>
        </div>
      </section>

      <TraditionalInvestmentMain />
    </div>
  );
};

export default TraditionalInvestmentMaster;
