"use client";

import "./CardStacking.scss";
import { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Link from "next/link";

gsap.registerPlugin(ScrollTrigger);

const CardStacking = () => {
  const PanelRef = useRef([]);
  const endTrigger = useRef(null);

  useEffect(() => {
    const pinnedPanels = PanelRef.current;
    const totalCards = pinnedPanels.length;

    pinnedPanels.forEach((panel, i) => {
      const isLastCard = i === totalCards - 1;
      const isSecondLastCard = i === totalCards - 2;

      gsap.fromTo(
        panel,
        {
          scale: 1, // Initial scale (no shrink)
          y: i * -5, // Initial position
        },
        {
          scale: isLastCard ? 1 : 0.9, // Final scale (shrink for non-last cards)
          y: isLastCard
            ? (totalCards * 5) / 1 // Overlap effect for the last card
            : i * -5, // Normal stacking for other cards
          scrollTrigger: {
            trigger: panel,
            start: "top top",
            endTrigger: endTrigger.current,
            end: "center bottom",
            pin: false, // Pin all cards
            pinSpacing: false,
            scrub: 1, // Smooth scroll effect
            id: i + 1,
            markers: false, // Optional: Add markers to see scroll positions
            // onUpdate: (self) => {
            //   if (self.progress === 1) {
            //     gsap.to(panel, { scale: 1 }); // Reset scale when all cards are stacked
            //   }
            // },
          },
        }
      );
    });

    // Handle the removal of pinning once the last card is fully in view
    gsap.timeline({
      scrollTrigger: {
        trigger: endTrigger.current,
        start: "top top",
        end: "center center",
        pin: true,
        pinSpacing: false,
        scrub: 1,
        // onLeave: () => {
        //   // Remove pinning once the last card is fully in view
        //   gsap.set(endTrigger.current, { pin: false });
        // },
      },
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <div className="CardStack_main_legacy_services flex flex-col justify-center items-center lg:py-0 bg-white">
      {/* =================================================================================================  */}
      <div className="s_wrapper !pb-0 !pt-0">
        {/* CARD ONE - Left to Right Gradient */}

        <div
          className="card__div one lg:mb-20 mb-10 grid grid-cols-3 gap-6 px-6 py-6 relative bg-cover bg-left bg-[url('/images/home/<USER>')] before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-full before:bg-gradient-to-r before:from-[#6b1609db] before:to-transparent before:z-10 overflow-hidden"
          ref={(el) => (PanelRef.current[0] = el)}
        >
          <div className="basis-[50%] flex flex-col justify-center md:pl-10 md:w-[60%] h-[350px] z-20 text-white col-span-3 ">
            <p className="text-sm md:text-base">Investment Avenues</p>
            <h2 className="text-2xl mb-4 md:text-5xl font-medium">
              Invest Today
            </h2>
            <p className=" text-justify md:text-start">
              Experience the power of early retirement planning with our expert
              guidance, ensuring your golden years arrive sooner than expected.
              Dare to dream bigger!
            </p>
            <div className="flex flex-col md:flex-row md:gap-4  gap-2 flex-wrap !mt-4 ">
              <Link href="/investment/traditional-investment">
                <button className="gradient-button-light text-[#ffffff] px-4 py-2 rounded-xl font-medium w-fit !ml-0">
                  Traditional Investment
                </button>
              </Link>
              <Link href="/investment/new-age-investment">
                <button className="gradient-button-light text-[#ffffff] px-4 py-2 rounded-xl font-medium w-fit !ml-0">
                  New-Age Investment
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* CARD TWO - Right to Left Gradient */}

        <div
          className="card__div two grid grid-cols-3 gap-6 px-6 py-6 relative bg-cover md:bg-center bg-[url('/images/home/<USER>')] before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-full before:bg-gradient-to-l before:from-[#6b1609db]  before:to-transparent before:z-10 overflow-hidden before:rotate-180 md:before:rotate-0"
          ref={(el) => (PanelRef.current[1] = el)}
        >
          <div className="basis-[50%] flex flex-col justify-center ml-auto h-[350px]  pr-10 md:w-[60%] lg:w-[50%]  z-20 text-white text-start col-span-3">
            <p className="text-sm md:text-base">Insurance</p>
            <h2 className="text-2xl mb-4 md:text-5xl font-medium  ">
              Insure Yourself
            </h2>
            <p className=" text-justify md:text-start">
              Our tailored insurance solutions ensure suitable coverage and our
              assistance in claims processing minimizes disruptions.
            </p>
            <Link href="/insurance">
              <button className="gradient-button-light text-[#ffffff] px-4 py-2 rounded-xl !mt-4 font-medium w-fit !ml-0">
                Know More
              </button>
            </Link>
          </div>
        </div>

        {/* ===============================================================================================================  */}

        {/* ===============================================================================================================  */}
        <div
          className="card__div six"
          style={{ visibility: "hidden", height: "0vh" }}
          ref={(el) => (PanelRef.current[2] = el)}
        >
          Card 7
        </div>
        <div className="end-pin" ref={endTrigger}></div>
      </div>
    </div>
  );
};

export default CardStacking;
