"use client";

import { useState } from "react";
import { ChevronUp, Plus } from "lucide-react";
import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";

export default function CashFlowTable() {
  const [expandedYears, setExpandedYears] = useState({});

  const toggleYear = (year) => {
    setExpandedYears((prev) => ({
      ...prev,
      [year]: !prev[year],
    }));
  };

  // Sample data
  const years = [
    {
      name: "Year1",
      withdrawal: "₹ 25,93,164",
      growth: "₹ 28,25,730",
      balance: "₹ 4,74,38,728",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,34,950",
          balance: "₹ 4,72,25,015",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,045",
          balance: "₹ 4,72,43,983",
        },
        {
          name: "<PERSON>",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,139",
          balance: "₹ 4,72,83,005",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,235",
          balance: "₹ 4,72,82,143",
        },
        {
          name: "May",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,330",
          balance: "₹ 4,73,01,376",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,426",
          balance: "₹ 4,73,20,705",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,523",
          balance: "₹ 4,73,40,131",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,620",
          balance: "₹ 4,73,59,655",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,718",
          balance: "₹ 4,73,79,275",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,816",
          balance: "₹ 4,73,98,994",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,35,914",
          balance: "₹ 4,74,18,812",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,16,097",
          growth: "₹ 2,36,014",
          balance: "₹ 4,74,38,728",
        },
      ],
    },
    {
      name: "Year2",
      withdrawal: "₹ 27,22,824",
      growth: "₹ 28,35,782",
      balance: "₹ 4,75,51,687",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,059",
          balance: "₹ 4,74,47,885",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,105",
          balance: "₹ 4,74,57,088",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,151",
          balance: "₹ 4,74,66,337",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,197",
          balance: "₹ 4,74,75,633",
        },
        {
          name: "May",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,244",
          balance: "₹ 4,74,84,974",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,290",
          balance: "₹ 4,74,94,363",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,337",
          balance: "₹ 4,75,03,798",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,384",
          balance: "₹ 4,75,13,280",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,432",
          balance: "₹ 4,75,22,810",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,480",
          balance: "₹ 4,75,32,388",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,527",
          balance: "₹ 4,75,42,013",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,26,902",
          growth: "₹ 2,36,576",
          balance: "₹ 4,75,51,687",
        },
      ],
    },
    {
      name: "Year3",
      withdrawal: "₹ 28,58,964",
      growth: "₹ 28,38,243",
      balance: "₹ 4,75,30,965",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,567",
          balance: "₹ 4,75,50,007",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,559",
          balance: "₹ 4,75,48,399",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,550",
          balance: "₹ 4,75,46,622",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,542",
          balance: "₹ 4,75,44,917",
        },
        {
          name: "May",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,533",
          balance: "₹ 4,75,43,203",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,525",
          balance: "₹ 4,75,41,481",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,516",
          balance: "₹ 4,75,39,750",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,508",
          balance: "₹ 4,75,38,011",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,499",
          balance: "₹ 4,75,36,263",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,490",
          balance: "₹ 4,75,34,506",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,481",
          balance: "₹ 4,75,32,740",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,38,247",
          growth: "₹ 2,36,472",
          balance: "₹ 4,75,30,965",
        },
      ],
    },
    {
      name: "Year4",
      withdrawal: "₹ 30,01,900",
      growth: "₹ 28,32,233",
      balance: "₹ 4,73,61,290",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,404",
          balance: "₹ 4,75,72,120",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,335",
          balance: "₹ 4,75,03,387",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,266",
          balance: "₹ 4,74,89,494",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,197",
          balance: "₹ 4,74,75,532",
        },
        {
          name: "May",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,127",
          balance: "₹ 4,74,61,499",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,36,057",
          balance: "₹ 4,74,47,397",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,986",
          balance: "₹ 4,74,33,224",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,915",
          balance: "₹ 4,74,18,981",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,844",
          balance: "₹ 4,74,04,666",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,773",
          balance: "₹ 4,73,90,279",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,701",
          balance: "₹ 4,73,75,821",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,50,159",
          growth: "₹ 2,35,628",
          balance: "₹ 4,73,61,290",
        },
      ],
    },
    {
      name: "Year5",
      withdrawal: "₹ 31,52,004",
      growth: "₹ 28,16,799",
      balance: "₹ 4,70,26,085",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,35,493",
          balance: "₹ 4,73,34,116",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,35,357",
          balance: "₹ 4,73,06,607",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,35,221",
          balance: "₹ 4,72,79,360",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,35,083",
          balance: "₹ 4,72,51,777",
        },
        {
          name: "May",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,946",
          balance: "₹ 4,72,24,055",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,807",
          balance: "₹ 4,71,96,195",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,668",
          balance: "₹ 4,71,68,196",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,528",
          balance: "₹ 4,71,40,056",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,387",
          balance: "₹ 4,71,11,776",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,246",
          balance: "₹ 4,70,83,355",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,34,103",
          balance: "₹ 4,70,54,791",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,62,667",
          growth: "₹ 2,33,961",
          balance: "₹ 4,70,26,085",
        },
      ],
    },
    {
      name: "Year6",
      withdrawal: "₹ 33,08,600",
      growth: "₹ 27,90,907",
      balance: "₹ 4,65,07,392",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,33,751",
          balance: "₹ 4,69,84,036",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,33,541",
          balance: "₹ 4,69,41,778",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,33,330",
          balance: "₹ 4,68,99,308",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,33,118",
          balance: "₹ 4,68,56,625",
        },
        {
          name: "May",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,32,904",
          balance: "₹ 4,68,13,729",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,32,690",
          balance: "₹ 4,67,70,619",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,32,474",
          balance: "₹ 4,67,27,293",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,32,257",
          balance: "₹ 4,66,83,750",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,32,040",
          balance: "₹ 4,66,39,990",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,31,821",
          balance: "₹ 4,65,96,011",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,31,601",
          balance: "₹ 4,65,51,812",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,75,800",
          growth: "₹ 2,31,380",
          balance: "₹ 4,65,07,392",
        },
      ],
    },
    {
      name: "Year7",
      withdrawal: "₹ 34,75,060",
      growth: "₹ 27,53,437",
      balance: "₹ 4,57,85,750",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,31,089",
          balance: "₹ 4,64,48,891",
        },
        {
          name: "Feb",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,30,797",
          balance: "₹ 4,63,90,098",
        },
        {
          name: "Mar",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,30,503",
          balance: "₹ 4,63,30,000",
        },
        {
          name: "Apr",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,30,207",
          balance: "₹ 4,62,71,627",
        },
        {
          name: "May",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,29,910",
          balance: "₹ 4,62,11,948",
        },
        {
          name: "Jun",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,29,612",
          balance: "₹ 4,61,51,969",
        },
        {
          name: "Jul",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,29,312",
          balance: "₹ 4,60,91,691",
        },
        {
          name: "Aug",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,29,011",
          balance: "₹ 4,60,31,112",
        },
        {
          name: "Sep",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,28,708",
          balance: "₹ 4,59,70,229",
        },
        {
          name: "Oct",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,28,403",
          balance: "₹ 4,59,09,043",
        },
        {
          name: "Nov",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,28,097",
          balance: "₹ 4,58,47,550",
        },
        {
          name: "Dec",
          withdrawal: "₹ 2,89,590",
          growth: "₹ 2,27,790",
          balance: "₹ 4,57,85,750",
        },
      ],
    },
    {
      name: "Year8",
      withdrawal: "₹ 36,48,840",
      growth: "₹ 27,03,176",
      balance: "₹ 4,48,40,086",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,27,408",
          balance: "₹ 4,57,09,088",
        },
        {
          name: "Feb",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,27,025",
          balance: "₹ 4,56,32,043",
        },
        {
          name: "Mar",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,26,640",
          balance: "₹ 4,55,54,613",
        },
        {
          name: "Apr",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,26,253",
          balance: "₹ 4,54,76,796",
        },
        {
          name: "May",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,25,864",
          balance: "₹ 4,53,98,589",
        },
        {
          name: "Jun",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,25,473",
          balance: "₹ 4,53,19,992",
        },
        {
          name: "Jul",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,25,080",
          balance: "₹ 4,52,41,002",
        },
        {
          name: "Aug",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,24,685",
          balance: "₹ 4,51,61,616",
        },
        {
          name: "Sep",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,24,288",
          balance: "₹ 4,50,81,834",
        },
        {
          name: "Oct",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,23,889",
          balance: "₹ 4,50,01,653",
        },
        {
          name: "Nov",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,23,488",
          balance: "₹ 4,49,21,071",
        },
        {
          name: "Dec",
          withdrawal: "₹ 3,04,070",
          growth: "₹ 2,23,085",
          balance: "₹ 4,48,40,086",
        },
      ],
    },
    {
      name: "Year9",
      withdrawal: "₹ 38,31,288",
      growth: "₹ 26,38,810",
      balance: "₹ 4,36,47,508",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,22,604",
          balance: "₹ 4,47,43,460",
        },
        {
          name: "Feb",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,22,121",
          balance: "₹ 4,46,46,220",
        },
        {
          name: "Mar",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,21,635",
          balance: "₹ 4,45,48,230",
        },
        {
          name: "Apr",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,21,147",
          balance: "₹ 4,44,50,496",
        },
        {
          name: "May",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,20,656",
          balance: "₹ 4,43,51,878",
        },
        {
          name: "Jun",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,20,163",
          balance: "₹ 4,42,52,767",
        },
        {
          name: "Jul",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,19,667",
          balance: "₹ 4,41,53,610",
        },
        {
          name: "Aug",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,19,169",
          balance: "₹ 4,40,53,066",
        },
        {
          name: "Sep",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,18,669",
          balance: "₹ 4,39,52,451",
        },
        {
          name: "Oct",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,18,166",
          balance: "₹ 4,38,51,343",
        },
        {
          name: "Nov",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,17,860",
          balance: "₹ 4,37,49,729",
        },
        {
          name: "Dec",
          withdrawal: "₹ 3,19,274",
          growth: "₹ 2,17,520",
          balance: "₹ 4,36,47,508",
        },
      ],
    },
    {
      name: "Year10",
      withdrawal: "₹ 4,02,22,856",
      growth: "₹ 25,58,919",
      balance: "₹ 4,21,83,670",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,16,562",
          balance: "₹ 4,35,28,931",
        },
        {
          name: "Feb",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,15,968",
          balance: "₹ 4,34,09,662",
        },
        {
          name: "Mar",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,15,372",
          balance: "₹ 4,32,89,796",
        },
        {
          name: "Apr",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,14,773",
          balance: "₹ 4,31,69,331",
        },
        {
          name: "May",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,14,170",
          balance: "₹ 4,30,48,263",
        },
        {
          name: "Jun",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,13,565",
          balance: "₹ 4,29,26,590",
        },
        {
          name: "Jul",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,12,957",
          balance: "₹ 4,28,04,309",
        },
        {
          name: "Aug",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,12,345",
          balance: "₹ 4,26,81,416",
        },
        {
          name: "Sep",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,11,731",
          balance: "₹ 4,25,57,909",
        },
        {
          name: "Oct",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,11,130",
          balance: "₹ 4,24,33,785",
        },
        {
          name: "Nov",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,10,493",
          balance: "₹ 4,23,09,039",
        },
        {
          name: "Dec",
          withdrawal: "₹ 3,35,238",
          growth: "₹ 2,09,869",
          balance: "₹ 4,21,83,670",
        },
      ],
    },
    {
      name: "Year11",
      withdrawal: "₹ 42,24,000",
      growth: "₹ 24,61,968",
      balance: "₹ 4,04,21,638",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,09,158",
          balance: "₹ 4,20,40,829",
        },
        {
          name: "Feb",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,08,444",
          balance: "₹ 4,18,97,273",
        },
        {
          name: "Mar",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,07,726",
          balance: "₹ 4,17,52,999",
        },
        {
          name: "Apr",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,07,005",
          balance: "₹ 4,16,08,004",
        },
        {
          name: "May",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,06,280",
          balance: "₹ 4,14,62,284",
        },
        {
          name: "Jun",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,05,551",
          balance: "₹ 4,13,15,836",
        },
        {
          name: "Jul",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,04,819",
          balance: "₹ 4,11,68,655",
        },
        {
          name: "Aug",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,04,083",
          balance: "₹ 4,10,20,738",
        },
        {
          name: "Sep",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,03,344",
          balance: "₹ 4,08,72,092",
        },
        {
          name: "Oct",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,02,600",
          balance: "₹ 4,07,22,682",
        },
        {
          name: "Nov",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,01,853",
          balance: "₹ 4,05,72,536",
        },
        {
          name: "Dec",
          withdrawal: "₹ 3,52,000",
          growth: "₹ 2,01,103",
          balance: "₹ 4,04,21,638",
        },
      ],
    },
    {
      name: "Year13",
      withdrawal: "₹ 46,56,960",
      growth: "₹ 22,00,018",
      balance: "₹ 3,58,85,895",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,89,723",
          balance: "₹ 3,81,34,380",
        },
        {
          name: "Feb",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,89,731",
          balance: "₹ 3,79,35,031",
        },
        {
          name: "Mar",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,87,735",
          balance: "₹ 3,77,34,686",
        },
        {
          name: "Apr",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,86,733",
          balance: "₹ 3,75,33,339",
        },
        {
          name: "May",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,85,726",
          balance: "₹ 3,73,30,986",
        },
        {
          name: "Jun",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,84,715",
          balance: "₹ 3,71,27,820",
        },
        {
          name: "Jul",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,83,698",
          balance: "₹ 3,69,23,238",
        },
        {
          name: "Aug",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,82,676",
          balance: "₹ 3,67,17,834",
        },
        {
          name: "Sep",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,81,649",
          balance: "₹ 3,65,11,402",
        },
        {
          name: "Oct",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,80,677",
          balance: "₹ 3,63,03,939",
        },
        {
          name: "Nov",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,79,579",
          balance: "₹ 3,60,95,438",
        },
        {
          name: "Dec",
          withdrawal: "₹ 3,88,060",
          growth: "₹ 1,78,537",
          balance: "₹ 3,58,85,895",
        },
      ],
    },
    {
      name: "Year14",
      withdrawal: "₹ 48,89,808",
      growth: "₹ 20,51,494",
      balance: "₹ 3,30,47,581",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,77,392",
          balance: "₹ 3,56,55,803",
        },
        {
          name: "Feb",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,76,242",
          balance: "₹ 3,54,24,561",
        },
        {
          name: "Mar",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,75,085",
          balance: "₹ 3,51,92,162",
        },
        {
          name: "Apr",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,73,923",
          balance: "₹ 3,49,58,601",
        },
        {
          name: "May",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,72,756",
          balance: "₹ 3,47,23,873",
        },
        {
          name: "Jun",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,71,582",
          balance: "₹ 3,44,87,971",
        },
        {
          name: "Jul",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,70,402",
          balance: "₹ 3,42,50,889",
        },
        {
          name: "Aug",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,69,217",
          balance: "₹ 3,40,12,622",
        },
        {
          name: "Sep",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,68,026",
          balance: "₹ 3,37,73,164",
        },
        {
          name: "Oct",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,66,828",
          balance: "₹ 3,35,32,509",
        },
        {
          name: "Nov",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,65,625",
          balance: "₹ 3,32,90,650",
        },
        {
          name: "Dec",
          withdrawal: "₹ 4,07,484",
          growth: "₹ 1,64,416",
          balance: "₹ 3,30,47,581",
        },
      ],
    },
    {
      name: "Year15",
      withdrawal: "₹ 51,34,296",
      growth: "₹ 18,68,340",
      balance: "₹ 2,97,81,626",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,63,099",
          balance: "₹ 3,27,82,822",
        },
        {
          name: "Feb",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,61,775",
          balance: "₹ 3,25,16,739",
        },
        {
          name: "Mar",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,60,444",
          balance: "₹ 3,22,48,325",
        },
        {
          name: "Apr",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,59,107",
          balance: "₹ 3,19,80,575",
        },
        {
          name: "May",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,57,764",
          balance: "₹ 3,17,10,480",
        },
        {
          name: "Jun",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,56,413",
          balance: "₹ 3,14,39,035",
        },
        {
          name: "Jul",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,55,056",
          balance: "₹ 3,11,66,233",
        },
        {
          name: "Aug",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,53,692",
          balance: "₹ 3,08,92,067",
        },
        {
          name: "Sep",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,52,321",
          balance: "₹ 3,06,16,530",
        },
        {
          name: "Oct",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,50,943",
          balance: "₹ 3,03,39,616",
        },
        {
          name: "Nov",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,49,559",
          balance: "₹ 3,00,61,318",
        },
        {
          name: "Dec",
          withdrawal: "₹ 4,27,858",
          growth: "₹ 1,48,167",
          balance: "₹ 2,97,81,626",
        },
      ],
    },
    {
      name: "Year16",
      withdrawal: "₹ 53,91,012",
      growth: "₹ 16,58,405",
      balance: "₹ 2,60,49,019",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,46,662",
          balance: "₹ 2,94,79,036",
        },
        {
          name: "Feb",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,45,149",
          balance: "₹ 2,91,74,934",
        },
        {
          name: "Mar",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,43,628",
          balance: "₹ 2,88,69,312",
        },
        {
          name: "Apr",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,42,100",
          balance: "₹ 2,85,62,161",
        },
        {
          name: "May",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,40,565",
          balance: "₹ 2,82,53,475",
        },
        {
          name: "Jun",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,39,021",
          balance: "₹ 2,79,43,245",
        },
        {
          name: "Jul",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,37,470",
          balance: "₹ 2,76,31,464",
        },
        {
          name: "Aug",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,35,911",
          balance: "₹ 2,73,18,124",
        },
        {
          name: "Sep",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,34,344",
          balance: "₹ 2,70,03,217",
        },
        {
          name: "Oct",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,32,770",
          balance: "₹ 2,66,86,736",
        },
        {
          name: "Nov",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,31,187",
          balance: "₹ 2,63,68,672",
        },
        {
          name: "Dec",
          withdrawal: "₹ 4,49,251",
          growth: "₹ 1,29,597",
          balance: "₹ 2,60,49,019",
        },
      ],
    },
    {
      name: "Year17",
      withdrawal: "₹ 56,60,568",
      growth: "₹ 14,19,263",
      balance: "₹ 2,18,07,713",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,27,887",
          balance: "₹ 2,57,05,191",
        },
        {
          name: "Feb",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,26,167",
          balance: "₹ 2,53,59,644",
        },
        {
          name: "Mar",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,24,440",
          balance: "₹ 2,50,12,370",
        },
        {
          name: "Apr",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,22,703",
          balance: "₹ 2,46,63,359",
        },
        {
          name: "May",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,20,958",
          balance: "₹ 2,43,12,604",
        },
        {
          name: "Jun",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,19,204",
          balance: "₹ 2,39,60,094",
        },
        {
          name: "Jul",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,17,442",
          balance: "₹ 2,36,05,822",
        },
        {
          name: "Aug",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,15,671",
          balance: "₹ 2,32,49,779",
        },
        {
          name: "Sep",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,13,890",
          balance: "₹ 2,28,91,955",
        },
        {
          name: "Oct",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,12,101",
          balance: "₹ 2,25,32,342",
        },
        {
          name: "Nov",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,10,303",
          balance: "₹ 2,21,70,931",
        },
        {
          name: "Dec",
          withdrawal: "₹ 4,71,714",
          growth: "₹ 1,08,496",
          balance: "₹ 2,18,07,713",
        },
      ],
    },
    {
      name: "Year18",
      withdrawal: "₹ 59,43,600",
      growth: "₹ 11,48,299",
      balance: "₹ 1,70,12,412",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 1,06,562",
          balance: "₹ 2,14,18,975",
        },
        {
          name: "Feb",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 1,04,618",
          balance: "₹ 2,10,28,294",
        },
        {
          name: "Mar",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 1,02,665",
          balance: "₹ 2,06,35,659",
        },
        {
          name: "Apr",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 1,00,702",
          balance: "₹ 2,02,41,060",
        },
        {
          name: "May",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 98,729",
          balance: "₹ 1,98,44,489",
        },
        {
          name: "Jun",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 96,746",
          balance: "₹ 1,94,45,935",
        },
        {
          name: "Jul",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 94,753",
          balance: "₹ 1,90,45,388",
        },
        {
          name: "Aug",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 92,750",
          balance: "₹ 1,86,42,839",
        },
        {
          name: "Sep",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 90,738",
          balance: "₹ 1,82,38,277",
        },
        {
          name: "Oct",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 88,715",
          balance: "₹ 1,78,31,691",
        },
        {
          name: "Nov",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 86,682",
          balance: "₹ 1,74,23,073",
        },
        {
          name: "Dec",
          withdrawal: "₹ 4,95,300",
          growth: "₹ 84,639",
          balance: "₹ 1,70,12,412",
        },
      ],
    },
    {
      name: "Year19",
      withdrawal: "₹ 62,40,780",
      growth: "₹ 8,42,698",
      balance: "₹ 1,16,14,330",
      months: [
        {
          name: "Jan",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 82,462",
          balance: "₹ 1,65,74,809",
        },
        {
          name: "Feb",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 80,274",
          balance: "₹ 1,61,35,018",
        },
        {
          name: "Mar",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 78,075",
          balance: "₹ 1,56,93,027",
        },
        {
          name: "Apr",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 75,865",
          balance: "₹ 1,52,48,827",
        },
        {
          name: "May",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 73,644",
          balance: "₹ 1,48,02,406",
        },
        {
          name: "Jun",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 71,412",
          balance: "₹ 1,43,53,753",
        },
        {
          name: "Jul",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 69,168",
          balance: "₹ 1,39,02,856",
        },
        {
          name: "Aug",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 66,914",
          balance: "₹ 1,34,49,705",
        },
        {
          name: "Sep",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 64,648",
          balance: "₹ 1,29,94,288",
        },
        {
          name: "Oct",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 62,371",
          balance: "₹ 1,25,36,594",
        },
        {
          name: "Nov",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 60,083",
          balance: "₹ 1,20,76,612",
        },
        {
          name: "Dec",
          withdrawal: "₹ 5,20,065",
          growth: "₹ 57,783",
          balance: "₹ 1,16,14,330",
        },
      ],
    },
    {
  "name": "Year20",
  "withdrawal": "₹ 65,52,816",
  "growth": "₹ 4,99,426",
  "balance": "₹ 55,60,940",
  "months": [
    {
      "name": "Jan",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 55,341",
      "balance": "₹ 1,11,23,603"
    },
    {
      "name": "Feb",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 52,888",
      "balance": "₹ 1,06,30,423"
    },
    {
      "name": "Mar",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 50,422",
      "balance": "₹ 1,01,34,777"
    },
    {
      "name": "Apr",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 47,944",
      "balance": "₹ 96,36,652"
    },
    {
      "name": "May",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 45,453",
      "balance": "₹ 91,36,037"
    },
    {
      "name": "Jun",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 42,950",
      "balance": "₹ 86,32,919"
    },
    {
      "name": "Jul",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 40,434",
      "balance": "₹ 81,27,285"
    },
    {
      "name": "Aug",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 37,906",
      "balance": "₹ 76,19,123"
    },
    {
      "name": "Sep",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 35,365",
      "balance": "₹ 71,08,421"
    },
    {
      "name": "Oct",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 32,812",
      "balance": "₹ 65,95,164"
    },
    {
      "name": "Nov",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 30,245",
      "balance": "₹ 60,79,342"
    },
    {
      "name": "Dec",
      "withdrawal": "₹ 5,46,068",
      "growth": "₹ 27,666",
      "balance": "₹ 55,60,940"
    }
  ]
}
  ];

  return (
    <section className="bg-[#fff] text-black">
      <div className="s_wrapper !pt-0">
        <div className="w-full">
          <h2 className="md:mb-6 text-2xl md:text-4xl lg:text-5xl font-medium text-[#040404]">
            <SkewFadeInWords text="Cash Flow After Retirement" />
          </h2>
          <div className="overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-white">
                  <th className="w-[60px] border border-gray-400 p-3 "></th>
                  <th className="border border-gray-400 p-3 text-center text-nowrap font-semibold">
                    Year
                  </th>
                  <th className="border border-gray-400 p-3 text-center text-nowrap font-semibold">
                    Withdrawal Amount
                  </th>
                  <th className="border border-gray-400 p-3 text-center text-nowrap font-semibold">
                    Growth Amount
                  </th>
                  <th className="border border-gray-400 p-3 text-center text-nowrap font-semibold">
                    Balance Corpus Amount
                  </th>
                </tr>
              </thead>
              <tbody>
                {years?.map((year, yearIndex) => (
                  <>
                    <tr key={yearIndex} className="bg-white hover:bg-gray-50">
                      <td className="border border-gray-400 p-3 text-center">
                        <button
                          onClick={() => toggleYear(year.name)}
                          className="w-6 h-6 bg-[#101435] cursor-pointer rounded-full text-white flex items-center justify-center"
                        >
                          {expandedYears[year.name] ? (
                            <ChevronUp size={16} />
                          ) : (
                            <Plus size={16} />
                          )}
                        </button>
                      </td>
                      <td className="border border-gray-400 p-3 text-nowrap">
                        {year.name}
                      </td>
                      <td className="border border-gray-400 p-3 text-right text-nowrap">
                        {year.withdrawal}
                      </td>
                      <td className="border border-gray-400 p-3 text-right text-nowrap">
                        {year.growth}
                      </td>
                      <td className="border border-gray-400 p-3 text-right text-nowrap">
                        {year.balance}
                      </td>
                    </tr>
                    {expandedYears[year.name] &&
                      year.months.map((month, monthIndex) => (
                        <tr
                          key={`${year.name}-${month.name}`}
                          className={
                            monthIndex % 2 === 0 ? "bg-white" : "bg-gray-100"
                          }
                        >
                          <td className="border border-gray-400 p-3 text-nowrap"></td>
                          <td className="border border-gray-400 p-3 text-nowrap">
                            {month.name}
                          </td>
                          <td className="border border-gray-400 p-3 text-right text-nowrap">
                            {month.withdrawal}
                          </td>
                          <td className="border border-gray-400 p-3 text-right text-nowrap">
                            {month.growth}
                          </td>
                          <td className="border border-gray-400 p-3 text-right text-nowrap">
                            {month.balance}
                          </td>
                        </tr>
                      ))}
                  </>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
}
