import React from "react";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import Image from "next/image";

const HealthInsurance = () => {
  return (
    <div className={`text-black bg-white`}>
      <div className="s_wrapper !pt-0">
        <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-4 lg:mb-8 text-center">
          <SkewFadeInWords text="Health Insurance" />
        </h2>
        <div
          className={`flex flex-col gap-4 md:gap-12 justify-between md:flex-row-reverse`}
        >
          <div className="w-full md:w-[50%] lg:w-[40%]">
            <Image
              src="/images/insurance/health-inc.jpg"
              alt="Health Insurance"
              width={400}
              height={400}
              className={`max-w-[520px] w-full h-auto md:ml-auto  rounded-md`}
            />
          </div>
          <div className="w-full md:w-[50%] lg:w-[60%] flex flex-col justify-center">
            <p className="lg:mb-4 mb-2 text-[#333]  text-justify md:text-start">
             Health Insurance covers the medical expenses of the insured due to an illness or accident in exchange for a premium amount. It enables the insurance company to provide medical coverage for hospitalisation expenses, day care procedures, critical illnesses, etc. A health plan also offers multiple benefits, including cashless hospitalisation and free medical check-ups.
            </p>
            <p className="lg:mb-4 mb-2 text-[#333]  text-justify md:text-start">
              Health insurance can also provide tax benefits on the premium paid to the insurance company under Section 80D of the Income Tax Act, 1961.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthInsurance;
