{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/circular-progress.jsx"], "sourcesContent": ["// \"use client\"\r\n\r\n// import { useEffect, useRef } from \"react\"\r\n\r\n\r\n\r\n// export function CircularProgress({ progress }) {\r\n//   const fillRef = useRef(null)\r\n\r\n//   useEffect(() => {\r\n//     if (fillRef.current) {\r\n//       const max = -219.99078369140625\r\n//       const cappedProgress = progress > 100 ? 100 : progress\r\n//       const dashOffset = ((100 - cappedProgress) / 100) * max\r\n//       fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n//     }\r\n//   }, [progress])\r\n\r\n//   return (\r\n//     <div className=\"relative w-[245px] h-[215px]\">\r\n//       <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n//         <path\r\n//           className=\"track\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#ac272b\"\r\n//           strokeWidth=\"40\"\r\n//           style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n//         />\r\n//         <path\r\n//           ref={fillRef}\r\n//           className=\"fill\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#1B1E49\"\r\n//           strokeWidth=\"40\"\r\n//           style={{\r\n//             transform: \"rotate(90deg) translate(0px, -80px)\",\r\n//             strokeDasharray: \"219.9907836914\",\r\n//             strokeDashoffset: \"-219.9907836914\",\r\n//             transition: \"stroke-dashoffset 1s\",\r\n//           }}\r\n//         />\r\n//       </svg>\r\n//       <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n//     </div>\r\n//   )\r\n// }\r\n\r\n// export default CircularProgress\r\n\r\n\"use client\"\r\n\r\nimport { useEffect, useRef } from \"react\"\r\n\r\nexport function CircularProgress({ progress }) {\r\n  const fillRef = useRef(null)\r\n\r\n  useEffect(() => {\r\n    if (fillRef.current) {\r\n      const max = 219.99078369140625\r\n      const cappedProgress = Math.min(progress, 100)\r\n      const dashOffset = ((100 - cappedProgress) / 100) * max\r\n      fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n    }\r\n  }, [progress])\r\n\r\n  return (\r\n    <div className=\"relative w-[245px] h-[245px] rounded-full overflow-hidden\">\r\n      <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n        {/* Red Track */}\r\n        <path\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#ac272b\"\r\n          strokeWidth=\"42\"\r\n          style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n        />\r\n        {/* Blue Fill */}\r\n        <path\r\n          ref={fillRef}\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#1B1E49\"\r\n          strokeWidth=\"40\"\r\n          strokeLinecap=\"butt\"\r\n          style={{\r\n            transform: \"rotate(90deg) translate(0px, -80px)\",\r\n            strokeDasharray: \"219.9907836914\",\r\n            strokeDashoffset: \"219.9907836914\",\r\n            transition: \"stroke-dashoffset 1s ease\",\r\n          }}\r\n        />\r\n      </svg>\r\n      {/* Inner white circle */}\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CircularProgress\r\n"], "names": [], "mappings": "AAAA,eAAe;AAEf,4CAA4C;AAI5C,mDAAmD;AACnD,iCAAiC;AAEjC,sBAAsB;AACtB,6BAA6B;AAC7B,wCAAwC;AACxC,+DAA+D;AAC/D,gEAAgE;AAChE,uEAAuE;AACvE,QAAQ;AACR,mBAAmB;AAEnB,aAAa;AACb,qDAAqD;AACrD,uEAAuE;AACvE,gBAAgB;AAChB,8BAA8B;AAC9B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,yEAAyE;AACzE,aAAa;AACb,gBAAgB;AAChB,0BAA0B;AAC1B,6BAA6B;AAC7B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,qBAAqB;AACrB,gEAAgE;AAChE,iDAAiD;AACjD,mDAAmD;AACnD,kDAAkD;AAClD,eAAe;AACf,aAAa;AACb,eAAe;AACf,4IAA4I;AAC5I,aAAa;AACb,MAAM;AACN,IAAI;AAEJ,kCAAkC;;;;;;AAIlC;;;AAFA;;AAIO,SAAS,iBAAiB,EAAE,QAAQ,EAAE;;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,MAAM;gBACZ,MAAM,iBAAiB,KAAK,GAAG,CAAC,UAAU;gBAC1C,MAAM,aAAa,AAAC,CAAC,MAAM,cAAc,IAAI,MAAO;gBACpD,QAAQ,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,WAAW,QAAQ;YAC9D;QACF;qCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;gBAAW,GAAE;gBAAM,GAAE;gBAAM,SAAQ;;kCAEhD,6LAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,OAAO;4BAAE,WAAW;wBAAsC;;;;;;kCAG5D,6LAAC;wBACC,KAAK;wBACL,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,OAAO;4BACL,WAAW;4BACX,iBAAiB;4BACjB,kBAAkB;4BAClB,YAAY;wBACd;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GA3CgB;KAAA;uCA6CD", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/SipCalculator%20old.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport CircularProgress from \"./circular-progress\";\r\n\r\nexport default function SipCalculator() {\r\n  // State for form inputs\r\n  const [investmentType, setInvestmentType] = useState(\"know-target-amount\");\r\n  const [investmentMode, setInvestmentMode] = useState(\"sip\");\r\n  const [targetAmount, setTargetAmount] = useState(\"1500000\");\r\n  const [duration, setDuration] = useState(\"10\");\r\n  const [rateOfReturn, setRateOfReturn] = useState(\"12\");\r\n\r\n  // State for calculation results\r\n  const [investedAmount, setInvestedAmount] = useState(0);\r\n  const [returns, setReturns] = useState(0);\r\n  const [totalWealth, setTotalWealth] = useState(0);\r\n  const [monthlyInvestment, setMonthlyInvestment] = useState(0);\r\n  const [graphProgress, setGraphProgress] = useState(0);\r\n\r\n  // State for errors\r\n  const [errors, setErrors] = useState({\r\n    targetAmount: \"\",\r\n    duration: \"\",\r\n    rateOfReturn: \"\",\r\n    general: \"\",\r\n  });\r\n\r\n  // ROI array for slider\r\n  const roiArr = useRef([]);\r\n\r\n  // Initialize ROI array\r\n  useEffect(() => {\r\n    const tempRoiArr = [];\r\n    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {\r\n      tempRoiArr.push(Number.parseFloat(i).toFixed(2));\r\n    }\r\n    roiArr.current = tempRoiArr;\r\n  }, []);\r\n\r\n  // Format number with commas\r\n  const numWithCommas = (num) => {\r\n    return num.toLocaleString(\"en-IN\");\r\n  };\r\n\r\n  // Remove commas from number string\r\n  const removeCommas = (number) => {\r\n    return number.toString().replace(/,/g, \"\");\r\n  };\r\n\r\n  // Calculate PMT - payment amount for loan or investment\r\n  const PMT = (\r\n    rate_per_period,\r\n    number_of_payments,\r\n    present_value,\r\n    future_value,\r\n    type\r\n  ) => {\r\n    if (rate_per_period !== 0.0) {\r\n      // Interest rate exists\r\n      const q = Math.pow(1 + rate_per_period, number_of_payments);\r\n      return (\r\n        -(rate_per_period * (future_value + q * present_value)) /\r\n        ((-1 + q) * (1 + rate_per_period * type))\r\n      );\r\n    } else if (number_of_payments !== 0.0) {\r\n      // No interest rate, but number of payments exists\r\n      return -(future_value + present_value) / number_of_payments;\r\n    }\r\n    return 0;\r\n  };\r\n\r\n  // Calculate present value\r\n  const presentValue = (rate, nper, pmt, fv) => {\r\n    if (nper === 0) {\r\n      return 0;\r\n    }\r\n\r\n    let pv_value;\r\n    if (rate === 0) {\r\n      // Interest rate is 0\r\n      pv_value = -(fv + pmt * nper);\r\n    } else {\r\n      const x = Math.pow(1 + rate, -nper);\r\n      const y = Math.pow(1 + rate, nper);\r\n      pv_value = -(x * (fv * rate - pmt + y * pmt)) / rate;\r\n    }\r\n\r\n    return Number.parseFloat(pv_value.toFixed(2));\r\n  };\r\n\r\n  // Calculate future value\r\n  const futureValue = (rate, nper, pmt, pv, type) => {\r\n    const pow = Math.pow(1 + rate, nper);\r\n    let fv;\r\n\r\n    if (rate) {\r\n      fv = (pmt * (1 + rate * type) * (1 - pow)) / rate - pv * pow;\r\n    } else {\r\n      fv = -1 * (pv + pmt * nper);\r\n    }\r\n\r\n    return Number.parseFloat(fv.toFixed(2));\r\n  };\r\n\r\n  // Validate range input\r\n  const validateRangeInput = (value, max, min, errorKey) => {\r\n    const numValue = Number.parseFloat(value);\r\n    const numMax = Number.parseFloat(max);\r\n    const numMin = Number.parseFloat(min);\r\n\r\n    let validated = true;\r\n    let errorMessage = \"\";\r\n\r\n    if (numValue > numMax || isNaN(numValue)) {\r\n      validated = false;\r\n      errorMessage = `Please enter a value between ${numWithCommas(\r\n        Number.parseInt(min)\r\n      )} and ${numWithCommas(Number.parseInt(max))}.`;\r\n    } else if (numValue < numMin || isNaN(numValue)) {\r\n      validated = false;\r\n      errorMessage = `Please enter a value between ${numWithCommas(\r\n        Number.parseInt(min)\r\n      )} and ${numWithCommas(Number.parseInt(max))}.`;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, [errorKey]: errorMessage }));\r\n    return validated;\r\n  };\r\n\r\n  // Calculate results based on inputs\r\n  const calculateResults = () => {\r\n    // Validate inputs\r\n    const targetAmtValid = validateRangeInput(\r\n      removeCommas(targetAmount),\r\n      \"1000000000\",\r\n      \"1\",\r\n      \"targetAmount\"\r\n    );\r\n    const durationValid = validateRangeInput(duration, \"50\", \"1\", \"duration\");\r\n    const roiValid = validateRangeInput(\r\n      rateOfReturn,\r\n      \"100\",\r\n      \"1\",\r\n      \"rateOfReturn\"\r\n    );\r\n\r\n    if (!targetAmtValid || !durationValid || !roiValid) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        general:\r\n          \"Please enter numeric inputs within the suggested range to get accurate results\",\r\n      }));\r\n      return;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, general: \"\" }));\r\n\r\n    const roi = Math.pow(1 + Number.parseFloat(rateOfReturn) / 100, 1 / 12) - 1;\r\n    const timePeriods = Number.parseInt(duration) * 12;\r\n    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));\r\n\r\n    let amtValue = 0;\r\n    let investVal = 0;\r\n    let profit = 0;\r\n    let profitPercent = 0;\r\n\r\n    if (investmentType === \"know-investment-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        amtValue = futureValue(roi, timePeriods, -1 * targetAmtValue, 0, 1);\r\n        investVal = targetAmtValue * timePeriods;\r\n      } else if (investmentMode === \"quarterly\") {\r\n        const intervals = Number.parseInt(duration) * 4;\r\n        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);\r\n        amtValue =\r\n          (targetAmtValue * (Math.pow(1 + quarterlyRoi, intervals) - 1)) /\r\n          quarterlyRoi;\r\n        investVal = targetAmtValue * intervals;\r\n      } else {\r\n        // lumpsum\r\n        amtValue =\r\n          targetAmtValue *\r\n          Math.pow(\r\n            1 + Number.parseFloat(rateOfReturn) / 100,\r\n            Number.parseInt(duration)\r\n          );\r\n        investVal = targetAmtValue;\r\n      }\r\n\r\n      profit = Math.round(amtValue) - Math.round(investVal);\r\n      profitPercent = Math.round((profit / Math.round(investVal)) * 100);\r\n      setGraphProgress((Math.round(profit) / Math.round(amtValue)) * 100);\r\n\r\n      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));\r\n      setTotalWealth(Math.round(amtValue));\r\n      setMonthlyInvestment(targetAmtValue);\r\n    } else if (investmentType === \"know-target-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        amtValue = PMT(\r\n          Number.parseFloat(rateOfReturn) / (100 * 12),\r\n          Number.parseInt(duration) * 12,\r\n          0,\r\n          -1 * targetAmtValue,\r\n          1\r\n        );\r\n        investVal = amtValue * Number.parseInt(duration) * 12;\r\n      } else if (investmentMode === \"quarterly\") {\r\n        const intervals = Number.parseInt(duration) * 4;\r\n        const quarterlyRoi = Number.parseFloat(rateOfReturn) / (100 * 4);\r\n        amtValue =\r\n          targetAmtValue /\r\n          ((Math.pow(1 + quarterlyRoi, intervals) - 1) / quarterlyRoi);\r\n        investVal = amtValue * intervals;\r\n      } else {\r\n        // lumpsum\r\n        amtValue = presentValue(\r\n          Number.parseFloat(rateOfReturn) / 100,\r\n          Number.parseInt(duration),\r\n          0,\r\n          -1 * targetAmtValue\r\n        );\r\n        investVal = amtValue;\r\n      }\r\n\r\n      profit = Math.round(targetAmtValue) - Math.round(investVal);\r\n      profitPercent = Math.round((profit / Math.round(investVal)) * 100);\r\n      setGraphProgress((Math.round(profit) / Math.round(targetAmtValue)) * 100);\r\n\r\n      setInvestedAmount(Math.round(investVal < 1 ? 1 : investVal));\r\n      setMonthlyInvestment(Math.round(amtValue > 1 ? amtValue : 0));\r\n    }\r\n\r\n    setReturns(Math.round(amtValue < 1 ? profit - 1 : profit));\r\n    setTotalWealth(targetAmtValue);\r\n  };\r\n\r\n  // Handle input changes\r\n  const handleInputChange = (e, setter) => {\r\n    const { value } = e.target;\r\n\r\n    if (e.target.type === \"text\") {\r\n      // For text inputs, update the value directly\r\n      setter(value);\r\n    } else if (e.target.type === \"range\") {\r\n      // For range inputs, update the corresponding text input\r\n      if (e.target.id === \"ill_int_rates_value\") {\r\n        // Handle ROI slider specially\r\n        const roiValue = roiArr.current[Number.parseInt(value)];\r\n        setter(roiValue);\r\n      } else {\r\n        setter(value);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Format target amount with commas\r\n  const formatTargetAmount = (value) => {\r\n    const numValue = Number.parseFloat(removeCommas(value));\r\n    if (isNaN(numValue)) return \"0\";\r\n    return numWithCommas(numValue);\r\n  };\r\n\r\n  // Calculate on input change\r\n  useEffect(() => {\r\n    calculateResults();\r\n  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);\r\n\r\n  // Get ROI slider value\r\n  const getRoiSliderValue = () => {\r\n    const index = roiArr.current.indexOf(rateOfReturn);\r\n    return index >= 0 ? index : roiArr.current.indexOf(\"12.00\");\r\n  };\r\n\r\n  // Get display text based on investment type and mode\r\n  const getDisplayText = () => {\r\n    if (investmentType === \"know-target-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        return {\r\n          preText: \"You will have to invest\",\r\n          postText: \"per month to achieve your goal.\",\r\n        };\r\n      } else if (investmentMode === \"quarterly\") {\r\n        return {\r\n          preText: \"You will have to invest\",\r\n          postText: \"quarterly to achieve your goal.\",\r\n        };\r\n      } else {\r\n        return {\r\n          preText: \"You will have to invest a lumpsum of\",\r\n          postText: \"to achieve your goal.\",\r\n        };\r\n      }\r\n    } else {\r\n      if (investmentMode === \"sip\") {\r\n        return {\r\n          preText: \"You will earn\",\r\n          postText: \"on your monthly investment.\",\r\n        };\r\n      } else if (investmentMode === \"quarterly\") {\r\n        return {\r\n          preText: \"You will earn\",\r\n          postText: \"on your quarterly investment.\",\r\n        };\r\n      } else {\r\n        return {\r\n          preText: \"You will earn\",\r\n          postText: \"on your lumpsum investment.\",\r\n        };\r\n      }\r\n    }\r\n  };\r\n\r\n  const { preText, postText } = getDisplayText();\r\n\r\n  return (\r\n    <section className=\"bg-[#fff]\">\r\n      <div className=\"s_wrapper\">\r\n        <div className=\"max-w-6xl mx-auto border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\r\n          <div className=\"flex flex-col md:flex-row\">\r\n            {/* Left side - Inputs */}\r\n            <div className=\"w-full md:w-1/2 md:p-8 p-4 bg-white\">\r\n              {/* Investment Type */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-black mb-2\">I know my</label>\r\n                <div className=\"flex gap-x-6 gap-y-2 flex-wrap\">\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"target_amount\"\r\n                      name=\"inv_type\"\r\n                      className=\"w-4 h-4 text-red-600 mr-2 accent-red-600\"\r\n                      value=\"know-target-amount\"\r\n                      checked={investmentType === \"know-target-amount\"}\r\n                      onChange={() => setInvestmentType(\"know-target-amount\")}\r\n                    />\r\n                    <label htmlFor=\"target_amount\" className=\"text-black\">\r\n                      Target Amount\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"current-inv-amt\"\r\n                      name=\"inv_type\"\r\n                      className=\"w-4 h-4 text-red-600 mr-2 accent-red-600\"\r\n                      value=\"know-investment-amount\"\r\n                      checked={investmentType === \"know-investment-amount\"}\r\n                      onChange={() =>\r\n                        setInvestmentType(\"know-investment-amount\")\r\n                      }\r\n                    />\r\n                    <label htmlFor=\"current-inv-amt\" className=\"text-black\">\r\n                      Current Investment Amount\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Target Amount */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-black mb-2\">\r\n                  {investmentType === \"know-target-amount\"\r\n                    ? \"My target amount is\"\r\n                    : \"Investment Amount\"}\r\n                </label>\r\n                <div className=\"relative mb-2\">\r\n                  <div className=\"flex items-center bg-gray-100 rounded p-2 w-full md:w-64\">\r\n                    <span className=\"text-black mr-1\">₹</span>\r\n                    <input\r\n                      id=\"target_input_value\"\r\n                      value={targetAmount}\r\n                      onChange={(e) => setTargetAmount(e.target.value)}\r\n                      onBlur={() =>\r\n                        setTargetAmount(formatTargetAmount(targetAmount))\r\n                      }\r\n                      maxLength={11}\r\n                      type=\"text\"\r\n                      className=\"bg-transparent text-black border-none w-full focus:outline-none font-medium\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div className=\"mb-1\">\r\n                  <input\r\n                    id=\"target_amt\"\r\n                    type=\"range\"\r\n                    min=\"1\"\r\n                    max=\"1000000\"\r\n                    value={removeCommas(targetAmount)}\r\n                    onChange={(e) => handleInputChange(e, setTargetAmount)}\r\n                    className=\"w-full h-1  bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600\"\r\n                  />\r\n                </div>\r\n                <div className=\"flex justify-between text-xs text-gray-500\">\r\n                  <span>₹ 1</span>\r\n                  <span>₹ 10 Lakhs</span>\r\n                </div>\r\n                {errors.targetAmount && (\r\n                  <p className=\"text-[#ac2629] text-xs mt-1\">\r\n                    {errors.targetAmount}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Investment Mode */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-black mb-2\">\r\n                  I want to invest\r\n                </label>\r\n                <div className=\"flex flex-wrap gap-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"sip_inv\"\r\n                      name=\"choice_investment\"\r\n                      className=\"w-4 h-4 text-red-600 mr-2 accent-red-600\"\r\n                      value=\"sip\"\r\n                      checked={investmentMode === \"sip\"}\r\n                      onChange={() => setInvestmentMode(\"sip\")}\r\n                    />\r\n                    <label htmlFor=\"sip_inv\" className=\"text-black\">\r\n                      Monthly (SIP)\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"quarterly_inv\"\r\n                      name=\"choice_investment\"\r\n                      className=\"w-4 h-4 text-red-600 mr-2 accent-red-600\"\r\n                      value=\"quarterly\"\r\n                      checked={investmentMode === \"quarterly\"}\r\n                      onChange={() => setInvestmentMode(\"quarterly\")}\r\n                    />\r\n                    <label htmlFor=\"quarterly_inv\" className=\"text-black\">\r\n                      Quarterly\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"lumsum_amt\"\r\n                      name=\"choice_investment\"\r\n                      className=\"w-4 h-4 text-red-600 mr-2 accent-red-600\"\r\n                      value=\"lumpsum\"\r\n                      checked={investmentMode === \"lumpsum\"}\r\n                      onChange={() => setInvestmentMode(\"lumpsum\")}\r\n                    />\r\n                    <label htmlFor=\"lumsum_amt\" className=\"text-black\">\r\n                      At Once (Lumpsum)\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Duration */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-black mb-2\">\r\n                  for a duration of\r\n                </label>\r\n                <div className=\"relative mb-2\">\r\n                  <div className=\"flex items-center bg-gray-100 rounded p-2 w-full md:w-64\">\r\n                    <input\r\n                      id=\"inv_ret_durations\"\r\n                      value={duration}\r\n                      onChange={(e) => setDuration(e.target.value)}\r\n                      maxLength={2}\r\n                      type=\"text\"\r\n                      className=\"bg-transparent text-black border-none w-5 max-w-fit focus:outline-none font-medium\"\r\n                    />\r\n                    <span className=\"text-black\">Years</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mb-1\">\r\n                  <input\r\n                    id=\"inv_ret_dur_value\"\r\n                    type=\"range\"\r\n                    min=\"1\"\r\n                    max=\"50\"\r\n                    value={duration}\r\n                    onChange={(e) => handleInputChange(e, setDuration)}\r\n                    className=\"w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600\"\r\n                  />\r\n                </div>\r\n                <div className=\"flex justify-between text-xs text-gray-500\">\r\n                  <span>1 Year</span>\r\n                  <span>50 Years</span>\r\n                </div>\r\n                {errors.duration && (\r\n                  <p className=\"text-[#ac2629] text-xs mt-1\">{errors.duration}</p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Rate of Return */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-black mb-2\">\r\n                  At the Rate of return of\r\n                </label>\r\n                <div className=\"relative mb-2\">\r\n                  <div className=\"flex items-center bg-gray-100 rounded p-2 w-full md:w-64\">\r\n                    <input\r\n                      id=\"ill_int_rates\"\r\n                      value={rateOfReturn}\r\n                      onChange={(e) => setRateOfReturn(e.target.value)}\r\n                      maxLength={5}\r\n                      type=\"text\"\r\n                      className=\"bg-transparent text-black border-none w-10 focus:outline-none font-medium\"\r\n                    />\r\n                    <span className=\"text-black\">%</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mb-1\">\r\n                  <input\r\n                    id=\"ill_int_rates_value\"\r\n                    type=\"range\"\r\n                    min=\"0\"\r\n                    max={roiArr.current.length - 1}\r\n                    value={getRoiSliderValue()}\r\n                    onChange={(e) => handleInputChange(e, setRateOfReturn)}\r\n                    className=\"w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-red-600\"\r\n                  />\r\n                </div>\r\n                <div className=\"flex justify-between text-xs text-gray-500\">\r\n                  <span>1%</span>\r\n                  <span>100%</span>\r\n                </div>\r\n                {errors.rateOfReturn && (\r\n                  <p className=\"text-[#ac2629] text-xs mt-1\">\r\n                    {errors.rateOfReturn}\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side - Results */}\r\n            <div className=\"w-full md:w-1/2 md:p-8 p-4 bg-[#f0f0fa]\">\r\n              <div className=\"flex flex-col md:flex-row items-center justify-between\">\r\n                <div className=\"text-black w-full md:w-1/2\">\r\n                  <div className=\"mb-4\">\r\n                    <p className=\"flex items-center text-sm font-medium\">\r\n                      <span className=\"inline-block w-3 h-3 bg-[#ae2f33] mr-2\"></span>\r\n                      Total amount invested\r\n                    </p>\r\n                    <p className=\"text-lg font-semibold ml-5\">\r\n                      ₹ {numWithCommas(investedAmount)}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"mb-4\">\r\n                    <p className=\"flex items-center text-sm font-medium\">\r\n                      <span className=\"inline-block w-3 h-3 bg-[#101435] mr-2\"></span>\r\n                      Returns\r\n                    </p>\r\n                    <p className=\"text-lg font-semibold ml-5\">\r\n                      ₹ {numWithCommas(returns)}\r\n                    </p>\r\n                  </div>\r\n                  <hr className=\"my-4 border-gray-300\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium\">\r\n                      Total wealth accumulated\r\n                    </p>\r\n                    <p className=\"text-lg font-semibold\">\r\n                      ₹{\" \"}\r\n                      {numWithCommas(\r\n                        Number.parseInt(removeCommas(targetAmount))\r\n                      )}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"mt-6 md:mt-0\">\r\n                  <CircularProgress progress={graphProgress} />\r\n                </div>\r\n              </div>\r\n\r\n              {errors.general && (\r\n                <p className=\"text-[#ae2f33] text-xs mt-6\">{errors.general}</p>\r\n              )}\r\n\r\n              <div className=\"mt-6 md:mt-20\">\r\n                <div className=\"bg-[#ae2f33] text-white p-4 rounded-lg text-sm md:text-base text-center\">\r\n                  <p>\r\n                    {preText}{\" \"}\r\n                    <span className=\"font-medium text-sm md:text-base\">\r\n                      ₹{numWithCommas(monthlyInvestment)}\r\n                    </span>{\" \"}\r\n                    {postText}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gCAAgC;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,cAAc;QACd,UAAU;QACV,cAAc;QACd,SAAS;IACX;IAEA,uBAAuB;IACvB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAExB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,OAAO,UAAU,CAAC,EAAE,OAAO,CAAC,MAAM,QAAQ,KAAK,KAAM;gBACnE,WAAW,IAAI,CAAC,OAAO,UAAU,CAAC,GAAG,OAAO,CAAC;YAC/C;YACA,OAAO,OAAO,GAAG;QACnB;kCAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,OAAO,IAAI,cAAc,CAAC;IAC5B;IAEA,mCAAmC;IACnC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,QAAQ,GAAG,OAAO,CAAC,MAAM;IACzC;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,iBACA,oBACA,eACA,cACA;QAEA,IAAI,oBAAoB,KAAK;YAC3B,uBAAuB;YACvB,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,iBAAiB;YACxC,OACE,CAAC,CAAC,kBAAkB,CAAC,eAAe,IAAI,aAAa,CAAC,IACtD,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAkB,IAAI,CAAC;QAE5C,OAAO,IAAI,uBAAuB,KAAK;YACrC,kDAAkD;YAClD,OAAO,CAAC,CAAC,eAAe,aAAa,IAAI;QAC3C;QACA,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAC,MAAM,MAAM,KAAK;QACrC,IAAI,SAAS,GAAG;YACd,OAAO;QACT;QAEA,IAAI;QACJ,IAAI,SAAS,GAAG;YACd,qBAAqB;YACrB,WAAW,CAAC,CAAC,KAAK,MAAM,IAAI;QAC9B,OAAO;YACL,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC;YAC9B,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM;YAC7B,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,IAAI,GAAG,CAAC,IAAI;QAClD;QAEA,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,CAAC;IAC5C;IAEA,yBAAyB;IACzB,MAAM,cAAc,CAAC,MAAM,MAAM,KAAK,IAAI;QACxC,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM;QAC/B,IAAI;QAEJ,IAAI,MAAM;YACR,KAAK,AAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,IAAK,OAAO,KAAK;QAC3D,OAAO;YACL,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI;QAC5B;QAEA,OAAO,OAAO,UAAU,CAAC,GAAG,OAAO,CAAC;IACtC;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,OAAO,KAAK,KAAK;QAC3C,MAAM,WAAW,OAAO,UAAU,CAAC;QACnC,MAAM,SAAS,OAAO,UAAU,CAAC;QACjC,MAAM,SAAS,OAAO,UAAU,CAAC;QAEjC,IAAI,YAAY;QAChB,IAAI,eAAe;QAEnB,IAAI,WAAW,UAAU,MAAM,WAAW;YACxC,YAAY;YACZ,eAAe,CAAC,6BAA6B,EAAE,cAC7C,OAAO,QAAQ,CAAC,MAChB,KAAK,EAAE,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjD,OAAO,IAAI,WAAW,UAAU,MAAM,WAAW;YAC/C,YAAY;YACZ,eAAe,CAAC,6BAA6B,EAAE,cAC7C,OAAO,QAAQ,CAAC,MAChB,KAAK,EAAE,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjD;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAa,CAAC;QAC1D,OAAO;IACT;IAEA,oCAAoC;IACpC,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,MAAM,iBAAiB,mBACrB,aAAa,eACb,cACA,KACA;QAEF,MAAM,gBAAgB,mBAAmB,UAAU,MAAM,KAAK;QAC9D,MAAM,WAAW,mBACf,cACA,OACA,KACA;QAGF,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,UAAU;YAClD,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,SACE;gBACJ,CAAC;YACD;QACF;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAG,CAAC;QAE7C,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO,UAAU,CAAC,gBAAgB,KAAK,IAAI,MAAM;QAC1E,MAAM,cAAc,OAAO,QAAQ,CAAC,YAAY;QAChD,MAAM,iBAAiB,OAAO,UAAU,CAAC,aAAa;QAEtD,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,gBAAgB;QAEpB,IAAI,mBAAmB,0BAA0B;YAC/C,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,YAAY,KAAK,aAAa,CAAC,IAAI,gBAAgB,GAAG;gBACjE,YAAY,iBAAiB;YAC/B,OAAO,IAAI,mBAAmB,aAAa;gBACzC,MAAM,YAAY,OAAO,QAAQ,CAAC,YAAY;gBAC9C,MAAM,eAAe,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAC/D,WACE,AAAC,iBAAiB,CAAC,KAAK,GAAG,CAAC,IAAI,cAAc,aAAa,CAAC,IAC5D;gBACF,YAAY,iBAAiB;YAC/B,OAAO;gBACL,UAAU;gBACV,WACE,iBACA,KAAK,GAAG,CACN,IAAI,OAAO,UAAU,CAAC,gBAAgB,KACtC,OAAO,QAAQ,CAAC;gBAEpB,YAAY;YACd;YAEA,SAAS,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC;YAC3C,gBAAgB,KAAK,KAAK,CAAC,AAAC,SAAS,KAAK,KAAK,CAAC,aAAc;YAC9D,iBAAiB,AAAC,KAAK,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,YAAa;YAE/D,kBAAkB,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI;YACjD,eAAe,KAAK,KAAK,CAAC;YAC1B,qBAAqB;QACvB,OAAO,IAAI,mBAAmB,sBAAsB;YAClD,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,IACT,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAC3C,OAAO,QAAQ,CAAC,YAAY,IAC5B,GACA,CAAC,IAAI,gBACL;gBAEF,YAAY,WAAW,OAAO,QAAQ,CAAC,YAAY;YACrD,OAAO,IAAI,mBAAmB,aAAa;gBACzC,MAAM,YAAY,OAAO,QAAQ,CAAC,YAAY;gBAC9C,MAAM,eAAe,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAC/D,WACE,iBACA,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,cAAc,aAAa,CAAC,IAAI,YAAY;gBAC7D,YAAY,WAAW;YACzB,OAAO;gBACL,UAAU;gBACV,WAAW,aACT,OAAO,UAAU,CAAC,gBAAgB,KAClC,OAAO,QAAQ,CAAC,WAChB,GACA,CAAC,IAAI;gBAEP,YAAY;YACd;YAEA,SAAS,KAAK,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC;YACjD,gBAAgB,KAAK,KAAK,CAAC,AAAC,SAAS,KAAK,KAAK,CAAC,aAAc;YAC9D,iBAAiB,AAAC,KAAK,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,kBAAmB;YAErE,kBAAkB,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI;YACjD,qBAAqB,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW;QAC5D;QAEA,WAAW,KAAK,KAAK,CAAC,WAAW,IAAI,SAAS,IAAI;QAClD,eAAe;IACjB;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC,GAAG;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAE1B,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC5B,6CAA6C;YAC7C,OAAO;QACT,OAAO,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,SAAS;YACpC,wDAAwD;YACxD,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,uBAAuB;gBACzC,8BAA8B;gBAC9B,MAAM,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,CAAC,OAAO;gBACvD,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,OAAO,UAAU,CAAC,aAAa;QAChD,IAAI,MAAM,WAAW,OAAO;QAC5B,OAAO,cAAc;IACvB;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAc;QAAU;QAAc;QAAgB;KAAe;IAEzE,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;QACrC,OAAO,SAAS,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;IACrD;IAEA,qDAAqD;IACrD,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,sBAAsB;YAC3C,IAAI,mBAAmB,OAAO;gBAC5B,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF,OAAO,IAAI,mBAAmB,aAAa;gBACzC,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF;QACF,OAAO;YACL,IAAI,mBAAmB,OAAO;gBAC5B,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF,OAAO,IAAI,mBAAmB,aAAa;gBACzC,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF;QACF;IACF;IAEA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IAE9B,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,IAAM,kBAAkB;;;;;;sEAEpC,6LAAC;4DAAM,SAAQ;4DAAgB,WAAU;sEAAa;;;;;;;;;;;;8DAIxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,IACR,kBAAkB;;;;;;sEAGtB,6LAAC;4DAAM,SAAQ;4DAAkB,WAAU;sEAAa;;;;;;;;;;;;;;;;;;;;;;;;8CAQ9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDACd,mBAAmB,uBAChB,wBACA;;;;;;sDAEN,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;kEAClC,6LAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,QAAQ,IACN,gBAAgB,mBAAmB;wDAErC,WAAW;wDACX,MAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,aAAa;gDACpB,UAAU,CAAC,IAAM,kBAAkB,GAAG;gDACtC,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;wCAEP,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDACV,OAAO,YAAY;;;;;;;;;;;;8CAM1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,IAAM,kBAAkB;;;;;;sEAEpC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAa;;;;;;;;;;;;8DAIlD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,IAAM,kBAAkB;;;;;;sEAEpC,6LAAC;4DAAM,SAAQ;4DAAgB,WAAU;sEAAa;;;;;;;;;;;;8DAIxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,IAAM,kBAAkB;;;;;;sEAEpC,6LAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAa;;;;;;;;;;;;;;;;;;;;;;;;8CAQzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAW;wDACX,MAAK;wDACL,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAa;;;;;;;;;;;;;;;;;sDAGjC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;gDACtC,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;wCAEP,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA+B,OAAO,QAAQ;;;;;;;;;;;;8CAK/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAW;wDACX,MAAK;wDACL,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAa;;;;;;;;;;;;;;;;;sDAGjC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAK,OAAO,OAAO,CAAC,MAAM,GAAG;gDAC7B,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;gDACtC,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;wCAEP,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDACV,OAAO,YAAY;;;;;;;;;;;;;;;;;;sCAO5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;8EACX,6LAAC;oEAAK,WAAU;;;;;;gEAAgD;;;;;;;sEAGlE,6LAAC;4DAAE,WAAU;;gEAA6B;gEACrC,cAAc;;;;;;;;;;;;;8DAGrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;8EACX,6LAAC;oEAAK,WAAU;;;;;;gEAAgD;;;;;;;sEAGlE,6LAAC;4DAAE,WAAU;;gEAA6B;gEACrC,cAAc;;;;;;;;;;;;;8DAGrB,6LAAC;oDAAG,WAAU;;;;;;8DACd,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEAGnC,6LAAC;4DAAE,WAAU;;gEAAwB;gEACjC;gEACD,cACC,OAAO,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;;;;;;sDAMrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gLAAA,CAAA,UAAgB;gDAAC,UAAU;;;;;;;;;;;;;;;;;gCAI/B,OAAO,OAAO,kBACb,6LAAC;oCAAE,WAAU;8CAA+B,OAAO,OAAO;;;;;;8CAG5D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;gDACE;gDAAS;8DACV,6LAAC;oDAAK,WAAU;;wDAAmC;wDAC/C,cAAc;;;;;;;gDACV;gDACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;GA9kBwB;KAAA", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nconst Banner = ({ imageUrl, title, subtitle }) => {\r\n  return (\r\n    <section className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}>\r\n      {/* Parallax Fixed Background Image - Only for Banner */}\r\n      <div\r\n        className=\"fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0\"\r\n      >\r\n        <img src={imageUrl} alt={title} className=\"w-full h-full object-cover\"/>\r\n      </div>\r\n      {/* <div\r\n        className=\"fixed top-0 left-0 w-full h-full !bg-cover !bg-center -z-10 pointer-events-none\"\r\n        style={backgroundStyles}\r\n      /> */}\r\n\r\n      {/* Dark Overlay */}\r\n      <div className=\"absolute inset-0 bg-[#000]/40 z-0\" />\r\n\r\n      {/* Banner Content */}\r\n      <div className=\"relative z-10 text-center px-4\">\r\n        <h1 className=\"text-3xl md:text-5xl font-medium\">{title}</h1>\r\n        {subtitle && (\r\n          <p className=\"text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Banner;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,qBACE,6LAAC;QAAQ,WAAW,CAAC,0GAA0G,CAAC;;0BAE9H,6LAAC;gBACC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,KAAK;oBAAU,KAAK;oBAAO,WAAU;;;;;;;;;;;0BAQ5C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;KA5BM;uCA8BS", "debugId": null}}]}