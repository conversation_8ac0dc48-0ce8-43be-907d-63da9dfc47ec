"use client";

import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import "./Navbar.scss";
import { Menu } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { usePathname } from "next/navigation";

const Navbar = () => {
  const [scrolled, setScrolled] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [investmentDropdownOpen, setInvestmentDropdownOpen] = useState(false);
  const [businessDropdownOpen, setBusinessDropdownOpen] = useState(false);
  const hideTimeoutRef = useRef(null);
  const investmentTimeoutRef = useRef(null);
  const businessTimeoutRef = useRef(null);
  const pathname = usePathname();
  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(null);
  const [submenuPath, setSubmenuPath] = useState([]);

  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleScroll = () => {
    setScrolled(window.scrollY > 150);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleMenuToggle = () => {
    setMenuOpen((prev) => !prev);
  };

  // Calculator Dropdown
  const showDropdown = () => {
    clearTimeout(hideTimeoutRef.current);
    setIsDropdownVisible(true);
    setBusinessDropdownOpen(false);
    setInvestmentDropdownOpen(false);
  };

  const hideDropdownWithDelay = () => {
    hideTimeoutRef.current = setTimeout(() => {
      setIsDropdownVisible(false);
    }, 200);
  };

  // Investment Dropdown
  const showInvestmentDropdown = () => {
    clearTimeout(investmentTimeoutRef.current);
    setInvestmentDropdownOpen(true);
  };

  const hideInvestmentDropdownWithDelay = () => {
    investmentTimeoutRef.current = setTimeout(() => {
      setInvestmentDropdownOpen(false);
    }, 400);
  };

  const toggleInvestmentDropdownMobile = () => {
    setInvestmentDropdownOpen((prev) => {
      if (!prev) {
        setBusinessDropdownOpen(false), setIsDropdownVisible(false);
      } // close other
      return !prev;
    });
  };

  // Business Opportunity Dropdown
  const showBusinessDropdown = () => {
    clearTimeout(businessTimeoutRef.current);
    setBusinessDropdownOpen(true);
  };

  const hideBusinessDropdownWithDelay = () => {
    businessTimeoutRef.current = setTimeout(() => {
      setBusinessDropdownOpen(false);
    }, 400);
  };

  const toggleBusinessDropdownMobile = () => {
    setBusinessDropdownOpen((prev) => {
      if (!prev) {
        setInvestmentDropdownOpen(false), setIsDropdownVisible(false);
      } // close other
      return !prev;
    });
  };

  return (
    <div
      className={`navbar-wrapper  hidden lg:block border-b drop-shadow-sm ${
        scrolled ? "scrolled-up" : ""
      }`}
    >
      <nav className={`navbar ${scrolled ? "!py-3" : "!py-5"}`}>
        <div className="w-[97%] lg:max-w-[1400px] flex justify-end items-center relative max-w-full">
          <div
            className={`logo pl-4 ${scrolled ? "logo-small" : ""}`}
            onClick={() => setMenuOpen(false)}
          >
            <Link href="/" className="transition-all">
              <Image
                src="/images/logo/cropped-winshine_logo-1.png"
                height={120}
                width={270}
                alt="Winshine Logo"
                className="max-w-[80%] ml-1 md:ml-0 lg:max-w-[220px] xl:max-w-[270px]"
              />
            </Link>
          </div>

          <div
            className={`${scrolled ? "top-16" : "top-18"} ${
              menuOpen
                ? "translate-y-0 mt-2 lg:translate-y-0 bg-white"
                : "translate-y-[-700px] lg:translate-y-0"
            } transition duration-400 items-center gap-[20px] absolute -z-10 lg:z-10 lg:!static lg:flex w-full lg:w-fit p-4 lg:p-0 rounded-xl drop-shadow-2xl lg:drop-shadow-none`}
          >
            <div className="flex flex-col lg:flex-row gap-4 lg:gap-3 xl:gap-6 2xl:gap-8">
              <Link
                href="/"
                className={`text-[#101435] transition-all text-[16px] lg:text-[14px] xl:text-[16px] lg:hidden select-none ${
                  pathname === "/" ? "font-semibold" : "font-[500]"
                }`}
                onClick={() => setMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/about-us"
                className={`text-[#101435] transition-all text-[16px] lg:text-[14px] xl:text-[16px] select-none ${
                  pathname === "/about-us" ? "font-semibold" : "font-[500]"
                }`}
                onClick={() => setMenuOpen(false)}
              >
                About Us
              </Link>

              <Link
                href="/services"
                className={`text-[#101435]  transition-all text-[16px] lg:text-[14px] xl:text-[16px] select-none ${
                  pathname === "/services" ? "font-semibold" : "font-[500]"
                }`}
                onClick={() => setMenuOpen(false)}
              >
                Services
              </Link>

              {/* Investment Dropdown */}
              <div
                className="relative"
                onMouseEnter={!isMobile ? showInvestmentDropdown : undefined}
                onMouseLeave={
                  !isMobile ? hideInvestmentDropdownWithDelay : undefined
                }
              >
                <p
                  className={`text-[#101435] transition-all text-[16px] cursor-pointer select-none ${
                    pathname.includes("/investment")
                      ? "font-semibold"
                      : "font-[500]"
                  }`}
                  onClick={
                    isMobile ? toggleInvestmentDropdownMobile : undefined
                  }
                >
                  Investment
                </p>
                <AnimatePresence>
                  {investmentDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                      className="absolute lg:left-[50%] lg:translate-x-[-50%] mt-4 flex flex-col bg-white text-black rounded-xl overflow-hidden navbar-dropdown-shadow  z-10 min-w-[220px] py-2 w-full lg:w-auto"
                    >
                      <Link
                        href="/investment/traditional-investment"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setInvestmentDropdownOpen(false);
                        }}
                      >
                        Traditional Investments
                      </Link>
                      <Link
                        href="/investment/new-age-investment"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setInvestmentDropdownOpen(false);
                        }}
                      >
                        New-Age Investment
                      </Link>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <Link
                href="/insurance"
                className={`text-[#101435] transition-all select-none text-[16px] lg:text-[14px] xl:text-[16px] ${
                  pathname === "/insurance" ? "font-semibold" : "font-[500]"
                }`}
                onClick={() => setMenuOpen(false)}
              >
                Insurance
              </Link>

              {/* Calculators Dropdown */}
              {/* <div
                className="relative"
                onMouseEnter={showDropdown}
                onMouseLeave={hideDropdownWithDelay}
              >
                <p
                  className={`text-[#101435] transition-all select-none text-[16px] lg:text-[14px] xl:text-[16px] cursor-pointer ${
                    pathname.includes("/calculator")
                      ? "font-semibold"
                      : "font-[500]"
                  }`}
                >
                  Calculators
                </p>
                <AnimatePresence>
                  {isDropdownVisible && (
                    <motion.div
                      initial={{ opacity: 0, y: -50 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.4, delay: 0.3 }}
                      className="absolute lg:left-[50%] lg:translate-x-[-50%] mt-4 flex flex-col bg-white text-black rounded-xl overflow-hidden navbar-dropdown-shadow  z-10 min-w-[220px] py-2 w-full lg:w-auto"
                    >
                      <Link
                        href="/calculator/sip-calculator"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setIsDropdownVisible(false);
                        }}
                      >
                        SIP
                      </Link>
                      <Link
                        href="/calculator/retirement-calculator"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setIsDropdownVisible(false);
                        }}
                      >
                        Retirement
                      </Link>
                      <Link
                        href="/calculator/emi-calculator"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setIsDropdownVisible(false);
                        }}
                      >
                        EMI
                      </Link>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div> */}

              {/* Resources */}
              <div className="relative group inline-block">
                <p
                  onMouseEnter={() => setResourcesDropdownOpen("resources")}
                  onMouseLeave={() => {
                    setResourcesDropdownOpen(null);
                    setSubmenuPath([]);
                  }}
                  className="text-[#101435] transition-all text-[16px] lg:text-[14px] xl:text-[16px] cursor-pointer select-none font-[500]"
                >
                  Resources
                </p>

                {/* Dropdown container */}
                <AnimatePresence>
                  {resourcesDropdownOpen === "resources" && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                      onMouseEnter={() => setResourcesDropdownOpen("resources")}
                      onMouseLeave={() => {
                        setResourcesDropdownOpen(null);
                        setSubmenuPath([]);
                      }}
                      className="absolute lg:left-[50%] lg:translate-x-[-50%] mt-4 flex flex-col bg-white text-black rounded-xl navbar-dropdown-shadow  z-10 min-w-[220px] py-2 w-full lg:w-auto"
                    >
                      <ul>
                        <li
                          className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                          onMouseEnter={() =>
                            setSubmenuPath(["investor-login"])
                          }
                          onMouseLeave={() => setSubmenuPath([])}
                        >
                          <div className="flex items-center justify-between cursor-pointer">
                            <p>Investor Login</p>
                            <p>→</p>
                          </div>
                          {submenuPath[0] === "investor-login" && (
                            <ul className="absolute md:top-0 top-full left-0 md:left-full shadow-lg z-30 flex flex-col bg-white text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://finzy.com/login" target="_blank">
                                  Finzy
                                </Link>
                              </li>
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://dashboard.lendenclub.com/auth/login" target="_blank">
                                  LenDen
                                </Link>
                              </li>
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://app.betterinvest.club/login" target="_blank">
                                  Better Invest
                                </Link>
                              </li>
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://www.altgraaf.com/" target="_blank">
                                  Altgraaf
                                </Link>
                              </li>
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://hbits.co/" target="_blank">
                                  hBits
                                </Link>
                              </li>
                            </ul>
                          )}
                        </li>
                        <li
                          className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                          onMouseEnter={() => setSubmenuPath(["ipo"])}
                          onMouseLeave={() => setSubmenuPath([])}
                        >
                          <div className="flex items-center justify-between cursor-pointer">
                            <p>IPO</p>
                            <p>→</p>
                          </div>
                          {submenuPath[0] === "ipo" && (
                            <ul className="absolute md:top-0 top-full left-0 md:left-full shadow-lg z-30 flex flex-col bg-white text-black rounded-xl navbar-dropdown-shadow min-w-[220px] md:min-w-[300px] py-2 w-full lg:w-auto">
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://ris.kfintech.com/ipostatus/ipos.aspx" target="_blank">
                                  IPO Allotment Status - KFINTECH
                                </Link>
                              </li>
                              <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                <Link href="https://in.mpms.mufg.com/Initial_Offer/public-issues.html" target="_blank">
                                  IPO Allotment status - Link Intime
                                </Link>
                              </li>
                            </ul>
                          )}
                        </li>
                        <li
                          className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                          onMouseEnter={() => setSubmenuPath(["insurance"])}
                          onMouseLeave={() => setSubmenuPath([])}
                        >
                          <div className="flex items-center justify-between cursor-pointer">
                            <p>Insurance</p>
                            <p>→</p>
                          </div>
                          {submenuPath[0] === "insurance" && (
                            <ul className="absolute md:top-0 top-full left-0 md:left-full shadow-lg z-30 flex flex-col bg-white text-black rounded-xl navbar-dropdown-shadow min-w-[220px] md:min-w-[300px] py-2 w-full lg:w-auto">
                              <li
                                className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                                onMouseEnter={() =>
                                  setSubmenuPath(["insurance", "BajajAllianz"])
                                }
                                onMouseLeave={() => setSubmenuPath([])}
                              >
                                <div className="flex items-center justify-between cursor-pointer">
                                  <p>Bajaj Allianz Life Insurance</p>
                                  <p>→</p>
                                </div>
                                {submenuPath[0] === "insurance" &&
                                  submenuPath[1] === "BajajAllianz" && (
                                    <ul className="absolute top-full md:top-0 right-0 md:right-full shadow-lg z-30 flex flex-col bg-[#cac9ff] text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://www.bajajallianzlife.com/renewal-payment.html" target="_blank">
                                          Online Premium Payment
                                        </Link>
                                      </li>
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://carequotebi.balic.in/carequote/welcome.jsp" target="_blank">
                                          Premium Calculator
                                        </Link>
                                      </li>
                                    </ul>
                                  )}
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                                onMouseEnter={() =>
                                  setSubmenuPath(["insurance", "ICICIPruLife"])
                                }
                                onMouseLeave={() => setSubmenuPath([])}
                              >
                                <div className="flex items-center justify-between cursor-pointer">
                                  <p>ICICI PruLife</p>
                                  <p>→</p>
                                </div>
                                {submenuPath[0] === "insurance" &&
                                  submenuPath[1] === "ICICIPruLife" && (
                                    <ul className="absolute top-full md:top-0 right-0 md:right-full shadow-lg z-30 flex flex-col bg-[#cac9ff] text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://www.iciciprulife.com/services/pay-life-insurance-premium-online.html" target="_blank">
                                          ICICI Prulife Online Premium
                                        </Link>
                                      </li>
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://customer.iciciprulife.com/csr/cmmn-home.htm?execution=e1s1" target="_blank">
                                          ICICI Prulife Login
                                        </Link>
                                      </li>
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://buy.iciciprulife.com/buy/EBI.htm?execution=e1s1#/LAEBI/T50" target="_blank">
                                          Premium Calculator
                                        </Link>
                                      </li>
                                    </ul>
                                  )}
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                                onMouseEnter={() =>
                                  setSubmenuPath(["insurance", "HDFCLife"])
                                }
                                onMouseLeave={() => setSubmenuPath([])}
                              >
                                <div className="flex items-center justify-between cursor-pointer">
                                  <p>HDFC Life</p>
                                  <p>→</p>
                                </div>
                                {submenuPath[0] === "insurance" &&
                                  submenuPath[1] === "HDFCLife" && (
                                    <ul className="absolute top-full md:top-0 right-0 md:right-full shadow-lg z-30 flex flex-col bg-[#cac9ff] text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://myaccount.hdfclife.com/login" target="_blank">
                                          Customer Login
                                        </Link>
                                      </li>
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://onlinepayments.hdfclife.com/HLifeWeb-QP/hlife/quick_pay.jsp" target="_blank">
                                          Online Premium Payment
                                        </Link>
                                      </li>
                                    </ul>
                                  )}
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                                onMouseEnter={() =>
                                  setSubmenuPath([
                                    "insurance",
                                    "ICICILombardGen",
                                  ])
                                }
                                onMouseLeave={() => setSubmenuPath([])}
                              >
                                <div className="flex items-center justify-between cursor-pointer">
                                  <p>ICICI Lombard Gen</p>
                                  <p>→</p>
                                </div>
                                {submenuPath[0] === "insurance" &&
                                  submenuPath[1] === "ICICILombardGen" && (
                                    <ul className="absolute top-full md:top-0 right-0 md:right-full shadow-lg z-30 flex flex-col bg-[#cac9ff] text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://www.icicilombard.com/" target="_blank">
                                          Customer Login
                                        </Link>
                                      </li>
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://www.icicilombard.com/renew-policy-online#/AllRenewal" target="_blank">
                                          Online Premium Payment
                                        </Link>
                                      </li>
                                    </ul>
                                  )}
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white relative"
                                onMouseEnter={() =>
                                  setSubmenuPath([
                                    "insurance",
                                    "HDFCErgoGen",
                                  ])
                                }
                                onMouseLeave={() => setSubmenuPath([])}
                              >
                                <div className="flex items-center justify-between cursor-pointer">
                                  <p>HDFC Ergo Gen Insurance in Insurance</p>
                                  <p>→</p>
                                </div>
                                {submenuPath[0] === "insurance" &&
                                  submenuPath[1] === "HDFCErgoGen" && (
                                    <ul className="absolute md:top-0 top-full right-0 md:right-full shadow-lg z-30 flex flex-col bg-[#cac9ff] text-black rounded-xl navbar-dropdown-shadow min-w-[220px] py-2 w-full lg:w-auto">
                                      <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                                        <Link href="https://www.hdfcergo.com/renew-hdfc-ergo-policy" target="_blank">
                                          Online Premium Payment
                                        </Link>
                                      </li>
                                    </ul>
                                  )}
                              </li>
                            </ul>
                          )}
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://phillipone.phillipcapital.in/" target="_blank">
                            Phillip Capital
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://www.camsonline.com/Investors" target="_blank">
                            Cams
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://mfs.kfintech.com/investor" target="_blank">
                            Kfintech
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://www.jmfinancialservices.in/customer-corner/tools-and-resources" target="_blank">
                            JM Tools
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://blinktrade.jmfinancialservices.in/userMaster/login" target="_blank">
                            JM Blinktrade Login
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://my.jmfonline.in/" target="_blank">
                            JM Back office Login
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://chartink.com/stocks/jmfinancil.html" target="_blank">
                            Chart
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://www.bseindia.com/" target="_blank">
                            BSE India
                          </Link>
                        </li>
                        <li className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white">
                          <Link href="https://www.nseindia.com/" target="_blank">
                            NSE India
                          </Link>
                        </li>
                      </ul>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              {/* ----- */}
              {/* Business Opportunity Dropdown */}
              <div
                className="relative"
                onMouseEnter={!isMobile ? showBusinessDropdown : undefined}
                onMouseLeave={
                  !isMobile ? hideBusinessDropdownWithDelay : undefined
                }
              >
                <p
                  className={`text-[#101435] transition-all text-[16px] lg:text-[14px] xl:text-[16px] cursor-pointer select-none ${
                    pathname.includes("/business-opportunity")
                      ? "font-semibold"
                      : "font-[500]"
                  }`}
                  onClick={isMobile ? toggleBusinessDropdownMobile : undefined}
                >
                  Business Opportunity
                </p>
                <AnimatePresence>
                  {businessDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                      className="absolute lg:left-[50%] lg:translate-x-[-50%] mt-4 flex flex-col bg-white text-black rounded-xl overflow-hidden navbar-dropdown-shadow  z-10 min-w-[220px] py-2 w-full lg:w-auto"
                    >
                      <Link
                        href="/business-opportunity/business-associate"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setBusinessDropdownOpen(false);
                        }}
                      >
                        For Business Associate
                      </Link>
                      <Link
                        href="/business-opportunity/employer"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setBusinessDropdownOpen(false);
                        }}
                      >
                        For Employer
                      </Link>
                      <Link
                        href="/business-opportunity/private-client"
                        className="px-4 py-2 hover:bg-[#101435] select-none hover:text-white"
                        onClick={() => {
                          setMenuOpen(false);
                          setBusinessDropdownOpen(false);
                        }}
                      >
                        For Private Client
                      </Link>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              {/* -------- */}
              <Link
                href="/contact"
                className={`text-[#101435] transition-all select-none text-[16px] lg:text-[14px] xl:text-[16px] ${
                  pathname === "/contact" ? "font-semibold" : "font-[500]"
                }`}
                onClick={() => setMenuOpen(false)}
              >
                Contact
              </Link>
            </div>

            <Link
              href="https://winshine.wealthmagic.in/"
              target="_blank"
              onClick={() => setMenuOpen(false)}
            >
              <button className="gradient-button-rd text-[#ffffff] px-4 py-2 rounded-xl select-none !ml-0 xl:!ml-4 !mt-4 md:!mt-0 font-semibold">
                Client Login
              </button>
            </Link>
          </div>

          <div
            className="items-center gap-[30px] lg:hidden py-1 pr-1 md:pr-0"
            onClick={handleMenuToggle}
          >
            <span>
              <Menu className="text-black lg:text-[#a91e22] h-8" />
            </span>
          </div>
        </div>
      </nav>
    </div>
  );
};

export default Navbar;
