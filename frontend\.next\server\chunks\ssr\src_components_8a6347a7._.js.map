{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/circular-progress.jsx"], "sourcesContent": ["// \"use client\"\r\n\r\n// import { useEffect, useRef } from \"react\"\r\n\r\n\r\n\r\n// export function CircularProgress({ progress }) {\r\n//   const fillRef = useRef(null)\r\n\r\n//   useEffect(() => {\r\n//     if (fillRef.current) {\r\n//       const max = -219.99078369140625\r\n//       const cappedProgress = progress > 100 ? 100 : progress\r\n//       const dashOffset = ((100 - cappedProgress) / 100) * max\r\n//       fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n//     }\r\n//   }, [progress])\r\n\r\n//   return (\r\n//     <div className=\"relative w-[245px] h-[215px]\">\r\n//       <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n//         <path\r\n//           className=\"track\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#ac272b\"\r\n//           strokeWidth=\"40\"\r\n//           style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n//         />\r\n//         <path\r\n//           ref={fillRef}\r\n//           className=\"fill\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#1B1E49\"\r\n//           strokeWidth=\"40\"\r\n//           style={{\r\n//             transform: \"rotate(90deg) translate(0px, -80px)\",\r\n//             strokeDasharray: \"219.9907836914\",\r\n//             strokeDashoffset: \"-219.9907836914\",\r\n//             transition: \"stroke-dashoffset 1s\",\r\n//           }}\r\n//         />\r\n//       </svg>\r\n//       <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n//     </div>\r\n//   )\r\n// }\r\n\r\n// export default CircularProgress\r\n\r\n\"use client\"\r\n\r\nimport { useEffect, useRef } from \"react\"\r\n\r\nexport function CircularProgress({ progress }) {\r\n  const fillRef = useRef(null)\r\n\r\n  useEffect(() => {\r\n    if (fillRef.current) {\r\n      const max = 219.99078369140625\r\n      const cappedProgress = Math.min(progress, 100)\r\n      const dashOffset = ((100 - cappedProgress) / 100) * max\r\n      fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n    }\r\n  }, [progress])\r\n\r\n  return (\r\n    <div className=\"relative w-[245px] h-[245px] rounded-full overflow-hidden\">\r\n      <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n        {/* Red Track */}\r\n        <path\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#ac272b\"\r\n          strokeWidth=\"42\"\r\n          style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n        />\r\n        {/* Blue Fill */}\r\n        <path\r\n          ref={fillRef}\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#1B1E49\"\r\n          strokeWidth=\"40\"\r\n          strokeLinecap=\"butt\"\r\n          style={{\r\n            transform: \"rotate(90deg) translate(0px, -80px)\",\r\n            strokeDasharray: \"219.9907836914\",\r\n            strokeDashoffset: \"219.9907836914\",\r\n            transition: \"stroke-dashoffset 1s ease\",\r\n          }}\r\n        />\r\n      </svg>\r\n      {/* Inner white circle */}\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CircularProgress\r\n"], "names": [], "mappings": "AAAA,eAAe;AAEf,4CAA4C;AAI5C,mDAAmD;AACnD,iCAAiC;AAEjC,sBAAsB;AACtB,6BAA6B;AAC7B,wCAAwC;AACxC,+DAA+D;AAC/D,gEAAgE;AAChE,uEAAuE;AACvE,QAAQ;AACR,mBAAmB;AAEnB,aAAa;AACb,qDAAqD;AACrD,uEAAuE;AACvE,gBAAgB;AAChB,8BAA8B;AAC9B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,yEAAyE;AACzE,aAAa;AACb,gBAAgB;AAChB,0BAA0B;AAC1B,6BAA6B;AAC7B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,qBAAqB;AACrB,gEAAgE;AAChE,iDAAiD;AACjD,mDAAmD;AACnD,kDAAkD;AAClD,eAAe;AACf,aAAa;AACb,eAAe;AACf,4IAA4I;AAC5I,aAAa;AACb,MAAM;AACN,IAAI;AAEJ,kCAAkC;;;;;;AAIlC;AAFA;;;AAIO,SAAS,iBAAiB,EAAE,QAAQ,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,MAAM;YACZ,MAAM,iBAAiB,KAAK,GAAG,CAAC,UAAU;YAC1C,MAAM,aAAa,AAAC,CAAC,MAAM,cAAc,IAAI,MAAO;YACpD,QAAQ,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,WAAW,QAAQ;QAC9D;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;gBAAW,GAAE;gBAAM,GAAE;gBAAM,SAAQ;;kCAEhD,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,OAAO;4BAAE,WAAW;wBAAsC;;;;;;kCAG5D,8OAAC;wBACC,KAAK;wBACL,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,OAAO;4BACL,WAAW;4BACX,iBAAiB;4BACjB,kBAAkB;4BAClB,YAAY;wBACd;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nconst Banner = ({ imageUrl, title, subtitle }) => {\r\n  return (\r\n    <section className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}>\r\n      {/* Parallax Fixed Background Image - Only for Banner */}\r\n      <div\r\n        className=\"fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0\"\r\n      >\r\n        <img src={imageUrl} alt={title} className=\"w-full h-full object-cover\"/>\r\n      </div>\r\n      {/* <div\r\n        className=\"fixed top-0 left-0 w-full h-full !bg-cover !bg-center -z-10 pointer-events-none\"\r\n        style={backgroundStyles}\r\n      /> */}\r\n\r\n      {/* Dark Overlay */}\r\n      <div className=\"absolute inset-0 bg-[#000]/40 z-0\" />\r\n\r\n      {/* Banner Content */}\r\n      <div className=\"relative z-10 text-center px-4\">\r\n        <h1 className=\"text-3xl md:text-5xl font-medium\">{title}</h1>\r\n        {subtitle && (\r\n          <p className=\"text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Banner;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,qBACE,8OAAC;QAAQ,WAAW,CAAC,0GAA0G,CAAC;;0BAE9H,8OAAC;gBACC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,KAAK;oBAAU,KAAK;oBAAO,WAAU;;;;;;;;;;;0BAQ5C,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}]}