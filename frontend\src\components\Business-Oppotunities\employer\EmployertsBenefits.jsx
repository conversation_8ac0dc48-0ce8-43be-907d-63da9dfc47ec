import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import Image from "next/image";
import React from "react";

const EmployertsBenefits = () => {
  return (
    <section className="bg-white">
      <div className="s_wrapper !pt-0">
        <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-2 lg:mb-4 text-black text-center">
          <SkewFadeInWords text="Benefits to Employers" />
        </h2>
        <div className="mx-auto gap-4 flex flex-col text-[#333] text-center max-w-3xl">
          <p className=" text-justify md:text-start">
            For companies prioritizing employee well-being and recognizing the
            link between financial literacy, productivity, and overall corporate
            success.
          </p>
        </div>
        <div className="flex flex-col md:flex-row gap-4 lg:gap-6 mt-8">
          <div className="w-full">
            <div className="p-6 flex justify-center items-center flex-col text-center">
              <Image src="/images/business-oppotunity/IncreasedProductivity.png" alt="Increased Productivity" width={100} height={100} className="w-[60px] h-[60px] md:w-[100px] md:h-[100px]" />
              <h3 className="text-gray-800 font-semibold mt-4 md:mt-8">Increased Productivity</h3>
              <p className="text-gray-700 mt-2">Financially literate employees are less stressed and more focused, leading to higher productivity.</p>
            </div>
          </div>
          <div className="w-full">
            <div className="p-6 flex justify-center items-center flex-col text-center">
              <Image src="/images/business-oppotunity/EmployeeSatisfaction.png" alt="Employee Satisfaction" width={100} height={100} className="w-[60px] h-[60px] md:w-[100px] md:h-[100px]" />
              <h3 className="text-gray-800 font-semibold mt-4 md:mt-8">Employee Satisfaction</h3>
              <p className="text-gray-700 mt-2">Enhances overall job satisfaction and loyalty.</p>
            </div>
          </div>
          <div className="w-full">
            <div className="p-6 flex justify-center items-center flex-col text-center">
              <Image src="/images/business-oppotunity/ReducedAbsenteeism.png" alt="Reduced Absenteeism" width={100} height={100} className="w-[60px] h-[60px] md:w-[100px] md:h-[100px]" />
              <h3 className="text-gray-800 font-semibold mt-4 md:mt-8">Reduced Absenteeism</h3>
              <p className="text-gray-700 mt-2">Financially secure employees are less likely to miss work due to financial stress or emergencies.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EmployertsBenefits;
