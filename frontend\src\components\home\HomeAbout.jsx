"use client";
import React from "react";
import CountUp from "react-countup";
import { useInView } from "react-intersection-observer";
import { Typewriter } from "react-simple-typewriter";

const HomeAbout = () => {
  const stats = [
    {
      value: 600,
      suffix: " cr+",
      label: "Assets",
      icon: "/images/home/<USER>",
    },
    {
      value: 30,
      suffix: " yrs+",
      label: "Industry experience",
      icon: "/images/home/<USER>",
    },
    {
      value: 2300,
      suffix: "+",
      label: "Clientele",
      icon: "/images/home/<USER>",
    },
    {
      value: 200,
      suffix: "+",
      label: "Insured lives",
      icon: "/images/home/<USER>",
    },
  ];
  return (
    <section className="bg-white pb-24">
      <div className="s_wrapperr ">
        <div className="bg-[#f7f0ec] text-[#101435] py-10 sm:py-12 md:py-16 text-center px-4 sm:px-8 md:px-16 lg:px-32">
          <h2 className="text-2xl sm:text-3xl md:text-5xl font-bold leading-tight">
            <span className="text-red-700 underline ">
              <Typewriter
                words={[
                  "Expertise",
                  "Transparency",
                  "Credibility",
                  "Experience ",
                ]}
                loop={100000}
                cursor
                cursorStyle="|"
                typeSpeed={100}
                deleteSpeed={80}
                delaySpeed={800}
              />
            </span>{" "}
            is our pillar.
          </h2>
          <p className="mt-4 sm:mt-6 max-w-full sm:max-w-2xl mx-auto text-base sm:text-lg text-[#101435] font-medium">
            Our achievements and the scale of our business represent {" "}
            <br className="hidden sm:block" />
            our commitment to your financial success.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 text-center md:max-w-[90%] mx-auto mt-10 lg:mt-16 relative max-w-7xl px-4 sm:px-6 lg:px-8">
          {stats.map((stat, index) => {
            const { ref, inView } = useInView({ triggerOnce: false });
            return (
              <div
                key={index}
                ref={ref}
                className={`flex flex-col items-center w-[90%] relative mx-auto md:w-[100%] h-[100px] justify-center aspect-square mt-6 md:mt-0 ${
                  index !== stats.length - 1
                    ? "md:border-r md:border-gray-300"
                    : ""
                }`}
              >
                {/* <div className="absolute top-[20%] left-[50%] translate-x-[-50%] translate-y-[-50%] opacity-10">
                    <Image
                      src={stat.icon}
                      alt={stat.label}
                      width={100}
                      height={100}
                    />
                  </div> */}
                <div className="md:text-3xl text-2xl font-semibold text-[#101435] lg:text-[44px]">
                  {inView ? <CountUp end={stat.value} duration={2} /> : 0}
                  <span className="md:text-3xl text-2xl text-gray-800">
                    {stat.suffix}
                  </span>
                </div>
                <div className="text-[#931F1D] font-semibold mt-8 text-xs md:text-sm lg:text-lg">
                  {stat.label}
                </div>
              </div>
            );
          })}
          <div className="absolute h-[calc(100%-40px)] w-[0.5px] bg-gray-500 left-[50%] top-[50%] translate-[-50%] md:hidden"></div>
          <div className="absolute w-[calc(100%-40px)] h-[0.5px] bg-gray-500 left-[50%] top-[50%] translate-[-50%] md:hidden"></div>
        </div>
      </div>
    </section>
  );
};

export default HomeAbout;
