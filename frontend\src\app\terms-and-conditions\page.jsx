import TermsAndConditionsMaster from "@/components/termsAndConditions/TermsAndConditionsMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
    <Head>
        <title>Terms and Conditions | Winshine Financial Services</title>
        <meta name="description" content="Read the Terms and Conditions of Winshine Financial Services to understand your rights and responsibilities while using our website and services." />
        <meta name="keywords" content="terms and conditions, user agreement, Winshine policies, financial services terms, website terms" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Terms and Conditions | Winshine Financial Services" />
        <meta property="og:description" content="Review Winshine’s Terms and Conditions to ensure a clear understanding of service usage, policies, and legal obligations." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/investment/2148803950.jpg" />
        <meta property="og:url" content="https://winshine.nipralo.com/terms-and-conditions" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/investment/2148803950.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/terms-and-conditions" />
      </Head>
      <TermsAndConditionsMaster />
    </>
  );
};

export default page;
