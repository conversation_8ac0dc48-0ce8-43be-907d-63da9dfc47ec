"use client"
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register GSAP ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

const ScrollingRowsMobile = () => {
  const firstRowRef = useRef(null);
  const secondRowRef = useRef(null);
  const wrapperRef = useRef(null);

  useEffect(() => {
    // Animation for the first row (moves left on scroll down)
    gsap.to(firstRowRef.current, {
      x: "-50%", // Adjust the value for desired scroll effect
      ease: "none",
      scrollTrigger: {
        trigger: wrapperRef.current,
        start: "top center",
        end: "bottom top",
        scrub: true,
      },
    });

    // Animation for the second row (moves right on scroll down)
    gsap.to(secondRowRef.current, {
      x: "50%", // Adjust the value for desired scroll effect
      ease: "none",
      scrollTrigger: {
        trigger: wrapperRef.current,
        start: "top center",
        end: "bottom top",
        scrub: true,
      },
    });

    // Cleanup on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <div
      ref={wrapperRef}
      className='scrolling-rows-wrapper overflow-hidden md:py-20 bg-white'
    >
      {/* First Row */}
      <div
        ref={firstRowRef}
        className='first-row text-[#222] text-[18px] text-center py-6 pb-4 md:py-10 flex justify-center  w-[240vw] md:w-[200vw]'
        // style={{ width: "200vw" }} // Centered
      >
        <h4 className='border-t border-b border-r md:ml-[-25vw] ml-[-70vw] px-2'>
          Motor Insurance
        </h4>
        <h4 className='border-t border-b border-r  px-2'>Travel Insurance</h4>
        <h4 className='border-t border-b border-r  px-2'>
         Fire Insurance
        </h4>
        <h4 className='border-t border-b border-r  px-2'>
          Tailored Freight Solutions
        </h4>
        <h4 className='border-t border-b border-r hidden sm:block px-2'>
          Theft Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
         Marine Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
          Tailored Freight Solutions
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
         Workmen’s Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
          Group Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
          Society Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
          Asset Insurance
        </h4>
      </div>

      {/* Spacer to provide separation between rows */}
      {/* <div className="spacer h-16"></div> */}

      {/* Second Row */}
      <div
        ref={secondRowRef}
        className='second-row text-[#222] text-[18px] xl:text-[32px] text-center py-6 pt-4 md:py-10 flex justify-center w-[240vw] ]md:w-[200vw] ml-[-100vw] md:!ml-[-120vw] '
        // style={{ marginLeft: "-100vw"}} // Centered
      >
        <h4 className='border-t border-b border-r border-l px-2'>
          Asset Insurance
        </h4>
        <h4 className='border-t border-b border-r px-2'>Society Insurance</h4>
        <h4 className='border-t border-b border-r px-2'>
       Group Insurance
        </h4>
        <h4 className='border-t border-b border-r px-2'>
       Workmen’s Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden sm:blockpx-2'>
         Marine Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:blockpx-2'>
         Theft Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
         Fire Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
         Travel Insurance
        </h4>
        <h4 className='border-t border-b border-r hidden md:block px-2'>
         Motor Insurance
        </h4>
      </div>
    </div>
  );
};

export default ScrollingRowsMobile;
