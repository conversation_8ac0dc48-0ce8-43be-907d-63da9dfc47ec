name: Deploy to 45 FTP

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm install --force
        working-directory: ./frontend

      - name: Build Project
        run: npm run build
        working-directory: ./frontend

      - name: Get commit message
        id: commit_msg
        run: echo "message=$(git log -1 --pretty=%B)" >> $GITHUB_OUTPUT

      - name: Deploy to FTP
        if: contains(steps.commit_msg.outputs.message, 'frontend-deploy')
        uses: SamKirkland/FTP-Deploy-Action@4.3.3
        with:
          server: ${{ secrets.FTP_HOST_82 }}
          username: ${{ secrets.FTP_USERNAME_45 }}
          password: ${{ secrets.FTP_PASSWORD_45 }}
          local-dir: frontend/.next/
