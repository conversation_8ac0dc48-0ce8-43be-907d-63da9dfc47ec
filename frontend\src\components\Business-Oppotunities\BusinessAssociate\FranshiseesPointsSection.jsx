import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import React from "react";
import LeftRightPoints from "./LeftRightPoints";

const FranshiseesPointsSection = () => {
  return (
    <section className="bg-white">
      <div className="s_wrapper">
        <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-2 lg:mb-4 text-black text-center">
          <SkewFadeInWords text="Are You Ready To Be Your Own Boss?" />
        </h2>
        <div className="mx-auto gap-4 flex flex-col text-[#333] text-center max-w-3xl">
          <p className=" text-justify md:text-center">
            Joining Winshine means becoming part of a legacy, gaining access to
            unparalleled support, and paving the way for your success without
            the usual hurdles. We empower you to thrive in the competitive
            financial services landscape by providing the tools and resources
            needed to build a profitable and sustainable business.
          </p>
        </div>

        <LeftRightPoints
          icon={"/images/business-oppotunity/PointstoNote.png"}
          title={"Points to Note"}
          description={
            "This is not a job it's a self-employment / business opportunity. No fixed salary the scope for income growth is unimaginable. Zero monetary cost to associate with us your investment is in terms of time and efforts. Our office space is in Ghatkopar East regular attendance is encouraged for a professional learning experience. Consider this opportunity as your permanent source of income don't apply if you don't enjoy financial markets."
          }
        />
        <LeftRightPoints
        iconOnRight={true}
          icon={"/images/business-oppotunity/RequiredSkills.png"}
          title={"Required Skills"}
          description={
            "Good interpersonal skills for networking and clientele development. A solid grasp of financial concepts, products, and their functioning. Proactive approach and an aggressive business development mindset. Ability to perform independently. Willingness to continuously learn. Understanding of various financial risks and how to manage them."
          }
        />
        <LeftRightPoints
          icon={"/images/business-oppotunity/EligibilityCriteria.png"}
          title={"Eligibility Criteria"}
          description={
            "No convictions or pending proceedings under any Indian Law. Minimum graduate qualification (graduate students can apply). Willingness to clear NISM certification for equity, commodities, and derivatives if interested in broking."
          }
        />
      </div>
    </section>
  );
};

export default FranshiseesPointsSection;
