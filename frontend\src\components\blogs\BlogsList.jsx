"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import Image from "next/image";


function formatDate(dateInput) {
  const date = new Date(dateInput);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

const BlogsList = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/blogs?populate=*`,
          {
            headers: {
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setBlogs(data?.data); // Strapi response format
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  if (loading)
    return (
      <div className="text-black h-[50vh] w-screen flex justify-center items-center bg-white">
        <p>Loading...</p>
      </div>
    );
  if (error)
    return (
      <div className="text-red-600 h-[50vh] w-screen flex justify-center items-center bg-white">
        <p>Error: {error}</p>
      </div>
    );

  return (
    <section className="bg-white">
      <div className="s_wrapper">
        {/* Header */}
        <div className="text-center max-w-2xl mx-auto md:mb-12">
          <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4 text-[#040404]">
            <SkewFadeInWords text="Latest Insights" />
          </h2>
        </div>

        {/* Blog Grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {blogs.map((post) => (
            <div
              key={post?.id}
              className="bg-white shadow-md rounded-xl overflow-hidden transition "
            >
              {/* {post?.cover?.url && <Image
                height={400}
                width={500}
                src={post?.cover?.url}
                alt={post?.title}
                className="w-full h-60 object-cover"
              />} */}
              <div className="p-5">
                <h2 className="text-xl text-[#040404] line-clamp-2">
                  {post?.title}
                </h2>
                <p className="text-sm text-gray-500 mb-2">
                  {formatDate(post?.published_on)}
                </p>
                <p className="text-gray-600 mb-4 line-clamp-4">
                  {post?.description}
                </p>
                <Link
                  href={`/blogs/${post?.slug}`}
                  className="gradient-button px-4 py-2 rounded-xl !ml-0 font-medium"
                >
                  Read more →
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogsList;
