import Link from "next/link";
import React from "react";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import { CheckCircle } from "lucide-react";

function ClientPrivileges() {
  return (
    <div className="bg-[#F9F3F1]">
      <div className="s_wrapper ">
        <div className="mx-auto">
          <div className="flex flex-col lg:flex-row items-start md:gap-8 gap-2">
            {/* Images section with specific positioning */}
            <h2 className="mb-4 lg:hidden text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4 text-[#040404]">
              <SkewFadeInWords text="Exclusive Client Privileges" />
            </h2>
            <div className="relative lg:w-1/2 flex  w-full gap-[4%]">
              <div className="md:w-[46%] rounded-lg overflow-hidden shadow-md md:h-[400px] md:mt-12">
                <img
                  src="/images/home/<USER>"
                  alt="Team collaborating in office"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="w-[46%] rounded-lg overflow-hidden shadow-md md:max-w-[46%] hidden md:block md:h-[400px] mb-6 md:mb-0">
                <img
                   src="/images/home/<USER>"
                  alt="Molecular model in hands"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Content section */}
            <div className="lg:w-1/2 md:mt-0">
              <div>
                <h2 className="mb-4 hidden lg:block text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4 text-[#040404]">
                  <SkewFadeInWords text="Exclusive Client Privileges" />
                </h2>
                <p className="text-gray-700 max-w-2xl mx-auto mb-6  text-justify md:text-start">
                  Unlock tailored financial benefits and exclusive offers
                  designed to enhance your investment experience, empower
                  informed decisions, and provide you with unmatched value
                  across every transaction.
                </p>
              </div>

              <div className="flex flex-col md:flex-row md:gap-4 text-black">
                <div className="md:w-1/2">
                  <ul>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Winshine App Integration
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        1 to 1 with Experts
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Trading at Fingertips
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Real-Time Trading Calls
                      </p>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2">
                  <ul>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Margin Trading Facility
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Financial Literacy Programs
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Value Added Services
                      </p>
                    </li>
                    <li className="flex gap-2 items-start mt-4 2xl:mt-6">
                      <CheckCircle className="min-w-4 mt-1.5 min-h-4 h-4 w-4 ml-2 text-[#ae2c2f] fill-white" />
                      <p className="relative font-semibold text-lg after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[1px] after:bg-[#ae2c2f] after:transition-all after:duration-300 hover:after:w-full">
                        Complete Investment Universe
                      </p>
                    </li>
                  </ul>
                </div>
              </div>
              <Link href="/services">
                <button className="gradient-button text-[#ffffff] px-4 py-2 rounded-xl md:!ml-0 mx-auto !mt-8 font-semibold">
                  Know More
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ClientPrivileges;
