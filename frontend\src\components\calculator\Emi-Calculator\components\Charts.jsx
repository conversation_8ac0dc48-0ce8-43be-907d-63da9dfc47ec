"use client";

import { useEffect, useRef } from "react";
import { Chart, registerables } from "chart.js";

Chart.register(...registerables);

export function PieChart({ data }) {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext("2d");

      chartInstance.current = new Chart(ctx, {
        type: "pie",
        data: {
          labels: data.map((item) => item.name),
          datasets: [
            {
              data: data.map((item) => item.value),
              backgroundColor: data.map((item) => item.color),
              borderWidth: 0,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: true,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: (context) => {
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const value = context.raw;
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${context.label}: ${percentage}%`;
                },
              },
            },
          },
        },
      });
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data]);

  return (
    <div className="w-full h-64 flex items-center justify-center">
      <canvas ref={chartRef}></canvas>
    </div>
  );
}

export function BarChart({ data }) {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext("2d");

      chartInstance.current = new Chart(ctx, {
        type: "bar",

        data: {
          labels: data.map((item) => item.year),
          datasets: [
            {
              label: "Principal",
              data: data.map((item) => item.principal),
              backgroundColor: "#1e2a5a",
              stack: "Stack 0",
            },
            {
              label: "Interest",
              data: data.map((item) => item.interest),
              backgroundColor: "#b22222",
              stack: "Stack 0",
            },
            {
              label: "Balance",
              data: data.map((item) => item.balance),
              type: "line",
              borderColor: "#4caf50",
              borderWidth: 2,
              fill: false,
              tension: 0.4,
              yAxisID: "y1",
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: true,
          scales: {
            x: {
              stacked: true,
              grid: {
                display: false,
              },
            },
            y: {
              stacked: true,
              beginAtZero: true,
              title: {
                display: true,
                text: "Amount",
              },
              ticks: {
                callback: (value) => {
                  if (value >= 1000000) {
                    return "₹ " + (value / 1000000).toFixed(1) + "M";
                  } else if (value >= 1000) {
                    return "₹ " + (value / 1000).toFixed(1) + "K";
                  }
                  return "₹ " + value;
                },
              },
            },
            y1: {
              position: "right",
              beginAtZero: true,
              title: {
                display: true,
                text: "Balance",
              },
              grid: {
                display: false,
              },
              ticks: {
                callback: (value) => {
                  if (value >= 1000000) {
                    return "₹ " + (value / 1000000).toFixed(1) + "M";
                  } else if (value >= 1000) {
                    return "₹ " + (value / 1000).toFixed(1) + "K";
                  }
                  return "₹ " + value;
                },
              },
            },
          },
          plugins: {
            legend: {
              position: "bottom",
            },
            tooltip: {
              callbacks: {
                label: (context) => {
                  let label = context.dataset.label || "";
                  if (label) {
                    label += ": ";
                  }
                  if (context.parsed.y !== null) {
                    label += new Intl.NumberFormat("en-IN", {
                      style: "currency",
                      currency: "INR",
                      maximumFractionDigits: 0,
                    }).format(context.parsed.y);
                  }
                  return label;
                },
              },
            },
          },
        },
      });
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data]);

  return (
    <div className="w-full lg:h-80 flex justify-center items-center">
      <canvas ref={chartRef}></canvas>
    </div>
  );
}
