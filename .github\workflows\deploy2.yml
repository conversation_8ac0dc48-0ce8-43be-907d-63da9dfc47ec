name: Deploy to Server 194
on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
# ====
    steps:
    - name: Checkout the repository
      uses: actions/checkout@v2

    - name: Install sshpass
      run: |
        sudo apt-get update
        sudo apt-get install -y sshpass

    - name: Deploy Frontend or Backend based on commit message
      run: |
        # Check for frontend-deploy
        if echo "${{ github.event.head_commit.message }}" | grep -q "frontend-deploy"; then
          echo "Deploying Frontend..."
          sshpass -p "${{ secrets.SSH_PASSWORD }}" ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_IP }} '
            cd /var/www/html/app/live/winshine_website &&
            git config pull.rebase false &&
            git pull origin main &&
            cd frontend &&
            if echo "${{ github.event.head_commit.message }}" | grep -q "frontend-deploy-npm-install"; then
              npm install
            fi
            chmod +x ./frontend-deploy.sh
            ./frontend-deploy.sh
          '
        fi

        # Check for backend-deploy
        if echo "${{ github.event.head_commit.message }}" | grep -q "backend-deploy"; then
          echo "Deploying Backend..."
          sshpass -p "${{ secrets.SSH_PASSWORD }}" ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_IP }} '
            cd /var/www/html/app/live/winshine_website &&
            git config pull.rebase false &&
            git pull origin main &&
            cd backend &&
            if echo "${{ github.event.head_commit.message }}" | grep -q "backend-deploy-npm-install"; then
              npm install
            fi
            chmod +x ./backend-deploy.sh
            ./backend-deploy.sh
          '
        fi

        # If no deployment action was triggered
        if ! (echo "${{ github.event.head_commit.message }}" | grep -q "frontend-deploy" || echo "${{ github.event.head_commit.message }}" | grep -q "backend-deploy"); then
          echo "No frontend or backend deployment triggered."
        fi
