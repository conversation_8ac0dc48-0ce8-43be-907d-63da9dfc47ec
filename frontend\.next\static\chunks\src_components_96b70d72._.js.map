{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/circular-progress.jsx"], "sourcesContent": ["// \"use client\"\r\n\r\n// import { useEffect, useRef } from \"react\"\r\n\r\n\r\n\r\n// export function CircularProgress({ progress }) {\r\n//   const fillRef = useRef(null)\r\n\r\n//   useEffect(() => {\r\n//     if (fillRef.current) {\r\n//       const max = -219.99078369140625\r\n//       const cappedProgress = progress > 100 ? 100 : progress\r\n//       const dashOffset = ((100 - cappedProgress) / 100) * max\r\n//       fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n//     }\r\n//   }, [progress])\r\n\r\n//   return (\r\n//     <div className=\"relative w-[245px] h-[215px]\">\r\n//       <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n//         <path\r\n//           className=\"track\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#ac272b\"\r\n//           strokeWidth=\"40\"\r\n//           style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n//         />\r\n//         <path\r\n//           ref={fillRef}\r\n//           className=\"fill\"\r\n//           d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n//           fill=\"none\"\r\n//           stroke=\"#1B1E49\"\r\n//           strokeWidth=\"40\"\r\n//           style={{\r\n//             transform: \"rotate(90deg) translate(0px, -80px)\",\r\n//             strokeDasharray: \"219.9907836914\",\r\n//             strokeDashoffset: \"-219.9907836914\",\r\n//             transition: \"stroke-dashoffset 1s\",\r\n//           }}\r\n//         />\r\n//       </svg>\r\n//       <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n//     </div>\r\n//   )\r\n// }\r\n\r\n// export default CircularProgress\r\n\r\n\"use client\"\r\n\r\nimport { useEffect, useRef } from \"react\"\r\n\r\nexport function CircularProgress({ progress }) {\r\n  const fillRef = useRef(null)\r\n\r\n  useEffect(() => {\r\n    if (fillRef.current) {\r\n      const max = 219.99078369140625\r\n      const cappedProgress = Math.min(progress, 100)\r\n      const dashOffset = ((100 - cappedProgress) / 100) * max\r\n      fillRef.current.style.strokeDashoffset = dashOffset.toString()\r\n    }\r\n  }, [progress])\r\n\r\n  return (\r\n    <div className=\"relative w-[245px] h-[245px] rounded-full overflow-hidden\">\r\n      <svg className=\"progress\" x=\"0px\" y=\"0px\" viewBox=\"0 0 80 80\">\r\n        {/* Red Track */}\r\n        <path\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#ac272b\"\r\n          strokeWidth=\"42\"\r\n          style={{ transform: \"rotate(90deg) translate(0px, -80px)\" }}\r\n        />\r\n        {/* Blue Fill */}\r\n        <path\r\n          ref={fillRef}\r\n          d=\"M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0\"\r\n          fill=\"none\"\r\n          stroke=\"#1B1E49\"\r\n          strokeWidth=\"40\"\r\n          strokeLinecap=\"butt\"\r\n          style={{\r\n            transform: \"rotate(90deg) translate(0px, -80px)\",\r\n            strokeDasharray: \"219.9907836914\",\r\n            strokeDashoffset: \"219.9907836914\",\r\n            transition: \"stroke-dashoffset 1s ease\",\r\n          }}\r\n        />\r\n      </svg>\r\n      {/* Inner white circle */}\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full\" />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CircularProgress\r\n"], "names": [], "mappings": "AAAA,eAAe;AAEf,4CAA4C;AAI5C,mDAAmD;AACnD,iCAAiC;AAEjC,sBAAsB;AACtB,6BAA6B;AAC7B,wCAAwC;AACxC,+DAA+D;AAC/D,gEAAgE;AAChE,uEAAuE;AACvE,QAAQ;AACR,mBAAmB;AAEnB,aAAa;AACb,qDAAqD;AACrD,uEAAuE;AACvE,gBAAgB;AAChB,8BAA8B;AAC9B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,yEAAyE;AACzE,aAAa;AACb,gBAAgB;AAChB,0BAA0B;AAC1B,6BAA6B;AAC7B,yDAAyD;AACzD,wBAAwB;AACxB,6BAA6B;AAC7B,6BAA6B;AAC7B,qBAAqB;AACrB,gEAAgE;AAChE,iDAAiD;AACjD,mDAAmD;AACnD,kDAAkD;AAClD,eAAe;AACf,aAAa;AACb,eAAe;AACf,4IAA4I;AAC5I,aAAa;AACb,MAAM;AACN,IAAI;AAEJ,kCAAkC;;;;;;AAIlC;;;AAFA;;AAIO,SAAS,iBAAiB,EAAE,QAAQ,EAAE;;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,MAAM;gBACZ,MAAM,iBAAiB,KAAK,GAAG,CAAC,UAAU;gBAC1C,MAAM,aAAa,AAAC,CAAC,MAAM,cAAc,IAAI,MAAO;gBACpD,QAAQ,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,WAAW,QAAQ;YAC9D;QACF;qCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;gBAAW,GAAE;gBAAM,GAAE;gBAAM,SAAQ;;kCAEhD,6LAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,OAAO;4BAAE,WAAW;wBAAsC;;;;;;kCAG5D,6LAAC;wBACC,KAAK;wBACL,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,OAAO;4BACL,WAAW;4BACX,iBAAiB;4BACjB,kBAAkB;4BAClB,YAAY;wBACd;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GA3CgB;KAAA;uCA6CD", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/SipCalculator.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { CircularProgress } from \"./circular-progress\";\r\n\r\nexport default function SipCalculator() {\r\n  // State for form inputs\r\n  const [investmentType, setInvestmentType] = useState(\"know-target-amount\");\r\n  const [investmentMode, setInvestmentMode] = useState(\"sip\");\r\n  const [targetAmount, setTargetAmount] = useState(\"1500000\");\r\n  const [duration, setDuration] = useState(\"10\");\r\n  const [rateOfReturn, setRateOfReturn] = useState(\"12\");\r\n\r\n  // State for calculation results\r\n  const [investedAmount, setInvestedAmount] = useState(0);\r\n  const [returns, setReturns] = useState(0);\r\n  const [totalWealth, setTotalWealth] = useState(0);\r\n  const [monthlyInvestment, setMonthlyInvestment] = useState(0);\r\n  const [graphProgress, setGraphProgress] = useState(0);\r\n\r\n  // State for errors\r\n  const [errors, setErrors] = useState({\r\n    targetAmount: \"\",\r\n    duration: \"\",\r\n    rateOfReturn: \"\",\r\n    general: \"\",\r\n  });\r\n\r\n  // ROI array for slider\r\n  const roiArr = useRef([]);\r\n\r\n  // Initialize ROI array\r\n  useEffect(() => {\r\n    const tempRoiArr = [];\r\n    for (let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01) {\r\n      tempRoiArr.push(Number.parseFloat(i).toFixed(2));\r\n    }\r\n    roiArr.current = tempRoiArr;\r\n  }, []);\r\n\r\n  // Format number with commas\r\n  const numWithCommas = (num) => {\r\n    return num.toLocaleString(\"en-IN\");\r\n  };\r\n\r\n  // Remove commas from number string\r\n  const removeCommas = (number) => {\r\n    return number.toString().replace(/,/g, \"\");\r\n  };\r\n\r\n  // Validate input range\r\n  const validateRangeInput = (value, max, min, field) => {\r\n    const numValue = Number(value);\r\n    if (isNaN(numValue) || numValue === \"\") {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        [field]: \"Please enter a valid number\",\r\n      }));\r\n      return false;\r\n    }\r\n\r\n    if (numValue < min) {\r\n      setErrors((prev) => ({ ...prev, [field]: `Minimum value is ${min}` }));\r\n      return false;\r\n    }\r\n\r\n    if (numValue > max) {\r\n      setErrors((prev) => ({ ...prev, [field]: `Maximum value is ${max}` }));\r\n      return false;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, [field]: \"\" }));\r\n    return true;\r\n  };\r\n  // PMT calculation function\r\n  const PMT = (rate, nper, pv, fv, type) => {\r\n    if (rate === 0) return -(fv + pv) / nper;\r\n\r\n    const term = Math.pow(1 + rate, nper);\r\n    return (rate * (fv + pv * term)) / ((term - 1) * (1 + rate * (type || 0)));\r\n  };\r\n\r\n  // Future value calculation\r\n  const futureValue = (rate, nper, pmt, pv, type) => {\r\n    if (rate === 0) return -(pv + pmt * nper);\r\n\r\n    const term = Math.pow(1 + rate, nper);\r\n    return -pv * term - (pmt * (term - 1) * (1 + rate * (type || 0))) / rate;\r\n  };\r\n\r\n  // Present value calculation\r\n  const presentValue = (rate, nper, pmt, fv, type) => {\r\n    if (!fv) fv = 0;\r\n    if (!type) type = 0;\r\n\r\n    const pow = Math.pow(1 + rate, nper);\r\n    let pv;\r\n\r\n    if (rate) {\r\n      pv = (-pmt * (1 + rate * type) * (pow - 1)) / (rate * pow) - fv / pow;\r\n    } else {\r\n      pv = -1 * (fv + pmt * nper);\r\n    }\r\n\r\n    return pv;\r\n  };\r\n\r\n  // Calculate results based on inputs\r\n  const calculateResults = () => {\r\n    // Validate inputs\r\n    const targetAmtValid = validateRangeInput(\r\n      removeCommas(targetAmount),\r\n      \"1000000\",\r\n      \"1\",\r\n      \"targetAmount\"\r\n    );\r\n    const durationValid = validateRangeInput(duration, \"50\", \"1\", \"duration\");\r\n    const roiValid = validateRangeInput(\r\n      rateOfReturn,\r\n      \"100\",\r\n      \"1\",\r\n      \"rateOfReturn\"\r\n    );\r\n\r\n    if (!targetAmtValid || !durationValid || !roiValid) {\r\n      setErrors((prev) => ({\r\n        ...prev,\r\n        general:\r\n          \"Please enter numeric inputs within the suggested range to get accurate results\",\r\n      }));\r\n      return;\r\n    }\r\n\r\n    setErrors((prev) => ({ ...prev, general: \"\" }));\r\n\r\n    let amtValue = 0;\r\n    let investVal = 0;\r\n    let profit = 0;\r\n    let profitPercent = 0;\r\n    const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));\r\n    const roi = Math.pow(1 + Number.parseFloat(rateOfReturn) / 100, 1 / 12) - 1;\r\n    const timePeriods = Number.parseInt(duration) * 12;\r\n\r\n    if (investmentType === \"know-investment-amount\") {\r\n      const monthlyRate = rateOfReturn / 100 / 12;\r\n      const periods = duration * 12;\r\n\r\n      if (investmentMode === \"sip\") {\r\n        // Future value of SIP\r\n        const fv = futureValue(monthlyRate, periods, -targetAmtValue, 0, 1);\r\n        setTotalWealth(Math.round(fv));\r\n        setInvestedAmount(Math.round(targetAmtValue * periods));\r\n        setReturns(Math.round(fv - targetAmtValue * periods));\r\n      } else if (investmentMode === \"lumpsum\") {\r\n        // Future value of lumpsum\r\n        const fv = targetAmtValue * Math.pow(1 + rateOfReturn / 100, duration);\r\n        setTotalWealth(Math.round(fv));\r\n        setInvestedAmount(Math.round(targetAmtValue));\r\n        setReturns(Math.round(fv - targetAmtValue));\r\n      }\r\n    } else if (investmentType === \"know-target-amount\") {\r\n      const monthlyRate = rateOfReturn / 100 / 12;\r\n      const periods = duration * 12;\r\n\r\n      if (investmentMode === \"sip\") {\r\n        // Required SIP amount\r\n        const pmt = PMT(monthlyRate, periods, 0, -targetAmtValue, 1);\r\n        setMonthlyInvestment(Math.round(pmt));\r\n        setInvestedAmount(Math.round(pmt * periods));\r\n        setReturns(Math.round(targetAmtValue - pmt * periods));\r\n      } else if (investmentMode === \"lumpsum\") {\r\n        // Required lumpsum amount\r\n        const pv = targetAmtValue / Math.pow(1 + rateOfReturn / 100, duration);\r\n        setMonthlyInvestment(Math.round(pv));\r\n        setInvestedAmount(Math.round(pv));\r\n        setReturns(Math.round(targetAmtValue - pv));\r\n      }\r\n    }\r\n\r\n    // setReturns(Math.round(amtValue < 1 ? profit - 1 : profit));\r\n    // setTotalWealth(targetAmtValue);\r\n  };\r\n\r\n  // Handle input changes\r\n  const handleInputChange = (e, setter) => {\r\n    const { value } = e.target;\r\n\r\n    if (e.target.type === \"text\") {\r\n      // For text inputs, update the value directly\r\n      setter(value);\r\n    } else if (e.target.type === \"range\") {\r\n      // For range inputs, update the corresponding text input\r\n      if (e.target.id === \"ill_int_rates_value\") {\r\n        // Handle ROI slider specially\r\n        const roiValue = roiArr.current[Number.parseInt(value)];\r\n        setter(roiValue);\r\n      } else {\r\n        setter(value);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Format target amount with commas\r\n  const formatTargetAmount = (value) => {\r\n    const numValue = Number.parseFloat(removeCommas(value));\r\n    if (isNaN(numValue)) return \"0\";\r\n    return numWithCommas(numValue);\r\n  };\r\n\r\n  // Calculate on input change\r\n  useEffect(() => {\r\n    calculateResults();\r\n  }, [targetAmount, duration, rateOfReturn, investmentType, investmentMode]);\r\n\r\n  // Get ROI slider value\r\n  const getRoiSliderValue = () => {\r\n    const index = roiArr.current.indexOf(rateOfReturn);\r\n    return index >= 0 ? index : roiArr.current.indexOf(\"12.00\");\r\n  };\r\n\r\n  // Get text for result display\r\n  const getResultText = () => {\r\n    let preText = \"\";\r\n    let postText = \"\";\r\n\r\n    if (investmentType === \"know-target-amount\") {\r\n      if (investmentMode === \"sip\") {\r\n        preText = \"Invest\";\r\n        postText = \"every month to reach your target amount\";\r\n      } else if (investmentMode === \"quarterly\") {\r\n        preText = \"Invest\";\r\n        postText = \"every quarter to reach your target amount\";\r\n      } else {\r\n        preText = \"Make a one-time investment of\";\r\n        postText = \"to reach your target amount\";\r\n      }\r\n    } else {\r\n      if (investmentMode === \"sip\") {\r\n        preText = \"Your monthly investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      } else if (investmentMode === \"quarterly\") {\r\n        preText = \"Your quarterly investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      } else {\r\n        preText = \"Your one-time investment of\";\r\n        postText = \"will grow to the amount shown\";\r\n      }\r\n    }\r\n\r\n    return { preText, postText };\r\n  };\r\n\r\n  const { preText, postText } = getResultText();\r\n\r\n  return (\r\n    <section className=\"bg-[#fff] text-black py-8\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <h1 className=\"text-3xl font-bold text-[#2e4765] mb-6 text-center\">\r\n          SIP Calculator: Systematic Investment Plan Calculator Online\r\n        </h1>\r\n\r\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\r\n          <div className=\"flex flex-col lg:flex-row\">\r\n            {/* Left side - Inputs */}\r\n            <div className=\"w-full lg:w-1/2 p-6\">\r\n              {/* Investment Mode Tabs */}\r\n              <div className=\"flex mb-6 gap-3\">\r\n                <button\r\n                  className={`px-4 py-2 rounded-lg cursor-pointer ${\r\n                    investmentMode === \"sip\"\r\n                      ? \"bg-[#1b1e49] text-white\"\r\n                      : \"bg-gray-100\"\r\n                  }`}\r\n                  onClick={() => setInvestmentMode(\"sip\")}\r\n                >\r\n                  SIP\r\n                </button>\r\n                <button\r\n                  className={`px-4 py-2 rounded-lg cursor-pointer ${\r\n                    investmentMode === \"lumpsum\"\r\n                      ? \"bg-[#1b1e49] text-white\"\r\n                      : \"bg-gray-100\"\r\n                  }`}\r\n                  onClick={() => setInvestmentMode(\"lumpsum\")}\r\n                >\r\n                  Lumpsum\r\n                </button>\r\n              </div>\r\n\r\n              {/* Amount Input */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex justify-between items-center w-full\">\r\n                  <label className=\"block text-gray-700 text-xl\">Amount</label>\r\n                  <div className=\"relative\">\r\n                    <span className=\"absolute left-3 top-2.5 text-gray-500\">\r\n                      ₹\r\n                    </span>\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"w-full max-w-28 p-2 pl-8 border border-gray-300 rounded-md\"\r\n                      value={formatTargetAmount(targetAmount)}\r\n                      onChange={(e) => handleInputChange(e, setTargetAmount)}\r\n                    />\r\n                  </div>\r\n                </div>\r\n                {errors.targetAmount && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">\r\n                    {errors.targetAmount}\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"500\"\r\n                    max=\"1000000\"\r\n                    step=\"500\"\r\n                    value={removeCommas(targetAmount)}\r\n                    onChange={(e) => handleInputChange(e, setTargetAmount)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>₹ 500</span>\r\n                    <span>₹ 10,00,000</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Expected Return Rate */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex justify-between items-center w-full\">\r\n                  <label className=\"block text-gray-700 mb-2\">\r\n                    Expected Return Rate (p.a)\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"w-full max-w-16 py-2 px-4 border border-gray-300 rounded-md \"\r\n                      value={rateOfReturn}\r\n                      onChange={(e) => handleInputChange(e, setRateOfReturn)}\r\n                    />\r\n                    <span className=\"absolute right-3 top-2 text-gray-500\">\r\n                      %\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                {errors.rateOfReturn && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">\r\n                    {errors.rateOfReturn}\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"5\"\r\n                    max=\"30\"\r\n                    step=\"0.5\"\r\n                    value={rateOfReturn}\r\n                    onChange={(e) => handleInputChange(e, setRateOfReturn)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>5%</span>\r\n                    <span>30%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* SIP Time Period */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex justify-between items-center w-full\">\r\n                  <label className=\"block text-gray-700 mb-2\">\r\n                    SIP time period\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"w-full max-w-24 py-2 px-4 border border-gray-300 rounded-md \"\r\n                      value={duration}\r\n                      onChange={(e) => handleInputChange(e, setDuration)}\r\n                    />\r\n                    <span className=\"absolute right-3 top-2.5 text-gray-500\">\r\n                      Years\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                {errors.duration && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">{errors.duration}</p>\r\n                )}\r\n\r\n                <div className=\"mt-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"1\"\r\n                    max=\"40\"\r\n                    step=\"1\"\r\n                    value={duration}\r\n                    onChange={(e) => handleInputChange(e, setDuration)}\r\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]\"\r\n                  />\r\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                    <span>0 years</span>\r\n                    <span>40 years</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side - Results */}\r\n            <div className=\"w-full lg:w-1/2 p-6 bg-gray-50\">\r\n              <div className=\"flex flex-col justify-center items-center mb-8\">\r\n                <div className=\"w-full h-full relative mb-4 flex justify-center\">\r\n                  <CircularProgress progress={graphProgress} />\r\n                </div>\r\n\r\n                <div className=\"text-center mt-3\">\r\n                  <p className=\"text-gray-600 mb-1\">\r\n                    {investmentMode === \"sip\"\r\n                      ? \"SIP per month\"\r\n                      : \"Investment amount\"}\r\n                  </p>\r\n                  <p className=\"text-2xl font-bold\">\r\n                    ₹ {numWithCommas(monthlyInvestment)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-4 h-4 bg-[#f47321] mr-3\"></div>\r\n                  <div>\r\n                    <p className=\"text-gray-600 text-sm\">Invested amount</p>\r\n                    <p className=\"font-semibold\">\r\n                      ₹ {numWithCommas(investedAmount)}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-4 h-4 bg-[#ae2f33] mr-3\"></div>\r\n                  <div>\r\n                    <p className=\"text-gray-600 text-sm\">Est. returns</p>\r\n                    <p className=\"font-semibold\">₹ {numWithCommas(returns)}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-8 p-4 bg-gray-100 rounded-lg\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <p className=\"text-gray-600\">Total amount:</p>\r\n                  <p className=\"text-2xl font-bold text-[#2e4765]\">\r\n                    ₹ {numWithCommas(totalWealth)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {errors.general && (\r\n                <div className=\"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n                  {errors.general}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"mt-6 text-center text-sm text-gray-600\">\r\n                <p>\r\n                  {preText}{\" \"}\r\n                  <span className=\"font-semibold\">\r\n                    ₹ {numWithCommas(monthlyInvestment)}\r\n                  </span>{\" \"}\r\n                  {postText}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Additional Information Section */}\r\n        <div className=\"mt-8 bg-white rounded-lg shadow-md p-6\">\r\n          <h2 className=\"text-2xl font-bold text-[#2e4765] mb-4\">\r\n            What is SIP Calculator?\r\n          </h2>\r\n          <div className=\"prose max-w-none\">\r\n            <p className=\"text-gray-700 mb-4\">\r\n              A SIP (Systematic Investment Plan) calculator is a financial tool\r\n              that helps you estimate the future value of your mutual fund\r\n              investments made through SIP. It calculates the potential returns\r\n              based on your monthly investment amount, investment duration, and\r\n              expected rate of return.\r\n            </p>\r\n\r\n            <h3 className=\"text-xl font-semibold text-[#2e4765] mb-3\">\r\n              How does SIP Calculator work?\r\n            </h3>\r\n            <p className=\"text-gray-700 mb-4\">\r\n              The SIP calculator uses the compound interest formula to calculate\r\n              the maturity amount. It considers the power of compounding, where\r\n              your returns also earn returns over time. The calculator takes\r\n              into account:\r\n            </p>\r\n            <ul className=\"list-disc list-inside text-gray-700 mb-4 space-y-2\">\r\n              <li>Monthly investment amount</li>\r\n              <li>Investment duration (in years)</li>\r\n              <li>Expected annual rate of return</li>\r\n              <li>Frequency of investment (monthly, quarterly, or lumpsum)</li>\r\n            </ul>\r\n\r\n            <h3 className=\"text-xl font-semibold text-[#2e4765] mb-3\">\r\n              Benefits of using SIP Calculator\r\n            </h3>\r\n            <ul className=\"list-disc list-inside text-gray-700 mb-4 space-y-2\">\r\n              <li>Plan your financial goals effectively</li>\r\n              <li>Understand the power of compounding</li>\r\n              <li>Compare different investment scenarios</li>\r\n              <li>Make informed investment decisions</li>\r\n              <li>Track your wealth creation journey</li>\r\n            </ul>\r\n\r\n            <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4 mt-6\">\r\n              <p className=\"text-blue-800\">\r\n                <strong>Disclaimer:</strong> The calculations provided by this\r\n                SIP calculator are for illustrative purposes only. Actual\r\n                returns may vary based on market conditions and fund\r\n                performance. Please consult with a financial advisor before\r\n                making investment decisions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gCAAgC;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,cAAc;QACd,UAAU;QACV,cAAc;QACd,SAAS;IACX;IAEA,uBAAuB;IACvB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAExB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,OAAO,UAAU,CAAC,EAAE,OAAO,CAAC,MAAM,QAAQ,KAAK,KAAM;gBACnE,WAAW,IAAI,CAAC,OAAO,UAAU,CAAC,GAAG,OAAO,CAAC;YAC/C;YACA,OAAO,OAAO,GAAG;QACnB;kCAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,OAAO,IAAI,cAAc,CAAC;IAC5B;IAEA,mCAAmC;IACnC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,QAAQ,GAAG,OAAO,CAAC,MAAM;IACzC;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,OAAO,KAAK,KAAK;QAC3C,MAAM,WAAW,OAAO;QACxB,IAAI,MAAM,aAAa,aAAa,IAAI;YACtC,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;YACD,OAAO;QACT;QAEA,IAAI,WAAW,KAAK;YAClB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE,CAAC,iBAAiB,EAAE,KAAK;gBAAC,CAAC;YACpE,OAAO;QACT;QAEA,IAAI,WAAW,KAAK;YAClB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE,CAAC,iBAAiB,EAAE,KAAK;gBAAC,CAAC;YACpE,OAAO;QACT;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAG,CAAC;QAC7C,OAAO;IACT;IACA,2BAA2B;IAC3B,MAAM,MAAM,CAAC,MAAM,MAAM,IAAI,IAAI;QAC/B,IAAI,SAAS,GAAG,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI;QAEpC,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,MAAM;QAChC,OAAO,AAAC,OAAO,CAAC,KAAK,KAAK,IAAI,IAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3E;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC,MAAM,MAAM,KAAK,IAAI;QACxC,IAAI,SAAS,GAAG,OAAO,CAAC,CAAC,KAAK,MAAM,IAAI;QAExC,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,MAAM;QAChC,OAAO,CAAC,KAAK,OAAO,AAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAK;IACtE;IAEA,4BAA4B;IAC5B,MAAM,eAAe,CAAC,MAAM,MAAM,KAAK,IAAI;QACzC,IAAI,CAAC,IAAI,KAAK;QACd,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM;QAC/B,IAAI;QAEJ,IAAI,MAAM;YACR,KAAK,AAAC,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,GAAG,IAAI,KAAK;QACpE,OAAO;YACL,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI;QAC5B;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,MAAM,iBAAiB,mBACrB,aAAa,eACb,WACA,KACA;QAEF,MAAM,gBAAgB,mBAAmB,UAAU,MAAM,KAAK;QAC9D,MAAM,WAAW,mBACf,cACA,OACA,KACA;QAGF,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,UAAU;YAClD,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,SACE;gBACJ,CAAC;YACD;QACF;QAEA,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAG,CAAC;QAE7C,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,gBAAgB;QACpB,MAAM,iBAAiB,OAAO,UAAU,CAAC,aAAa;QACtD,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO,UAAU,CAAC,gBAAgB,KAAK,IAAI,MAAM;QAC1E,MAAM,cAAc,OAAO,QAAQ,CAAC,YAAY;QAEhD,IAAI,mBAAmB,0BAA0B;YAC/C,MAAM,cAAc,eAAe,MAAM;YACzC,MAAM,UAAU,WAAW;YAE3B,IAAI,mBAAmB,OAAO;gBAC5B,sBAAsB;gBACtB,MAAM,KAAK,YAAY,aAAa,SAAS,CAAC,gBAAgB,GAAG;gBACjE,eAAe,KAAK,KAAK,CAAC;gBAC1B,kBAAkB,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,WAAW,KAAK,KAAK,CAAC,KAAK,iBAAiB;YAC9C,OAAO,IAAI,mBAAmB,WAAW;gBACvC,0BAA0B;gBAC1B,MAAM,KAAK,iBAAiB,KAAK,GAAG,CAAC,IAAI,eAAe,KAAK;gBAC7D,eAAe,KAAK,KAAK,CAAC;gBAC1B,kBAAkB,KAAK,KAAK,CAAC;gBAC7B,WAAW,KAAK,KAAK,CAAC,KAAK;YAC7B;QACF,OAAO,IAAI,mBAAmB,sBAAsB;YAClD,MAAM,cAAc,eAAe,MAAM;YACzC,MAAM,UAAU,WAAW;YAE3B,IAAI,mBAAmB,OAAO;gBAC5B,sBAAsB;gBACtB,MAAM,MAAM,IAAI,aAAa,SAAS,GAAG,CAAC,gBAAgB;gBAC1D,qBAAqB,KAAK,KAAK,CAAC;gBAChC,kBAAkB,KAAK,KAAK,CAAC,MAAM;gBACnC,WAAW,KAAK,KAAK,CAAC,iBAAiB,MAAM;YAC/C,OAAO,IAAI,mBAAmB,WAAW;gBACvC,0BAA0B;gBAC1B,MAAM,KAAK,iBAAiB,KAAK,GAAG,CAAC,IAAI,eAAe,KAAK;gBAC7D,qBAAqB,KAAK,KAAK,CAAC;gBAChC,kBAAkB,KAAK,KAAK,CAAC;gBAC7B,WAAW,KAAK,KAAK,CAAC,iBAAiB;YACzC;QACF;IAEA,8DAA8D;IAC9D,kCAAkC;IACpC;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC,GAAG;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAE1B,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC5B,6CAA6C;YAC7C,OAAO;QACT,OAAO,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,SAAS;YACpC,wDAAwD;YACxD,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,uBAAuB;gBACzC,8BAA8B;gBAC9B,MAAM,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,CAAC,OAAO;gBACvD,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,OAAO,UAAU,CAAC,aAAa;QAChD,IAAI,MAAM,WAAW,OAAO;QAC5B,OAAO,cAAc;IACvB;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAc;QAAU;QAAc;QAAgB;KAAe;IAEzE,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;QACrC,OAAO,SAAS,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;IACrD;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI,UAAU;QACd,IAAI,WAAW;QAEf,IAAI,mBAAmB,sBAAsB;YAC3C,IAAI,mBAAmB,OAAO;gBAC5B,UAAU;gBACV,WAAW;YACb,OAAO,IAAI,mBAAmB,aAAa;gBACzC,UAAU;gBACV,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;QACF,OAAO;YACL,IAAI,mBAAmB,OAAO;gBAC5B,UAAU;gBACV,WAAW;YACb,OAAO,IAAI,mBAAmB,aAAa;gBACzC,UAAU;gBACV,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;QACF;QAEA,OAAO;YAAE;YAAS;QAAS;IAC7B;IAEA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IAE9B,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAInE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oCAAoC,EAC9C,mBAAmB,QACf,4BACA,eACJ;gDACF,SAAS,IAAM,kBAAkB;0DAClC;;;;;;0DAGD,6LAAC;gDACC,WAAW,CAAC,oCAAoC,EAC9C,mBAAmB,YACf,4BACA,eACJ;gDACF,SAAS,IAAM,kBAAkB;0DAClC;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA8B;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwC;;;;;;0EAGxD,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,mBAAmB;gEAC1B,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;4CAI3C,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DACV,OAAO,YAAY;;;;;;0DAIxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,aAAa;wDACpB,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2B;;;;;;kEAG5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO;gEACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;0EAExC,6LAAC;gEAAK,WAAU;0EAAuC;;;;;;;;;;;;;;;;;;4CAK1D,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DACV,OAAO,YAAY;;;;;;0DAIxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2B;;;;;;kEAG5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO;gEACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;;;;;;0EAExC,6LAAC;gEAAK,WAAU;0EAAyC;;;;;;;;;;;;;;;;;;4CAK5D,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ;;;;;;0DAG3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;wDACtC,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gLAAA,CAAA,mBAAgB;oDAAC,UAAU;;;;;;;;;;;0DAG9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,mBAAmB,QAChB,kBACA;;;;;;kEAEN,6LAAC;wDAAE,WAAU;;4DAAqB;4DAC7B,cAAc;;;;;;;;;;;;;;;;;;;kDAKvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;;oEAAgB;oEACxB,cAAc;;;;;;;;;;;;;;;;;;;0DAKvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;;oEAAgB;oEAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;;wDAAoC;wDAC5C,cAAc;;;;;;;;;;;;;;;;;;oCAKtB,OAAO,OAAO,kBACb,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO;;;;;;kDAInB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;gDACE;gDAAS;8DACV,6LAAC;oDAAK,WAAU;;wDAAgB;wDAC3B,cAAc;;;;;;;gDACX;gDACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAQlC,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAMlC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C;GA9gBwB;KAAA", "debugId": null}}, {"offset": {"line": 1188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nconst Banner = ({ imageUrl, title, subtitle }) => {\r\n  return (\r\n    <section className={`relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`}>\r\n      {/* Parallax Fixed Background Image - Only for Banner */}\r\n      <div\r\n        className=\"fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0\"\r\n      >\r\n        <img src={imageUrl} alt={title} className=\"w-full h-full object-cover\"/>\r\n      </div>\r\n      {/* <div\r\n        className=\"fixed top-0 left-0 w-full h-full !bg-cover !bg-center -z-10 pointer-events-none\"\r\n        style={backgroundStyles}\r\n      /> */}\r\n\r\n      {/* Dark Overlay */}\r\n      <div className=\"absolute inset-0 bg-[#000]/40 z-0\" />\r\n\r\n      {/* Banner Content */}\r\n      <div className=\"relative z-10 text-center px-4\">\r\n        <h1 className=\"text-3xl md:text-5xl font-medium\">{title}</h1>\r\n        {subtitle && (\r\n          <p className=\"text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Banner;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,qBACE,6LAAC;QAAQ,WAAW,CAAC,0GAA0G,CAAC;;0BAE9H,6LAAC;gBACC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,KAAK;oBAAU,KAAK;oBAAO,WAAU;;;;;;;;;;;0BAQ5C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;KA5BM;uCA8BS", "debugId": null}}]}