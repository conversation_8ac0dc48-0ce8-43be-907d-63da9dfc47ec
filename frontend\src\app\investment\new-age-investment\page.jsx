import NewAgeInvestmentMaster from '@/components/investment/New-Age-Investment/NewAgeInvestmentMaster'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
     <Head>
        <title>New Age Investment Options | Ethical & Innovative Wealth Solutions | Winshine</title>
        <meta name="description" content="Explore Winshine’s new age investment options that combine ethics with innovation. Discover mutual funds, ETFs, digital assets, and sustainable investments tailored for modern investors." />
        <meta name="keywords" content="new age investments, ethical investing, mutual funds, ETFs, digital assets, sustainable investments, Winshine investment solutions" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="New Age Investment Options | Winshine Financial Services" />
        <meta property="og:description" content="Invest in the future with Winshine’s innovative and ethical new age investment options. Build wealth responsibly with expert guidance." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/investment/new-age-investment-banner.jpg" /> 
        <meta property="og:url" content="https://winshine.nipralo.com/investment/new-age-investment" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/investment/new-age-investment-banner.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/investment/new-age-investment" />
      </Head>
      <NewAgeInvestmentMaster/>
    </>
  )
}

export default page
