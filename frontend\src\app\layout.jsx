import { DM_Sans } from "next/font/google";

import "./globals.css";
import Head from "next/head";
import Navbar from "@/components/layout/Navbar/Navbar";
import Footer from "@/components/layout/Footer/Footer";
import SmoothScroll from "@/components/ui/lenis/smoothScroll";
import MobileNavbar from "@/components/layout/Navbar/MobileNavbar";

// Load fonts
const dmSans = DM_Sans({
  variable: "--font-dm-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"], // adjust as needed
  display: "swap",
});

// Default SEO metadata
export const metadata = {
  title: "Winshine Financial Services - One Stop Investment Solutions",
  description: "",
  openGraph: {
    title: "Winshine Financial Services - One Stop Investment Solutions",
    description: "",
    type: "website",
    url: "",
    image: "/favicon.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Winshine Financial Services - One Stop Investment Solutions",
    description: "",
    image: "/favicon.png",
  },
};

export default function Layout({ children }) {

  return (
    <html lang="en">
      <Head>
        {/* SEO Metadata */}
        <title>
          Winshine Financial Services - One Stop Investment Solutions
        </title>
        <meta
          name="description"
          content="Winshine Financial Services empowers clients to build ethical wealth with professional guidance. Explore our savings, investment, insurance, and tax planning services today."
        />
        <meta
          name="keywords"
          content="Winshine, financial services, ethical investing, wealth creation, tax planning, insurance, investment planning, savings, professional financial guidance"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta name="description" content="" />
        <meta name="robots" content="index, follow" />
        <meta property="og:title" content="Winshine Financial Services - One Stop Investment Solutions" />
        <meta property="og:description" content="Empowering clients with ethical and professional financial services - savings, investments, insurance, and more." />
        <meta property="og:image" content="https://www.winshine.in/images/home/<USER>" /> 
        <meta property="og:url" content="https://www.winshine.in" />
        <meta name="twitter:card" content="https://www.winshine.in/images/home/<USER>" />
        <link rel="canonical" href="https://www.winshine.in" />
        {/* Standard Favicon */}
        <link rel="icon" type="image/x-icon" href="/favicon.png" />
        {/* Favicon for different sizes */}
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
        {/* Android Chrome Favicon */}
        <link rel="icon" type="image/png" sizes="192x192" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/favicon.png" />
        {/* Apple Touch Icon */}
        <link rel="apple-touch-icon" href="/favicon.png" />
        {/* Optional: Open Graph and Twitter card metadata */}
        <meta property="og:image" content="/favicon.png" />
        <meta name="twitter:image" content="/favicon.png" />
      </Head>
      <body className={`${dmSans.variable} antialiased`}>
        <SmoothScroll />
        <Navbar />
        <MobileNavbar />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
