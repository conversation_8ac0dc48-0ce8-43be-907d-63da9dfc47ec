{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.13.1", "@strapi/plugin-users-permissions": "5.13.1", "@strapi/provider-upload-cloudinary": "^5.13.1", "@strapi/strapi": "5.13.1", "cloudinary": "^2.6.1", "mysql2": "3.9.8", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "f7ac1cc35b9019a68910ac6e92d4df1a5ada963f20ca3e8b6911e48ddf5ef9b8"}}