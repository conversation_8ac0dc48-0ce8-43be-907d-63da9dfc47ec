import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Slider from "react-slick";
import "./Testimonial.scss";
import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import { Star } from "lucide-react";
import Link from "next/link";

// Slick Slider settings
const settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  prevArrow: (
    <div className="arrows-btn">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="#fa6e1c99"
        viewBox="0 0 448 512"
      >
        <path d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z" />
      </svg>
    </div>
  ),
  nextArrow: (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="#fa6e1c99"
        viewBox="0 0 448 512"
      >
        <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z" />
      </svg>
    </div>
  ),
};

gsap.registerPlugin(ScrollTrigger);

const Testimonial = () => {
  const sectionRef = useRef(null);
  const leftRef = useRef(null);
  const rightRef = useRef(null);

  useEffect(() => {
    // const section = sectionRef.current;
    // const leftSide = leftRef.current;
    // const rightSide = rightRef.current;

    // Clean up previous animations when component unmounts to avoid conflicts
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="lg:h-max h-[auto]">
      <div className="grid grid-cols-1 gap-0 lg:grid-cols-12 lg:gap-x-10 xl:gap-x-20">
        {/* Left side content */}
        <div ref={leftRef} className="left-content lg:col-span-4">
          <div className="sticky-content">
            <div className="lg:h-[30vh] h-[20vh] flex justify-between items-center lg:w-[90%] w-[100%] mx-auto ml-0">
              <div className="flex flex-col items-start justify-between w-full ">
                <h2 className="mb-4 text-2xl lg:text-4xl font-medium lg:mb-4 text-[#040404]">
                  <SkewFadeInWords text="What Our Client Says" />
                </h2>
                <p className="mt-2 text-black">
                  Rated 4.9<span className="text-[#d3b703]">★</span> from 158
                  reviews - Hear what our clients and industry leaders say about
                  the trust, results and service that define Winshine
                </p>
                <Link
                  href="https://www.google.com/search?sca_esv=46de8ce3f677badb&sxsrf=AE3TifPWU9yXeHp472SqjCvIWoBBOltEOg:1748499212215&si=AMgyJEtREmoPL4P1I5IDCfuA8gybfVI2d5Uj7QMwYCZHKDZ-E1_PbEbidRPExaiMU_y3JMVoJwcfKM4PV3n_QlEHcs56YXsj4g520oW4eqdtmI7p6KwjTSoc2awFa4mPJSjIuMiS6JyRWoX0bGRJP3vP0aQVYFQQPg%3D%3D&q=Winshine+Financial+Services+Reviews&sa=X&ved=2ahUKEwjApLyTg8iNAxWvsFYBHf56F40Q0bkNegQIJRAE&biw=1366&bih=641&dpr=1#lrd=0x3be7c62e9d7d6357:0xd943e730b640cdd3,3,,,,"
                  target="_blank"
                  className="text-blue-600 hover:text-blue-800 hover:underline mt-2"
                >
                  Write a Review
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Right side content */}

        <div className="hidden lg:block lg:col-span-8">
          <div
            ref={rightRef}
            className="grid right-content lg:grid-cols-2 gap-x-10 "
          >
            <div className="desktop-rv-1 team-member h-max px-6 py-8 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#bf360c] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  V
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Vivek Sridharan
                  </div>
                  <div className="text-gray-400 text-xs">12 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">8 months ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  My Father has been a customer of Winshine close to a decade.
                  When he passed away Winshine stood by me and helped me with
                  the transfers. Winshine helped with the extremely arduous
                  process of transferring all holdings, They have good
                  accounting system to help track investments across portfolio
                  and for tax reporting.
                </p>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                  The staff is extremely responsible, prompt and trustworthy.
                </p>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                  Winshine leaders managed my fathers portfolio profitably.
                </p>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                  In Winshine I have the provider that I need to manage my
                  portfolio. Thank you Winshine!!
                </p>
              </div>
            </div>
            <div className="desktop-rv-2 team-member h-max px-6 py-8 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#024939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  M
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Meghana Shah
                  </div>
                  <div className="text-gray-400 text-sm">4 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">8 months ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  I have had very good experience with this firm. They
                  prioritize clients need & tailor their services to allign with
                  individual goals. All the employees consistently go above &
                  beyond to assist clients.
                </p>
              </div>
            </div>
            <div className="desktop-rv-3 team-member mb-[60px]  h-max px-6 py-8 rounded-[16px] overflow-y-hidden">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#0058a0] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  H
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Haarvi Shah
                  </div>
                  <div className="text-gray-400 text-sm">1 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">8 months ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  The team at Winshine demonstrates exceptional market insight
                  and expertise, making informed decisions that drive strong
                  returns. I've always found the team to be incredibly
                  responsive, addressing inquiries and concerns with impressive
                  speed and thoroughness.
                </p>
              </div>
            </div>
            <div className="desktop-rv-4 team-member h-max px-6 py-6 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#024939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  M
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Mahadevan Krishnan
                  </div>
                  <div className="text-gray-400 text-sm">4 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(4)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <Star className="w-3 h-3 fill-gray-300 text-gray-300" />
                <span className="ml-2 text-gray-400 text-xs">a year ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  My initial interaction with Winshine Financial Services has
                  cemented my faith in their services over time. They give sound
                  and varied suggestions leaving it to me to decide and take the
                  best decision. My association with WFS over the years has
                  given me confidence that I am with a group of people who are
                  professionals and can be depended on with one's finances. I
                  wish Mr Subahu and his staff, with special reference to Mrs
                  Anuja, all the very best and wish all of them good health,
                  peace and prosperity in 2024.
                </p>
              </div>
            </div>
            <div className="desktop-rv-5 team-member h-max px-6 py-6 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#f5511e] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  K
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Kamlesh Bagaria
                  </div>
                  <div className="text-gray-400 text-sm">3 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">a year ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  I am client of Winshine from last 17 years and before Winshine
                  started I was attached to S.V.lnvestments. During these period
                  I have earned profits each year on my investments. Their
                  research and advices are phenomenal. They do research very
                  deeply, honestly and suggest to invest only if they are highly
                  satisfied. Risk on investment as per their suggestions are
                  always very low and probability of gain profit is very high.
                  One of their promoter is a Chartered Account (Rank holder).
                  Their back office team is highly prompt and energetic. I am
                  fortunate that I am their client.
                </p>
              </div>
            </div>
            <div className="desktop-rv-6 team-member h-max px-6 py-6 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#034939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  S
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Sudha Joshi
                  </div>
                  <div className="text-gray-400 text-sm">1 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">a year ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  Service is good
                </p>
              </div>
            </div>
            <div></div>
            <div className="desktop-rv-7 team-member h-max px-6 py-6 rounded-[16px] overflow-y-hidden mb-[60px]">
              <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                {/* Circle with initial */}
                <div className="bg-[#689f39] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                  N
                </div>

                {/* Name and reviews */}
                <div className="">
                  <div className="font-medium leading-tight text-gray-700">
                    Nidhi Solanki
                  </div>
                  <div className="text-gray-400 text-sm">5 reviews</div>
                </div>
              </div>
              <div className="flex items-center gap-0.5 mb-1">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-2 text-gray-400 text-xs">9 months ago</span>
              </div>
              <div>
                <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                  Shubhu Sir explained things very well. Promt and timely
                  service by his team.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile slider testimonial */}
        <div className="lg:hidden">
          <Slider {...settings}>
            <div className="container-1 slide-item">
              <div className="team-member h-max px-4 md:px-6 py-4 md:py-8 rounded-[16px] shadow-none border overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#bf360c] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    V
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Vivek Sridharan
                    </div>
                    <div className="text-gray-400 text-xs">12 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">
                    8 months ago
                  </span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    My Father has been a customer of Winshine close to a decade.
                    When he passed away Winshine stood by me and helped me with
                    the transfers. Winshine helped with the extremely arduous
                    process of transferring all holdings, They have good
                    accounting system to help track investments across portfolio
                    and for tax reporting.
                  </p>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                    The staff is extremely responsible, prompt and trustworthy.
                  </p>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                    Winshine leaders managed my fathers portfolio profitably.
                  </p>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm mt-3">
                    In Winshine I have the provider that I need to manage my
                    portfolio. Thank you Winshine!!
                  </p>
                </div>
              </div>
            </div>
            <div className="container-2 slide-item">
              <div className="team-member lg:mt-[-200px] h-max px-4 md:px-6 py-4 md:py-8 shadow-none border rounded-[16px] overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#024939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    M
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Meghana Shah
                    </div>
                    <div className="text-gray-400 text-sm">4 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">
                    8 months ago
                  </span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    I have had very good experience with this firm. They
                    prioritize clients need & tailor their services to allign
                    with individual goals. All the employees consistently go
                    above & beyond to assist clients.
                  </p>
                </div>
              </div>
            </div>
            <div className="container-3 slide-item">
              <div className="team-member lg:mt-[300px] shadow-none h-max px-4 md:px-6 py-4 md:py-8 border rounded-[16px] overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#0058a0] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    H
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Haarvi Shah
                    </div>
                    <div className="text-gray-400 text-sm">1 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">
                    8 months ago
                  </span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    The team at Winshine demonstrates exceptional market insight
                    and expertise, making informed decisions that drive strong
                    returns. I've always found the team to be incredibly
                    responsive, addressing inquiries and concerns with
                    impressive speed and thoroughness.
                  </p>
                </div>
              </div>
            </div>
            <div className="container-4 slide-item">
              <div className="team-member h-max px-4 md:px-6 py-4 md:py-6 rounded-[16px] border shadow-none overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#024939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    M
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Mahadevan Krishnan
                    </div>
                    <div className="text-gray-400 text-sm">4 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(4)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <Star className="w-3 h-3 fill-gray-300 text-gray-300" />
                  <span className="ml-2 text-gray-400 text-xs">a year ago</span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    My initial interaction with Winshine Financial Services has
                    cemented my faith in their services over time. They give
                    sound and varied suggestions leaving it to me to decide and
                    take the best decision. My association with WFS over the
                    years has given me confidence that I am with a group of
                    people who are professionals and can be depended on with
                    one's finances. I wish Mr Subahu and his staff, with special
                    reference to Mrs Anuja, all the very best and wish all of
                    them good health, peace and prosperity in 2024.
                  </p>
                </div>
              </div>
            </div>
            <div className="container-5 slide-item">
              <div className="team-member h-max px-4 md:px-6 py-4 md:py-6 rounded-[16px] border shadow-none overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#f5511e] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    K
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Kamlesh Bagaria
                    </div>
                    <div className="text-gray-400 text-sm">3 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">a year ago</span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    I am client of Winshine from last 17 years and before
                    Winshine started I was attached to S.V.lnvestments. During
                    these period I have earned profits each year on my
                    investments. Their research and advices are phenomenal. They
                    do research very deeply, honestly and suggest to invest only
                    if they are highly satisfied. Risk on investment as per
                    their suggestions are always very low and probability of
                    gain profit is very high. One of their promoter is a
                    Chartered Account (Rank holder). Their back office team is
                    highly prompt and energetic. I am fortunate that I am their
                    client.
                  </p>
                </div>
              </div>
            </div>
            <div className="container-6 slide-item">
              <div className="team-member h-max px-4 md:px-6 py-4 min-w-full md:py-6 rounded-[16px] border shadow-none overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#034939] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    S
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Sudha Joshi
                    </div>
                    <div className="text-gray-400 text-sm">1 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">a year ago</span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    Service is good
                  </p>
                </div>
              </div>
            </div>
            <div className="container-7 slide-item">
              <div className="team-member h-max px-4 md:px-6 py-4 md:py-6 rounded-[16px] border shadow-none overflow-y-hidden">
                <div className="flex items-center space-x-4 text-white rounded-md w-fit mb-3">
                  {/* Circle with initial */}
                  <div className="bg-[#689f39] w-10 h-10 rounded-full flex items-center justify-center text-lg font-semibold">
                    N
                  </div>

                  {/* Name and reviews */}
                  <div className="">
                    <div className="font-medium leading-tight text-gray-700">
                      Nidhi Solanki
                    </div>
                    <div className="text-gray-400 text-sm">5 reviews</div>
                  </div>
                </div>
                <div className="flex items-center gap-0.5 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="ml-2 text-gray-400 text-xs">
                    9 months ago
                  </span>
                </div>
                <div>
                  <p className="max-h-[90%] overflow-y-hidden text-justify text-sm">
                    Shubhu Sir explained things very well. Promt and timely
                    service by his team.
                  </p>
                </div>
              </div>
            </div>
          </Slider>
        </div>
      </div>
    </section>
  );
};

export default Testimonial;
