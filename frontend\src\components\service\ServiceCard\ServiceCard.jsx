import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import Image from "next/image";
import React from "react";

const ServiceCard = ({description, title, image, imageRightSide =  false}) => {
  return (
    <div className={` text-black ${imageRightSide ? "bg-[#f9f3f1]" : "bg-white"}`}>
      <div className="s_wrapper ">
        <div className={`flex flex-col gap-4 justify-between ${imageRightSide ? "md:flex-row-reverse" : "md:flex-row"}`}>
          <div className="w-full md:w-[50%] lg:w-[35%]">
            <Image
              src={image}
              alt={title}
              width={400}
              height={400}
              className={`max-w-[400px] rounded-md w-full h-auto ${imageRightSide ? "md:ml-auto" : ""}`}
            />
          </div>
          <div className="w-full md:w-[50%] lg:w-[65%] flex flex-col justify-center">
            <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4">
              <SkewFadeInWords text={title} />
            </h2>

            <p className="lg:mb-4 mb-2 text-[#333]  text-justify md:text-start">
             {description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceCard;
