import BusinessAssociateMaster from '@/components/Business-Oppotunities/BusinessAssociate/BusinessAssociateMaster'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
    <Head>
        <title>Partner with Winshine | Business Associate Opportunity in Financial Services</title>
        <meta name="description" content="Join <PERSON><PERSON><PERSON> as a Business Associate and build your career in ethical financial services. Collaborate with a trusted brand offering savings, investments, insurance, and more." />
        <meta name="keywords" content="business associate, financial partnership, join Winshine, financial career, investment associate, financial advisor opportunity, ethical financial services" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Business Associate Opportunity | Winshine Financial Services" />
        <meta property="og:description" content="Grow with <PERSON><PERSON><PERSON> as a Business Associate. Be part of a mission-driven team focused on ethical and professional financial services." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/business-oppotunity/business-associate2.jpg" />
        <meta property="og:url" content="https://winshine.nipralo.com/business-opportunity/business-associate" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/business-oppotunity/business-associate2.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/business-opportunity/business-associate" />
      </Head>
    <BusinessAssociateMaster/>
    </>
  )
}

export default page
