(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/calculator/sip-calculator/circular-progress.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// "use client"
// import { useEffect, useRef } from "react"
// export function CircularProgress({ progress }) {
//   const fillRef = useRef(null)
//   useEffect(() => {
//     if (fillRef.current) {
//       const max = -219.99078369140625
//       const cappedProgress = progress > 100 ? 100 : progress
//       const dashOffset = ((100 - cappedProgress) / 100) * max
//       fillRef.current.style.strokeDashoffset = dashOffset.toString()
//     }
//   }, [progress])
//   return (
//     <div className="relative w-[245px] h-[215px]">
//       <svg className="progress" x="0px" y="0px" viewBox="0 0 80 80">
//         <path
//           className="track"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#ac272b"
//           strokeWidth="40"
//           style={{ transform: "rotate(90deg) translate(0px, -80px)" }}
//         />
//         <path
//           ref={fillRef}
//           className="fill"
//           d="M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0"
//           fill="none"
//           stroke="#1B1E49"
//           strokeWidth="40"
//           style={{
//             transform: "rotate(90deg) translate(0px, -80px)",
//             strokeDasharray: "219.9907836914",
//             strokeDashoffset: "-219.9907836914",
//             transition: "stroke-dashoffset 1s",
//           }}
//         />
//       </svg>
//       <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full" />
//     </div>
//   )
// }
// export default CircularProgress
__turbopack_context__.s({
    "CircularProgress": (()=>CircularProgress),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
function CircularProgress({ progress }) {
    _s();
    const fillRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircularProgress.useEffect": ()=>{
            if (fillRef.current) {
                const max = 219.99078369140625;
                const cappedProgress = Math.min(progress, 100);
                const dashOffset = (100 - cappedProgress) / 100 * max;
                fillRef.current.style.strokeDashoffset = dashOffset.toString();
            }
        }
    }["CircularProgress.useEffect"], [
        progress
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative w-[245px] h-[245px] rounded-full overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "progress",
                x: "0px",
                y: "0px",
                viewBox: "0 0 80 80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0",
                        fill: "none",
                        stroke: "#ac272b",
                        strokeWidth: "42",
                        style: {
                            transform: "rotate(90deg) translate(0px, -80px)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                        lineNumber: 72,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        ref: fillRef,
                        d: "M5,40a35,35 0 1,0 70,0a35,35 0 1,0 -70,0",
                        fill: "none",
                        stroke: "#1B1E49",
                        strokeWidth: "40",
                        strokeLinecap: "butt",
                        style: {
                            transform: "rotate(90deg) translate(0px, -80px)",
                            strokeDasharray: "219.9907836914",
                            strokeDashoffset: "219.9907836914",
                            transition: "stroke-dashoffset 1s ease"
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100px] h-[100px] bg-white rounded-full"
            }, void 0, false, {
                fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/calculator/sip-calculator/circular-progress.jsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_s(CircularProgress, "/bCvsHLZppcYn+l67o1widLIpC4=");
_c = CircularProgress;
const __TURBOPACK__default__export__ = CircularProgress;
var _c;
__turbopack_context__.k.register(_c, "CircularProgress");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/calculator/sip-calculator/SipCalculator.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SipCalculator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$calculator$2f$sip$2d$calculator$2f$circular$2d$progress$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/calculator/sip-calculator/circular-progress.jsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function SipCalculator() {
    _s();
    // State for form inputs
    const [investmentType, setInvestmentType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("know-investment-amount");
    const [investmentMode, setInvestmentMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("sip");
    const [targetAmount, setTargetAmount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("1500000");
    const [duration, setDuration] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("10");
    const [rateOfReturn, setRateOfReturn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("12");
    // State for calculation results
    const [investedAmount, setInvestedAmount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [returns, setReturns] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [totalWealth, setTotalWealth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [monthlyInvestment, setMonthlyInvestment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [graphProgress, setGraphProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // State for errors
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        targetAmount: "",
        duration: "",
        rateOfReturn: "",
        general: ""
    });
    // ROI array for slider
    const roiArr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    // Initialize ROI array
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SipCalculator.useEffect": ()=>{
            const tempRoiArr = [];
            for(let i = 1; Number.parseFloat(i.toFixed(2)) < 100.01; i += 0.01){
                tempRoiArr.push(Number.parseFloat(i).toFixed(2));
            }
            roiArr.current = tempRoiArr;
        }
    }["SipCalculator.useEffect"], []);
    // Format number with commas
    const numWithCommas = (num)=>{
        return num.toLocaleString("en-IN");
    };
    // Remove commas from number string
    const removeCommas = (number)=>{
        return number.toString().replace(/,/g, "");
    };
    // Validate input range
    const validateRangeInput = (value, max, min, field)=>{
        const numValue = Number(value);
        if (isNaN(numValue) || numValue === "") {
            setErrors((prev)=>({
                    ...prev,
                    [field]: "Please enter a valid number"
                }));
            return false;
        }
        if (numValue < min) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: `Minimum value is ${min}`
                }));
            return false;
        }
        if (numValue > max) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: `Maximum value is ${max}`
                }));
            return false;
        }
        setErrors((prev)=>({
                ...prev,
                [field]: ""
            }));
        return true;
    };
    // PMT calculation function - calculates payment amount for annuity
    // Standard financial formula: PMT = (FV * r) / (((1 + r)^n - 1) * (1 + r * type))
    const PMT = (rate, nper, pv, fv, type = 0)=>{
        if (!fv) fv = 0;
        if (!pv) pv = 0;
        if (rate === 0) {
            return -(fv + pv) / nper;
        }
        const term = Math.pow(1 + rate, nper);
        const numerator = rate * (fv + pv * term);
        const denominator = (term - 1) * (1 + rate * type);
        return -numerator / denominator;
    };
    // Future value calculation for annuity (SIP)
    // Standard formula: FV = PMT * (((1 + r)^n - 1) / r) * (1 + r * type) + PV * (1 + r)^n
    const futureValue = (rate, nper, pmt, pv = 0, type = 0)=>{
        if (rate === 0) {
            return -(pv + pmt * nper);
        }
        const term = Math.pow(1 + rate, nper);
        const annuityFV = pmt * ((term - 1) / rate) * (1 + rate * type);
        const presentValueFV = pv * term;
        return -(annuityFV + presentValueFV);
    };
    // Present value calculation
    // Standard formula: PV = (FV / (1 + r)^n) - PMT * (((1 + r)^n - 1) / (r * (1 + r)^n)) * (1 + r * type)
    const presentValue = (rate, nper, pmt, fv, type = 0)=>{
        if (!fv) fv = 0;
        if (!pmt) pmt = 0;
        if (rate === 0) {
            return -(fv + pmt * nper);
        }
        const term = Math.pow(1 + rate, nper);
        const futureValuePV = fv / term;
        const annuityPV = pmt * ((term - 1) / (rate * term)) * (1 + rate * type);
        return -(futureValuePV + annuityPV);
    };
    // Calculate results based on inputs
    const calculateResults = ()=>{
        // Validate inputs
        const targetAmtValid = validateRangeInput(removeCommas(targetAmount), "10000000000", "1", "targetAmount");
        const durationValid = validateRangeInput(duration, "50", "1", "duration");
        const roiValid = validateRangeInput(rateOfReturn, "100", "0", "rateOfReturn");
        if (!targetAmtValid || !durationValid || !roiValid) {
            setErrors((prev)=>({
                    ...prev,
                    general: "Please enter numeric inputs within the suggested range to get accurate results"
                }));
            return;
        }
        setErrors((prev)=>({
                ...prev,
                general: ""
            }));
        const targetAmtValue = Number.parseFloat(removeCommas(targetAmount));
        const annualRate = Number.parseFloat(rateOfReturn) / 100;
        const years = Number.parseInt(duration);
        if (investmentType === "know-investment-amount") {
            // Calculate future value from known investment amount
            if (investmentMode === "sip") {
                // Monthly SIP calculation
                const monthlyRate = annualRate / 12;
                const months = years * 12;
                let fv;
                if (monthlyRate === 0) {
                    // No interest case
                    fv = targetAmtValue * months;
                } else {
                    // Future value of SIP using standard formula
                    // FV = PMT * (((1 + r)^n - 1) / r) * (1 + r) [for beginning of period]
                    fv = targetAmtValue * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate * (1 + monthlyRate));
                }
                const totalInvested = targetAmtValue * months;
                const returns = fv - totalInvested;
                setTotalWealth(Math.round(fv));
                setInvestedAmount(Math.round(totalInvested));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(targetAmtValue));
                setGraphProgress(fv > 0 ? returns / fv * 100 : 0);
            } else if (investmentMode === "quarterly") {
                // Quarterly investment calculation
                const quarterlyRate = annualRate / 4;
                const quarters = years * 4;
                let fv;
                if (quarterlyRate === 0) {
                    // No interest case
                    fv = targetAmtValue * quarters;
                } else {
                    fv = targetAmtValue * ((Math.pow(1 + quarterlyRate, quarters) - 1) / quarterlyRate * (1 + quarterlyRate));
                }
                const totalInvested = targetAmtValue * quarters;
                const returns = fv - totalInvested;
                setTotalWealth(Math.round(fv));
                setInvestedAmount(Math.round(totalInvested));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(targetAmtValue));
                setGraphProgress(fv > 0 ? returns / fv * 100 : 0);
            } else {
                // Lumpsum calculation
                let fv;
                if (annualRate === 0) {
                    // No interest case
                    fv = targetAmtValue;
                } else {
                    fv = targetAmtValue * Math.pow(1 + annualRate, years);
                }
                const returns = fv - targetAmtValue;
                setTotalWealth(Math.round(fv));
                setInvestedAmount(Math.round(targetAmtValue));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(targetAmtValue));
                setGraphProgress(fv > 0 ? returns / fv * 100 : 0);
            }
        } else if (investmentType === "know-target-amount") {
            // Calculate required investment to reach target amount
            if (investmentMode === "sip") {
                // Required monthly SIP calculation
                const monthlyRate = annualRate / 12;
                const months = years * 12;
                let requiredSIP;
                if (monthlyRate === 0) {
                    // No interest case
                    requiredSIP = targetAmtValue / months;
                } else {
                    // PMT = FV / (((1 + r)^n - 1) / r) * (1 + r) [for beginning of period]
                    requiredSIP = targetAmtValue / ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate * (1 + monthlyRate));
                }
                const totalInvested = requiredSIP * months;
                const returns = targetAmtValue - totalInvested;
                setTotalWealth(Math.round(targetAmtValue));
                setInvestedAmount(Math.round(totalInvested));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(requiredSIP));
                setGraphProgress(targetAmtValue > 0 ? returns / targetAmtValue * 100 : 0);
            } else if (investmentMode === "quarterly") {
                // Required quarterly investment calculation
                const quarterlyRate = annualRate / 4;
                const quarters = years * 4;
                let requiredQuarterly;
                if (quarterlyRate === 0) {
                    // No interest case
                    requiredQuarterly = targetAmtValue / quarters;
                } else {
                    requiredQuarterly = targetAmtValue / ((Math.pow(1 + quarterlyRate, quarters) - 1) / quarterlyRate * (1 + quarterlyRate));
                }
                const totalInvested = requiredQuarterly * quarters;
                const returns = targetAmtValue - totalInvested;
                setTotalWealth(Math.round(targetAmtValue));
                setInvestedAmount(Math.round(totalInvested));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(requiredQuarterly));
                setGraphProgress(targetAmtValue > 0 ? returns / targetAmtValue * 100 : 0);
            } else {
                // Required lumpsum calculation
                let requiredLumpsum;
                if (annualRate === 0) {
                    // No interest case - target amount equals required lumpsum
                    requiredLumpsum = targetAmtValue;
                } else {
                    requiredLumpsum = targetAmtValue / Math.pow(1 + annualRate, years);
                }
                const returns = targetAmtValue - requiredLumpsum;
                setTotalWealth(Math.round(targetAmtValue));
                setInvestedAmount(Math.round(requiredLumpsum));
                setReturns(Math.round(returns));
                setMonthlyInvestment(Math.round(requiredLumpsum));
                setGraphProgress(targetAmtValue > 0 ? returns / targetAmtValue * 100 : 0);
            }
        }
    };
    // Handle input changes
    const handleInputChange = (e, setter)=>{
        const { value } = e.target;
        if (e.target.type === "text") {
            // For text inputs, update the value directly
            setter(value);
        } else if (e.target.type === "range") {
            // For range inputs, update the corresponding text input
            if (e.target.id === "ill_int_rates_value") {
                // Handle ROI slider specially
                const roiValue = roiArr.current[Number.parseInt(value)];
                setter(roiValue);
            } else {
                setter(value);
            }
        }
    };
    // Format target amount with commas
    const formatTargetAmount = (value)=>{
        const numValue = Number.parseFloat(removeCommas(value));
        if (isNaN(numValue)) return "0";
        return numWithCommas(numValue);
    };
    // Calculate on input change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SipCalculator.useEffect": ()=>{
            calculateResults();
        }
    }["SipCalculator.useEffect"], [
        targetAmount,
        duration,
        rateOfReturn,
        investmentType,
        investmentMode
    ]);
    // Get ROI slider value
    const getRoiSliderValue = ()=>{
        const index = roiArr.current.indexOf(rateOfReturn);
        return index >= 0 ? index : roiArr.current.indexOf("12.00");
    };
    // Get text for result display
    const getResultText = ()=>{
        let preText = "";
        let postText = "";
        if (investmentType === "know-target-amount") {
            if (investmentMode === "sip") {
                preText = "Invest";
                postText = "every month to reach your target amount";
            } else if (investmentMode === "quarterly") {
                preText = "Invest";
                postText = "every quarter to reach your target amount";
            } else {
                preText = "Make a one-time investment of";
                postText = "to reach your target amount";
            }
        } else {
            if (investmentMode === "sip") {
                preText = "Your monthly investment of";
                postText = "will grow to the amount shown";
            } else if (investmentMode === "quarterly") {
                preText = "Your quarterly investment of";
                postText = "will grow to the amount shown";
            } else {
                preText = "Your one-time investment of";
                postText = "will grow to the amount shown";
            }
        }
        return {
            preText,
            postText
        };
    };
    const { preText, postText } = getResultText();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: "bg-[#fff] text-black py-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto px-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold text-[#2e4765] mb-6 text-center",
                    children: "SIP Calculator: Systematic Investment Plan Calculator Online"
                }, void 0, false, {
                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                    lineNumber: 377,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-md overflow-hidden",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col lg:flex-row",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full lg:w-1/2 p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex mb-6 gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `px-4 py-2 rounded-lg cursor-pointer ${investmentMode === "sip" ? "bg-[#1b1e49] text-white" : "bg-gray-100"}`,
                                                onClick: ()=>setInvestmentMode("sip"),
                                                children: "SIP"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 416,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `px-4 py-2 rounded-lg cursor-pointer ${investmentMode === "quarterly" ? "bg-[#1b1e49] text-white" : "bg-gray-100"}`,
                                                onClick: ()=>setInvestmentMode("quarterly"),
                                                children: "Quarterly"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 426,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `px-4 py-2 rounded-lg cursor-pointer ${investmentMode === "lumpsum" ? "bg-[#1b1e49] text-white" : "bg-gray-100"}`,
                                                onClick: ()=>setInvestmentMode("lumpsum"),
                                                children: "Lumpsum"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 436,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 415,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-center w-full",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-gray-700 text-xl",
                                                        children: investmentType === "know-target-amount" ? "Target Amount" : investmentMode === "sip" ? "Monthly Investment" : investmentMode === "quarterly" ? "Quarterly Investment" : "Investment Amount"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 451,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "absolute left-3 top-2.5 text-gray-500",
                                                                children: "₹"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 461,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "text",
                                                                className: "w-full max-w-28 p-2 pl-8 border border-gray-300 rounded-md",
                                                                value: formatTargetAmount(targetAmount),
                                                                onChange: (e)=>handleInputChange(e, setTargetAmount)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 464,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 460,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 450,
                                                columnNumber: 17
                                            }, this),
                                            errors.targetAmount && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-red-500 text-xs mt-1",
                                                children: errors.targetAmount
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 473,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "range",
                                                        min: "500",
                                                        max: "1000000",
                                                        step: "500",
                                                        value: removeCommas(targetAmount),
                                                        onChange: (e)=>handleInputChange(e, setTargetAmount),
                                                        className: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 479,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-xs text-gray-500 mt-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "₹ 500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 489,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "₹ 10,00,000"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 490,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 488,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 478,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 449,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-center w-full",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-gray-700 mb-2",
                                                        children: "Expected Return Rate (p.a)"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 498,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "text",
                                                                className: "w-full max-w-16 py-2 px-4 border border-gray-300 rounded-md ",
                                                                value: rateOfReturn,
                                                                onChange: (e)=>handleInputChange(e, setRateOfReturn)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 502,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "absolute right-3 top-2 text-gray-500",
                                                                children: "%"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 508,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 501,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 497,
                                                columnNumber: 17
                                            }, this),
                                            errors.rateOfReturn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-red-500 text-xs mt-1",
                                                children: errors.rateOfReturn
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 514,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "range",
                                                        min: "5",
                                                        max: "30",
                                                        step: "0.5",
                                                        value: rateOfReturn,
                                                        onChange: (e)=>handleInputChange(e, setRateOfReturn),
                                                        className: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 520,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-xs text-gray-500 mt-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "5%"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 530,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "30%"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 531,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 529,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 519,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 496,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-center w-full",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-gray-700 mb-2",
                                                        children: "Investment time period"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 539,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "text",
                                                                className: "w-full max-w-24 py-2 px-4 border border-gray-300 rounded-md ",
                                                                value: duration,
                                                                onChange: (e)=>handleInputChange(e, setDuration)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 543,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "absolute right-3 top-2.5 text-gray-500",
                                                                children: "Years"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 549,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 542,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 538,
                                                columnNumber: 17
                                            }, this),
                                            errors.duration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-red-500 text-xs mt-1",
                                                children: errors.duration
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 555,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "range",
                                                        min: "1",
                                                        max: "40",
                                                        step: "1",
                                                        value: duration,
                                                        onChange: (e)=>handleInputChange(e, setDuration),
                                                        className: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-[#ac272b]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 559,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-xs text-gray-500 mt-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "1 years"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 569,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "40 years"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 570,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 568,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 558,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 537,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                lineNumber: 384,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full lg:w-1/2 p-6 bg-gray-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col justify-center items-center mb-8",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-full h-full relative mb-4 flex justify-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$calculator$2f$sip$2d$calculator$2f$circular$2d$progress$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CircularProgress"], {
                                                    progress: graphProgress
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                    lineNumber: 580,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 579,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center mt-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-600 mb-1",
                                                        children: investmentMode === "sip" ? "SIP per month" : investmentMode === "quarterly" ? "Investment per quarter" : "Investment amount"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 584,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-2xl font-bold",
                                                        children: [
                                                            "₹ ",
                                                            numWithCommas(monthlyInvestment)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 591,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 583,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 578,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-4 h-4 bg-[#f47321] mr-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 599,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-gray-600 text-sm",
                                                                children: "Invested amount"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 601,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-semibold",
                                                                children: [
                                                                    "₹ ",
                                                                    numWithCommas(investedAmount)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 602,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 600,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 598,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-4 h-4 bg-[#ae2f33] mr-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 609,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-gray-600 text-sm",
                                                                children: "Est. returns"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 611,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-semibold",
                                                                children: [
                                                                    "₹ ",
                                                                    numWithCommas(returns)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                                lineNumber: 612,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                        lineNumber: 610,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                lineNumber: 608,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 597,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-8 p-4 bg-gray-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-600",
                                                    children: "Total amount:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                    lineNumber: 619,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-bold text-[#2e4765]",
                                                    children: [
                                                        "₹ ",
                                                        numWithCommas(totalWealth)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                    lineNumber: 620,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                            lineNumber: 618,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 617,
                                        columnNumber: 15
                                    }, this),
                                    errors.general && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",
                                        children: errors.general
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 627,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-6 text-center text-sm text-gray-600",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                preText,
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-semibold",
                                                    children: [
                                                        "₹ ",
                                                        numWithCommas(monthlyInvestment)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                                    lineNumber: 635,
                                                    columnNumber: 19
                                                }, this),
                                                " ",
                                                postText
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                            lineNumber: 633,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                        lineNumber: 632,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                                lineNumber: 577,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                        lineNumber: 382,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
                    lineNumber: 381,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
            lineNumber: 376,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/calculator/sip-calculator/SipCalculator.jsx",
        lineNumber: 375,
        columnNumber: 5
    }, this);
}
_s(SipCalculator, "31EDKgBveFLnNLs8LMtW6upW7Cs=");
_c = SipCalculator;
var _c;
__turbopack_context__.k.register(_c, "SipCalculator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/reusable/banner/Banner.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
"use client";
;
const Banner = ({ imageUrl, title, subtitle })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: `relative h-[40vh] md:h-[50vh] w-full flex items-center justify-center overflow-hidden -mt-[1px] text-white`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 left-0 w-full h-full -z-10 pointer-events-none pt-4 md:pt-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: imageUrl,
                    alt: title,
                    className: "w-full h-full object-cover"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                    lineNumber: 10,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-[#000]/40 z-0"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 text-center px-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl md:text-5xl font-medium",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this),
                    subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mt-4",
                        children: subtitle
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                        lineNumber: 24,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
                lineNumber: 21,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/reusable/banner/Banner.jsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
};
_c = Banner;
const __TURBOPACK__default__export__ = Banner;
var _c;
__turbopack_context__.k.register(_c, "Banner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_96b70d72._.js.map