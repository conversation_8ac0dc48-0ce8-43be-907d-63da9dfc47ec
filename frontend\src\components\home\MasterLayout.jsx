"use client";
import ClientPrivileges from "./ClientPrivileges";
import ClientPrivileges2 from "./ClientPrivileges2";
import GoogleReviews from "./GoogleReviews";
import HomeAbout from "./HomeAbout";
import HomeCalculator from "./HomeCalculator";
import InvestToday from "./InvestToday";
import HomeTestimonial from "./testimonial/HomeTestimonial";
// import ReviewCarousel from "./ReviewCarousel";
import Testimonials from "./Testimonials/Testimonials";
import VideoBanner from "./VideoBanner";
import WhyUs from "./WhyUs";
import dynamic from "next/dynamic";

const ReviewCarousel = dynamic(() => import("./ReviewCarousel"), {
  ssr: false, // Avoid server-side rendering
});

const MasterLayout = () => {
  return (
    <div>
       <VideoBanner />
       <HomeAbout/>
       {/* <CounterDetails/> */}
       {/* <Pillars/> */}
       {/* <ClientPrivileges2/> */}
       <ClientPrivileges/>
       <WhyUs/>
       <InvestToday/>
       {/* <ReviewCarousel/> */}
       <HomeTestimonial/>
       <HomeCalculator/>
       {/* <Testimonials/> */}

       {/* <GoogleReviews/> */}
       {/* <HomeFaq/> */}
    </div>
  );
};

export default MasterLayout;
