"use client";

import SkewFadeInWords from "@/components/ui/animation/SkewFadeInWords";
import Marquee from "react-fast-marquee";

export default function FranchiseAdvantages() {
  const advantages = [
    {
      icon: "/images/business-oppotunity/zero-cost.png",
      title: "Zero Upfront Costs*",
    },
    {
      icon: "/images/business-oppotunity/EliminateMonthly.png",
      title: "Eliminate Monthly Overheads",
    },
    {
      icon: "/images/business-oppotunity/ProfitFocused.png",
      title: "Profit-Focused Approach",
    },
    {
      icon: "/images/business-oppotunity/ServiceEndCustomer.png",
      title: "Service to End Customer",
    },
    {
      icon: "/images/business-oppotunity/One-Stop.png",
      title: "One-Stop Investment Solution",
    },
    {
      icon: "/images/business-oppotunity/FreeWorkspace.png",
      title: "Free Workspace",
    },
  ];

  return (
    <div className="py-8 bg-[#F9F3F1]">
      <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium mb-2 lg:mb-4 text-black text-center">
        <SkewFadeInWords text="Key Advantages for Franchisees" />
      </h2>

      <Marquee speed={60} className="py-4 pb-8" autoFill={true}>
        {advantages.map((advantage, index) => (
          <div
            key={index}
            className="flex flex-col items-center md:mx-8 mx-2 md:w-48 w-40 aspect-square shadow-xl rounded-3xl p-1 md:p-4 bg-white"
          >
            <div className="min-w-20 min-h-20 md:min-w-28 md:min-h-24 flex items-center justify-center mb-3">
              <img
                src={advantage.icon || "/placeholder.svg"}
                alt={advantage.title}
                className="md:w-16 md:h-16 w-12 h-12 object-contain"
              />
            </div>
            <p className={`text-center text-sm md:text-base font-semibold text-[#333]`}>
              {advantage.title}
            </p>
          </div>
        ))}
      </Marquee>
      <div className="p-0 s_wrapper">
        <div className="p-6 border-2 border-dashed border-[#b33c337c] rounded-2xl bg-[#ff8f873a] md:mt-8">
          <p className="text-[#333] italic text-base  text-justify md:text-start">
            Disclaimer: While we won’t impose any charges, please note that
            there will be associated costs for obtaining essential
            qualifications and certifications as mandated by SEBI and exchanges.
          </p>
        </div>
      </div>
    </div>
  );
}
