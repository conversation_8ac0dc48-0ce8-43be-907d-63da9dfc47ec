// Sample data structure for the cash flow table
export const cashFlowData = [
  {
    id: "year1",
    year: "Year 1",
    withdrawalAmount: 1200000,
    growthAmount: 1500000,
    balanceCorpusAmount: 30300000,
    months: [
      {
        name: "January",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25025000,
      },
      {
        name: "February",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25050000,
      },
      {
        name: "March",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25075000,
      },
      {
        name: "April",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25100000,
      },
      {
        name: "May",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25125000,
      },
      {
        name: "June",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25150000,
      },
      {
        name: "July",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25175000,
      },
      {
        name: "August",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25200000,
      },
      {
        name: "September",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25225000,
      },
      {
        name: "October",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25250000,
      },
      {
        name: "November",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25275000,
      },
      {
        name: "December",
        withdrawalAmount: 100000,
        growthAmount: 125000,
        balanceCorpusAmount: 25300000,
      },
    ],
  },
  {
    id: "year2",
    year: "Year 2",
    withdrawalAmount: 1320000,
    growthAmount: 1650000,
    balanceCorpusAmount: 30630000,
    months: [
      {
        name: "January",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25327500,
      },
      {
        name: "February",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25355000,
      },
      {
        name: "March",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25382500,
      },
      {
        name: "April",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25410000,
      },
      {
        name: "May",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25437500,
      },
      {
        name: "June",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25465000,
      },
      {
        name: "July",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25492500,
      },
      {
        name: "August",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25520000,
      },
      {
        name: "September",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25547500,
      },
      {
        name: "October",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25575000,
      },
      {
        name: "November",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25602500,
      },
      {
        name: "December",
        withdrawalAmount: 110000,
        growthAmount: 137500,
        balanceCorpusAmount: 25630000,
      },
    ],
  },
  {
    id: "year3",
    year: "Year 3",
    withdrawalAmount: 1452000,
    growthAmount: 1815000,
    balanceCorpusAmount: 30993000,
    months: [
      {
        name: "January",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25660250,
      },
      {
        name: "February",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25690500,
      },
      {
        name: "March",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25720750,
      },
      {
        name: "April",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25751000,
      },
      {
        name: "May",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25781250,
      },
      {
        name: "June",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25811500,
      },
      {
        name: "July",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25841750,
      },
      {
        name: "August",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25872000,
      },
      {
        name: "September",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25902250,
      },
      {
        name: "October",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25932500,
      },
      {
        name: "November",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25962750,
      },
      {
        name: "December",
        withdrawalAmount: 121000,
        growthAmount: 151250,
        balanceCorpusAmount: 25993000,
      },
    ],
  },
  {
    id: "year4",
    year: "Year 4",
    withdrawalAmount: 1597200,
    growthAmount: 1996500,
    balanceCorpusAmount: 31392300,
    months: [
      {
        name: "January",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26026275,
      },
      {
        name: "February",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26059550,
      },
      {
        name: "March",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26092825,
      },
      {
        name: "April",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26126100,
      },
      {
        name: "May",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26159375,
      },
      {
        name: "June",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26192650,
      },
      {
        name: "July",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26225925,
      },
      {
        name: "August",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26259200,
      },
      {
        name: "September",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26292475,
      },
      {
        name: "October",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26325750,
      },
      {
        name: "November",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26359025,
      },
      {
        name: "December",
        withdrawalAmount: 133100,
        growthAmount: 166375,
        balanceCorpusAmount: 26392300,
      },
    ],
  },
  {
    id: "year5",
    year: "Year 5",
    withdrawalAmount: 1756920,
    growthAmount: 2196150,
    balanceCorpusAmount: 31831530,
    months: [
      {
        name: "January",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26428902.5,
      },
      {
        name: "February",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26465505,
      },
      {
        name: "March",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26502107.5,
      },
      {
        name: "April",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26538710,
      },
      {
        name: "May",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26575312.5,
      },
      {
        name: "June",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26611915,
      },
      {
        name: "July",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26648517.5,
      },
      {
        name: "August",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26685120,
      },
      {
        name: "September",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26721722.5,
      },
      {
        name: "October",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26758325,
      },
      {
        name: "November",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26794927.5,
      },
      {
        name: "December",
        withdrawalAmount: 146410,
        growthAmount: 183012.5,
        balanceCorpusAmount: 26831530,
      },
    ],
  },
]
