{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/calculator/sip-calculator/SipCalculatorMaster.jsx"], "sourcesContent": ["import React from 'react'\r\nimport SipCalculator from './SipCalculator'\r\nimport Banner from '@/components/ui/reusable/banner/Banner'\r\n\r\nconst SipCalculatorMaster = () => {\r\n  return (\r\n    <div>\r\n        <Banner\r\n        title=\"SIP Calculator\"\r\n        imageUrl=\"/images/calculator/calculator-banner.jpeg\"\r\n        subtitle=\"\"\r\n      />\r\n      <SipCalculator/>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default SipCalculatorMaster\r\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;;;;;AAEA,MAAM,sBAAsB;IAC1B,qBACE,8OAAC;;0BACG,8OAAC,wJAAA,CAAA,UAAM;gBACP,OAAM;gBACN,UAAS;gBACT,UAAS;;;;;;0BAEX,8OAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/app/calculator/sip-calculator/page.jsx"], "sourcesContent": ["import SipCalculatorMaster from '@/components/calculator/sip-calculator/SipCalculatorMaster'\r\nimport Head from 'next/head'\r\nimport React from 'react'\r\n\r\nconst page = () => {\r\n  return (\r\n    <>\r\n    <Head>\r\n        <title>SIP Calculator | Plan Your Mutual Fund Investments | Winshine</title>\r\n        <meta name=\"description\" content=\"Use Winshine’s SIP Calculator to estimate the future value of your mutual fund investments. Plan your wealth creation journey with accurate and easy projections.\" />\r\n        <meta name=\"keywords\" content=\"SIP calculator, mutual fund calculator, investment calculator, monthly investment planning, wealth creation, Winshine SIP tool\" />\r\n        <meta name=\"author\" content=\"Winshine Financial Services\" />\r\n        <meta property=\"og:title\" content=\"SIP Calculator | Mutual Fund Investment Planner | Winshine\" />\r\n        <meta property=\"og:description\" content=\"Estimate returns on your SIP investments with our easy-to-use calculator. Winshine helps you make smarter, ethical investment decisions.\" />\r\n        <meta property=\"og:image\" content=\"https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg\" /> \r\n        <meta property=\"og:url\" content=\"https://winshine.nipralo.com/calculator/sip-calculator\" />\r\n        <meta name=\"twitter:card\" content=\"https://winshine.nipralo.com/images/calculator/calculator-banner.jpeg\" />\r\n        <link rel=\"canonical\" href=\"https://winshine.nipralo.com/calculator/sip-calculator\" />\r\n      </Head>\r\n    <SipCalculatorMaster/>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default page\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO;IACX,qBACE;;0BACA,8OAAC,oKAAA,CAAA,UAAI;;kCACD,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,UAAS;wBAAiB,SAAQ;;;;;;kCACxC,8OAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,UAAS;wBAAS,SAAQ;;;;;;kCAChC,8OAAC;wBAAK,MAAK;wBAAe,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;;;;;;;0BAE/B,8OAAC,4KAAA,CAAA,UAAmB;;;;;;;AAGxB;uCAEe", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA;wBAEzG,YAAA;4BAAA;4BAAA,CACA,kCAD4D;4BAC5D,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kCAChDY,QAAAA,CAAAA,CAAY;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACVC,MAAMZ,UAAUa,QAAQ;;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACV,2CAA2C;sBAC3CC,IAAAA,CAAAA;YAAAA;SAAAA,CAAY;;SACZC,UAAU;cACVC,IAAAA;YAAAA,CAAU,EAAE,GAAA;gBACd,OAAA,QAAA;wBAAA;4BACAC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,CAAU,qBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACRC,OAAAA,GAAAA,6SAAAA,CAAAA,EAAYnB,QAAAA,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,MAAAA,EAAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACA;qBAAA", "ignoreList": [0], "debugId": null}}]}