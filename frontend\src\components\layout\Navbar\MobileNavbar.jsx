"use client";

import { useEffect, useState } from "react";
import { ChevronDown, ChevronRight, Menu, X } from "lucide-react";
import "./Navbar.scss";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

const MobileNavbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState({});
  const [scrolled, setScrolled] = useState(false);
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const toggleExpanded = (itemId) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  const navigationItems = [
    {
      id: "home",
      label: "Home",
      href: "/",
    },
    {
      id: "about",
      label: "About Us",
      href: "/about-us",
    },
    {
      id: "services",
      label: "Services",
      href: "/services",
    },
    {
      id: "investment",
      label: "Investment",
      children: [
        {
          id: "traditional-investment",
          label: "Traditional Investment",
          href: "/investment/traditional-investment",
        },
        {
          id: "new-age-investment",
          label: "New-Age Investment",
          href: "/investment/new-age-investment",
        },
      ],
    },
    {
      id: "insurance",
      label: "Insurance",
      href: "/insurance",
    },
    {
      id: "resources",
      label: "Resources",
      children: [
        {
          id: "Investor-Login",
          label: "Investor Login",
          children: [
            {
              id: "finzy",
              label: "Finzy",
              href: "https://finzy.com/login",
            },
            {
              id: "LenDen",
              label: "LenDen",
              href: "https://dashboard.lendenclub.com/auth/login",
            },
            {
              id: "BetterInvest",
              label: "Better Invest",
              href: "https://app.betterinvest.club/login",
            },
            {
              id: "altgraaf",
              label: "Altgraaf",
              href: "https://www.altgraaf.com/",
            },
            {
              id: "hbits",
              label: "hBits",
              href: "https://hbits.co/",
            },
          ],
        },
        {
          id: "ipo",
          label: "IPO",
          children: [
            {
              id: "Kfintech",
              label: "IPO Allotment Status - KFINTECH",
              href: "https://ris.kfintech.com/ipostatus/ipos.aspx",
            },
            {
              id: "LinkIntime",
              label: "IPO Allotment status - Link Intime",
              href: "https://in.mpms.mufg.com/Initial_Offer/public-issues.html",
            },
          ],
        },
        {
          id: "insurance",
          label: "Insurance",
          children: [
            {
              id: "BajajAllianzLifeInsurance",
              label: "Bajaj Allianz Life Insurance",
              children: [
                {
                  id: "BajajAllianzOnlinePayment",
                  label: "Online Premium Payment",
                  href: "https://www.bajajallianzlife.com/renewal-payment.html",
                },
                {
                  id: "BajajAllianzPremiumCalculator",
                  label: "Premium Calculator",
                  href: "https://carequotebi.balic.in/carequote/welcome.jsp",
                },
              ],
            },
            {
              id: "ICICIPruLife",
              label: "ICICI PruLife",
              children: [
                {
                  id: "ICICIPrulifeOnlinePremium",
                  label: "ICICI Prulife Online Premium",
                  href: "https://www.iciciprulife.com/services/pay-life-insurance-premium-online.html",
                },
                {
                  id: "ICICPrulifeLogin",
                  label: "ICICI Prulife Login",
                  href: "https://customer.iciciprulife.com/csr/cmmn-home.htm?execution=e1s1",
                },
                {
                  id: "PremiumCalculator",
                  label: "Premium Calculator",
                  href: "https://buy.iciciprulife.com/buy/EBI.htm?execution=e1s1#/LAEBI/T50",
                },
              ],
            },
            {
              id: "HDFCLife",
              label: "HDFC Life",
              children: [
                {
                  id: "HDFCLifeCustomerLogin",
                  label: "Customer Login",
                  href: "https://myaccount.hdfclife.com/login",
                },
                {
                  id: "HDFCLifeOnlinePremiumPayment",
                  label: "Online Premium Payment",
                  href: "https://onlinepayments.hdfclife.com/HLifeWeb-QP/hlife/quick_pay.jsp",
                },
              ],
            },
            {
              id: "ICICILombardGen",
              label: "ICICI Lombard Gen",
              children: [
                {
                  id: "ICICILombardGenCustomerLogin",
                  label: "Customer Login",
                  href: "https://www.icicilombard.com/",
                },
                {
                  id: "ICICILombardGenOnlinePremiumPayment",
                  label: "Online Premium Payment",
                  href: "https://www.icicilombard.com/renew-policy-online#/AllRenewal",
                },
              ],
            },
            {
              id: "HDFCErgoGenInsuranceInsurance",
              label: "HDFC Ergo Gen Insurance in Insurance",
              children: [
                {
                  id: "HDFCErgoOnlinePremiumPayment",
                  label: "Online Premium Payment",
                  href: "https://www.hdfcergo.com/renew-hdfc-ergo-policy",
                },
              ],
            },
          ],
        },
        {
          id: "PhillipCapital",
          label: "Phillip Capital",
          href: "https://phillipone.phillipcapital.in/",
        },
        {
          id: "Cams",
          label: "Cams",
          href: "https://www.camsonline.com/Investors",
        },
        {
          id: "Kfintech",
          label: "KFINTECH",
          href: "https://mfs.kfintech.com/investor",
        },
        {
          id: "JMTools",
          label: "JM Tools",
          href: "https://www.jmfinancialservices.in/customer-corner/tools-and-resources",
        },
        {
          id: "JMBlinktradeLogin",
          label: "JM Blinktrade Login",
          href: "https://blinktrade.jmfinancialservices.in/userMaster/login",
        },
        {
          id: "Chart",
          label: "Chart",
          href: "https://chartink.com/stocks/jmfinancil.html",
        },
        {
          id: "BSEIndia",
          label: "BSE India",
          href: "https://www.bseindia.com/",
        },
        {
          id: "NSEIndia",
          label: "NSE India",
          href: "https://www.nseindia.com/",
        },
      ],
    },
    {
      id: "BusinessOpportunity",
      label: "Business Opportunity",
      children: [
        {
          id: "BusinessAssociate",
          label: "For Business Associate",
          href: "/business-opportunity/business-associate",
        },
        {
          id: "employer",
          label: "For Employer",
          href: "/business-opportunity/employer",
        },
        {
          id: "PrivateClient",
          label: "For Private Client",
          href: "/business-opportunity/private-client",
        },
      ],
    },

    {
      id: "contact",
      label: "Contact",
      href: "/contact",
    },
    {
      id: "ClientLogin",
      label: "Client Login",
      href: "https://winshine.wealthmagic.in/",
    },
  ];

  const handleScroll = () => {
    setScrolled(window.scrollY > 150);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const renderNavItem = (item, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems[item.id];

    return (
      <motion.div
        key={item.id}
        className="w-full"
        variants={{
          hidden: { opacity: 0, x: 20 },
          visible: {
            opacity: 1,
            x: 0,
            transition: {
              type: "spring",
              stiffness: 300,
              damping: 25,
            },
          },
        }}
      >
        {item.href ? (
          <motion.a
            className={`flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-100 transition-colors duration-200 cursor-pointer ${
              level > 0 ? `pl-${4 + level * 4} ml-2` : ""
            }`}
            whileHover={{
              backgroundColor: "rgb(243 244 246)",
              transition: { duration: 0.2 },
            }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              if (hasChildren) {
                toggleExpanded(item.id);
              } else if (item.href) {
                setIsOpen(false);
              }
            }}
            href={item.href}
          >
            <div className="flex items-center space-x-3">
              <span
                className={`text-gray-800 ${
                  level > 0 ? "text-sm" : "text-base"
                } font-medium`}
              >
                {item.label}
              </span>
            </div>
            {hasChildren && (
              <div className="flex-shrink-0">
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                )}
              </div>
            )}
          </motion.a>
        ) : (
          <motion.div
            className={`flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-100 transition-colors duration-200 cursor-pointer ${
              level > 0 ? `pl-${4 + level * 4} ml-2` : ""
            }`}
            whileHover={{
              backgroundColor: "rgb(243 244 246)",
              transition: { duration: 0.2 },
            }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              if (hasChildren) {
                toggleExpanded(item.id);
              } else if (item.href) {
                setIsOpen(false);
              }
            }}
          >
            <div className="flex items-center space-x-3">
              <span
                className={`text-gray-800 ${
                  level > 0 ? "text-sm" : "text-base"
                } font-medium`}
              >
                {item.label}
              </span>
            </div>
            {hasChildren && (
              <div className="flex-shrink-0">
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                )}
              </div>
            )}
          </motion.div>
        )}
        <AnimatePresence>
          {hasChildren && isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: "auto",
                opacity: 1,
                transition: {
                  height: { duration: 0.3, ease: "easeInOut" },
                  opacity: { duration: 0.2, delay: 0.1 },
                },
              }}
              exit={{
                height: 0,
                opacity: 0,
                transition: {
                  height: { duration: 0.3, ease: "easeInOut" },
                  opacity: { duration: 0.1 },
                },
              }}
              className="bg-gray-200 overflow-hidden"
            >
              <motion.div
                initial="hidden"
                animate="visible"
                exit="hidden"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.03,
                      delayChildren: 0.1,
                    },
                  },
                }}
              >
                {item.children.map((child, childIndex) =>
                  renderNavItem(child, level + 1, childIndex)
                )}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <div
      className={`navbar-wrapper  lg:hidden border-b  ${
        scrolled ? "scrolled-up" : ""
      }`}
    >
      <div className="relative">
        {/* Header */}
        <header className="border-b border-gray-200">
          <nav
            className={`navbar flex items-center justify-between px-4 py-3 ${
              scrolled ? "!py-4" : "!py-7"
            }`}
          >
            <div className="w-[97%] flex justify-between items-center max-w-full">
              <div className={`max-w-fit ${scrolled ? "" : ""}`}>
                <Link href="/" className="transition-all !max-w-fit">
                  <Image
                    src="/images/logo/cropped-winshine_logo-1.png"
                    height={120}
                    width={270}
                    alt="Winshine Logo"
                    className=""
                  />
                </Link>
              </div>

              {/* Mobile menu button */}
              <motion.button
                onClick={toggleMenu}
                className="p-2 rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200"
                aria-label="Toggle menu"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={{ rotate: isOpen ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {isOpen ? (
                    <X className="w-6 h-6 relative z-[99]" />
                  ) : (
                    <Menu className="w-6 h-6" />
                  )}
                </motion.div>
              </motion.button>
            </div>
          </nav>
        </header>

        {/* Mobile Navigation Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed z-50"
            >
              {/* Menu Panel */}
              <motion.div
                initial={{ x: "100%" }}
                animate={{ x: 0 }}
                exit={{ x: "100%" }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                  duration: 0.3,
                }}
                className={`${
                  scrolled
                    ? "top-[85px] h-[calc(100dvh-90px)]"
                    : "top-[110px] h-[calc(100dvh-120px)]"
                } fixed left-[50%] translate-x-[-50%] rounded-2xl w-[calc(100vw-10px)] bg-white shadow-2xl flex flex-col`}
              >
                {/* Menu Items - Scrollable Area */}
                <nav className="flex-1 overflow-y-auto min-h-0">
                  <motion.div
                    className="py-2"
                    initial="hidden"
                    animate="visible"
                    variants={{
                      hidden: { opacity: 0 },
                      visible: {
                        opacity: 1,
                        transition: {
                          staggerChildren: 0.05,
                          delayChildren: 0.1,
                        },
                      },
                    }}
                  >
                    {navigationItems.map((item, index) =>
                      renderNavItem(item, 0, index)
                    )}
                  </motion.div>
                </nav>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default MobileNavbar;
