"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { Toaster, toast } from "react-hot-toast";
import { FaInstagram } from "react-icons/fa";
import { FaWhatsapp } from "react-icons/fa";
import { IoMdCall } from "react-icons/io";
import { FaLinkedinIn, FaFacebookF } from "react-icons/fa";
import { IoMailOpenOutline } from "react-icons/io5";

const Footer = () => {
  const [formData, setFormData] = useState({ name: "", email: "" });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.id]: e.target.value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const newErrors = {};
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (
      !/^[a-zA-Z][a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(
        formData.email
      )
    ) {
      newErrors.email = "Enter a valid email";
    }
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/newsletters`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            data: {
              email: formData.email,
            },
          }),
        }
      );

      const result = await response.json();
      if (response.ok) {
        toast.success("Subscribed successfully!");
        setFormData({ email: "" }); // Reset form
        setIsLoading(false);
      } else {
        toast.error(
          `Failed to subscribe: ${result.error?.message || "Unknown error"}`
        );
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setIsLoading(false);
      toast.error("An error occurred. Please try again later.");
    }
  };

  return (
    <footer className="bg-[#101435] font-sans ">
      <Toaster position="top-right" />
      <div className="flex flex-col items-center gap-4 fixed right-8 bottom-8 z-[10]">
        <Link href="https://wa.me/+919833135459" target="_blank">
          <div className="bg-[#101435ee] p-2.5 rounded-full group transition-all duration-500 shadow shadow-gray-300">
            <FaWhatsapp
              size={20}
              className="text-white group-hover:scale-105 transition-all duration-500"
            />
          </div>
        </Link>
        <Link href="tel:+919833135459">
          <div className="bg-[#101435ee] p-2.5 rounded-full group transition-all duration-500 shadow shadow-gray-300">
            <IoMdCall
              size={20}
              className="text-white group-hover:scale-105 transition-all duration-500"
            />
          </div>
        </Link>
      </div>
      <div className="s_wrapper px-6 mx-auto !pb-5 ">
        <div className="grid grid-cols-2 gap-6 sm:grid-cols-2 sm:gap-y-10 lg:grid-cols-5">
          <div className="col-span-2 md:w-[80%] flex flex-col justify-between gap-12">
            <Image
              src="/images/logo/winshine_logo-white.png"
              height={200}
              width={340}
              alt="winshine_logo"
            />
            <div>
              <h2 className="text-lg md:text-2xl font-medium text-[#ffffff]">
                Subscribe Monthly Newsletter
              </h2>

              <form
                className="flex w-fit mt-6 space-y-0 sm:ml-0 gap-2 sm:justify-center"
                onSubmit={handleSubmit}
              >
                <div className="flex-1">
                  <input
                    id="email"
                    type="text"
                    value={formData.email}
                    onChange={handleChange}
                    className="px-4 py-2 text-gray-700 bg-white border rounded-xl focus:border-blue-400  focus:outline-none focus:ring focus:ring-opacity-40 focus:ring-blue-300 w-full"
                    placeholder="Email Address"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm">{errors.email}</p>
                  )}
                </div>

                <button
                  className="gradient-button-rd px-4 font-semibold py-2 max-w-[100px] rounded-xl max-h-fit cursor-pointer"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Submitting" : "Subscribe"}
                </button>
              </form>
            </div>
          </div>

          <div>
            <p className="font-semibold text-[#ffffff] ">Quick Link</p>

            <div className="flex flex-col items-start mt-3 space-y-2">
              <Link
                href="/"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Home
              </Link>
              <Link
                href="/about-us"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                About Us
              </Link>
              <Link
                href="/services"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Services
              </Link>
              <Link
                href="/insurance"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Insurance
              </Link>
              <Link
                href="/investment/traditional-investment"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Traditional Investments
              </Link>
              <Link
                href="/investment/new-age-investment"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                New-Age Investment
              </Link>
              <Link
                href="/blogs"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Blogs
              </Link>
            </div>
          </div>

          <div>
            <p className="font-semibold text-[#ffffff] ">Calculator</p>

            <div className="flex flex-col items-start mt-3 space-y-2">
              <Link
                href="/calculator/sip-calculator"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                SIP
              </Link>
              <Link
                href="/calculator/retirement-calculator"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Retirement
              </Link>
              <Link
                href="/calculator/emi-calculator"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                EMI
              </Link>
            </div>

            <p className="font-semibold text-[#ffffff] mt-2">
              Business Opportunity
            </p>

            <div className="flex flex-col items-start mt-3 space-y-2">
              <Link
                href="/business-opportunity/business-associate"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                For Business Associate
              </Link>
              <Link
                href="/business-opportunity/employer"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                For Employer
              </Link>
              <Link
                href="/business-opportunity/private-client"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                For Private Client
              </Link>
            </div>
          </div>

          <div>
            <p className="font-semibold text-[#ffffff] ">Company</p>

            <div className="flex flex-col items-start mt-3 space-y-2">
              <Link
                href="/contact"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Contact Us
              </Link>
              <Link
                href="/terms-and-conditions"
                className="text-[#ffffff] transition-colors duration-300  hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Terms and Conditions
              </Link>
              <Link
                href="/privacy-policy"
                className="text-[#ffffff] transition-colors duration-300 hover:cursor-pointer hover:text-[#f8f8f86e]"
              >
                Privacy Policy
              </Link>
              {/* Social Links */}
              <div className="flex items-center gap-4 mt-2">
                <Link
                  href="mailto:<EMAIL>"
                  className="border group hover:border-[#b12f35] p-1.5 rounded-full transition-all duration-500"
                >
                  <IoMailOpenOutline
                    size={16}
                    className="group-hover:text-[#b12f35] transition-all duration-500"
                  />
                </Link>

                <Link
                  href="https://www.instagram.com/winshine_financial_services"
                  target="_blank"
                >
                  <div className="group border hover:border-[#b12f35] p-1.5 rounded-full transition-all duration-500">
                    <FaInstagram
                      size={16}
                      className="group-hover:text-[#b12f35] transition-all duration-500"
                    />
                  </div>
                </Link>
                <Link
                  href="https://www.linkedin.com/company/winshine-financial-services/"
                  target="_blank"
                >
                  <div className="group border hover:border-[#b12f35] p-1.5 rounded-full transition-all duration-500">
                    <FaLinkedinIn
                      size={16}
                      className="group-hover:text-[#b12f35] transition-all duration-500"
                    />
                  </div>
                </Link>
                <Link
                  href="https://www.facebook.com/share/1Aqgy7HYny/"
                  target="_blank"
                >
                  <div className="group border hover:border-[#b12f35] p-1.5 rounded-full transition-all duration-500">
                    <FaFacebookF
                      size={16}
                      className="group-hover:text-[#b12f35] transition-all duration-500"
                    />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <hr className="my-6 border-[#ffffff]/20 md:mt-12  h-[2px]" />

        <p className="font-sans text-start md:text-center text-[#ffffff]">
          © {new Date().getFullYear()}
          <Link href="/" target="_blank" className="hover:text-[#f8f8f86e]">
            {" "}
            Winshine Financial Services.{" "}
          </Link>
          All Rights Reserved. Designed and Developed by{" "}
          <Link
            href="https://nipralo.com"
            target="_blank"
            className="hover:text-[#f8f8f86e]"
          >
            Nipralo Technologies
          </Link>
        </p>
      </div>
    </footer>
  );
};

export default Footer;
