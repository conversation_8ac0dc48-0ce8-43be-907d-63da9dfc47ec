import Banner from "@/components/ui/reusable/banner/Banner";
import React from "react";
import PrivateClientDescription from "./PrivateClientDescription";
import PrivateClientPoints from "./PrivateClientPoints";

const PrivateClientMaster = () => {

  return (
    <div>
      <Banner
        title="Business Opportunities - As A Private Client"
        imageUrl="/images/business-oppotunity/private-client-bannerr.jpg"
        subtitle=""
      />
      
      <PrivateClientDescription />
      <PrivateClientPoints />
      <section className="bg-white">
        <div className="s_wrapper">
          <div className="mx-auto gap-4 flex flex-col text-[#333] text-center max-w-3xl">
            <p className="text-[#333]  text-justify md:text-start">
              Join us, where your investment journey isn't just a menu it's a
              lifetime service, making you care-free, burden-free, and even a
              bit lazy.
            </p>
            <p className="text-[#333]  text-justify md:text-start">
              We're your one-stop investment solution, offering the same
              opportunities as any other advisor, but with added services that
              make you relaxed and carefree at no additional cost.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PrivateClientMaster;
