import React from "react";
import "./VideoBanner.css";
const VideoBanner = () => {
  return (
    <div className=" pt-0 relative overflow-hidden min-w-full h-auto -z-9 bg-white">
      <div className="video-wrapper">
        {/* desktop */}
        <video
          loop
          muted
          autoPlay
          playsInline
          controls={false}
          preload="none"
          className="hidden md:block"
        >
          <source src="/video/winshine-banner2.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        {/* mobile view */}
        <video
          loop
          muted
          autoPlay
          playsInline
          controls={false}
          preload="none"
          className="md:hidden"
        >
          <source
            src="/video/winshine-banner2.mp4"
            type="video/mp4"
            className="md:hidden"
          />
          Your browser does not support the video tag.
        </video>
      </div>
    </div>
  );
};

export default VideoBanner;
