{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/reusable/banner/Banner.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/reusable/banner/Banner.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/reusable/banner/Banner.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/animation/SkewFadeInWords.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/animation/SkewFadeInWords.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/animation/SkewFadeInWords.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/ui/animation/SkewFadeInWords.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/animation/SkewFadeInWords.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/animation/SkewFadeInWords.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/Details.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport SkewFadeInWords from \"../ui/animation/SkewFadeInWords\";\r\nimport { IoLocationOutline } from \"react-icons/io5\";\r\nimport { LuPhone } from \"react-icons/lu\";\r\nimport { MdOutlineMail } from \"react-icons/md\";\r\nimport Link from \"next/link\";\r\nconst Details = () => {\r\n  return (\r\n    <div className=\"text-[#040404] md:mt-5\">\r\n      <h2 className=\"text-2xl md:text-4xl lg:text-5xl font-medium mb-4 lg:mb-4\">\r\n        <SkewFadeInWords text=\"Get in Touch\" />\r\n      </h2>\r\n      <div className=\"flex flex-col gap-2\">\r\n        <p className=\"mb-2 text-[20px] flex gap-5 xl:w-[70%]\">\r\n          <span>\r\n            <IoLocationOutline className=\"bg-gradient text-white text-[36px] p-2 rounded-lg \" />\r\n          </span>{\" \"}\r\n          <span className=\"\">\r\n            {\" \"}\r\n            <a\r\n              href=\"https://maps.app.goo.gl/2opqtYSoibK1dQZV7\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n            >\r\n              107, Manratna Business Park, Junction of Tilak Road, Derasar Ln,\r\n              Ghatkopar East, Mumbai, Maharashtra - 400077, India\r\n            </a>\r\n          </span>\r\n        </p>\r\n        <p className=\"mb-2 text-[20px] flex gap-5 items-center\">\r\n          <span>\r\n            <LuPhone className=\"bg-gradient text-white text-[36px] p-2 rounded-lg \" />\r\n          </span>{\" \"}\r\n          <div>\r\n            <Link href=\"tel:+************ \">\r\n              <span className=\"mt-1\">+91 9833135459 </span>\r\n            </Link>{\" \"}\r\n            <br />\r\n            <Link href=\"tel:022 35939918 \">\r\n              <span className=\"mt-1\">022 35939918 </span>\r\n            </Link>\r\n          </div>\r\n        </p>\r\n        <p className=\"text-[20px] flex gap-5\">\r\n          <span>\r\n            <MdOutlineMail className=\"bg-gradient text-white text-[36px] p-2 rounded-lg \" />\r\n          </span>\r\n          <Link href=\"mailto:<EMAIL>\">\r\n            <span className=\"mt-1\"><EMAIL></span>\r\n          </Link>\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Details;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BACZ,cAAA,8OAAC,uJAAA,CAAA,UAAe;oBAAC,MAAK;;;;;;;;;;;0BAExB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;0CACC,cAAA,8OAAC,+IAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;4BACvB;0CACR,8OAAC;gCAAK,WAAU;;oCACb;kDACD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;kDACL;;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;0CACC,cAAA,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;4BACb;0CACR,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;oCACjB;kDACR,8OAAC;;;;;kDACD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;kCAI7B,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;0CACC,cAAA,8OAAC,8IAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC;oCAAK,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC;uCAEe", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/Form.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/contact/Form.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact/Form.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/Form.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/contact/Form.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact/Form.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/DetailAndForm.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Details from \"./Details\";\r\nimport Form from \"./Form\";\r\n\r\nconst DetailAndForm = () => {\r\n  return (\r\n    <div className=\"bg-white\">\r\n      <div className=\"s_wrapper flex flex-col md:flex-row justify-between gap-8\">\r\n        <Details />\r\n        <Form />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailAndForm;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wIAAA,CAAA,UAAO;;;;;8BACR,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/ContactMap.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst ContactMap = () => {\r\n  return (\r\n    <div className=\"bg-white\">\r\n      <div className=\"s_wrapper flex flex-col md:flex-row justify-between gap-8 !pt-0\">\r\n        <iframe\r\n          src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3770.5854707456497!2d72.90399337425194!3d19.08195485178744!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3be7c62e9d7d6357%3A0xd943e730b640cdd3!2sWinshine%20Financial%20Services!5e0!3m2!1sen!2sin!4v1747386673976!5m2!1sen!2sin\"\r\n          width=\"600\"\r\n          height=\"360\"\r\n          style={{border: \"0px\"}}\r\n          allowFullScreen=\"\"\r\n          loading=\"lazy\"\r\n          referrerPolicy=\"no-referrer-when-downgrade\"\r\n          className=\"w-full rounded-4xl\"\r\n        ></iframe>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactMap;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,aAAa;IACjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,KAAI;gBACJ,OAAM;gBACN,QAAO;gBACP,OAAO;oBAAC,QAAQ;gBAAK;gBACrB,iBAAgB;gBAChB,SAAQ;gBACR,gBAAe;gBACf,WAAU;;;;;;;;;;;;;;;;AAKpB;uCAEe", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/components/contact/ContactMaster.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Banner from \"../ui/reusable/banner/Banner\";\r\nimport DetailAndForm from \"./DetailAndForm\";\r\nimport ContactMap from \"./ContactMap\";\r\n\r\nconst ContactMaster = () => {\r\n  return (\r\n    <>\r\n      <Banner\r\n        title=\"Contact Us\"\r\n     imageUrl=\"/images/contact/contact-banner.webp\"\r\n        subtitle=\"Have questions? We’re here to help - reach out!\"\r\n      />\r\n      <DetailAndForm />\r\n      <ContactMap/>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ContactMaster;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB;IACpB,qBACE;;0BACE,8OAAC,wJAAA,CAAA,UAAM;gBACL,OAAM;gBACT,UAAS;gBACN,UAAS;;;;;;0BAEX,8OAAC,8IAAA,CAAA,UAAa;;;;;0BACd,8OAAC,2IAAA,CAAA,UAAU;;;;;;;AAGjB;uCAEe", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/nipralo%20projects/winshine_website/frontend/src/app/contact/page.jsx"], "sourcesContent": ["import ContactMaster from \"@/components/contact/ContactMaster\";\r\nimport Head from \"next/head\";\r\nimport React from \"react\";\r\n\r\nconst ContactPage = () => {\r\n  return (\r\n    <>\r\n      <Head>\r\n        <title>\r\n          Contact Winshine | Get in Touch with Our Financial Experts\r\n        </title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Reach out to Winshine Financial Services for expert guidance on investments, insurance, savings, and tax planning. We're here to help you achieve financial wellness.\"\r\n        />\r\n        <meta\r\n          name=\"keywords\"\r\n          content=\"contact Winshine, financial advisor contact, get in touch, Winshine Financial Services, investment help, insurance support, tax planning contact\"\r\n        />\r\n        <meta name=\"author\" content=\"Winshine Financial Services\" />\r\n        <meta\r\n          property=\"og:title\"\r\n          content=\"Contact Us | Winshine Financial Services\"\r\n        />\r\n        <meta\r\n          property=\"og:description\"\r\n          content=\"Have questions or need assistance? Contact Winshine's expert financial team and start your journey to secure, ethical wealth creation.\"\r\n        />\r\n        <meta\r\n          property=\"og:image\"\r\n          content=\"https://winshine.nipralo.com/images/contact/contact-banner.webp\"\r\n        />{\" \"}\r\n        {/* Replace with actual image */}\r\n        <meta\r\n          property=\"og:url\"\r\n          content=\"https://winshine.nipralo.com/contact\"\r\n        />\r\n        <meta name=\"twitter:card\" content=\"https://winshine.nipralo.com/images/contact/contact-banner.webp\" />\r\n        <link rel=\"canonical\" href=\"https://winshine.nipralo.com/contact\" />\r\n      </Head>\r\n      <ContactMaster />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ContactPage;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,cAAc;IAClB,qBACE;;0BACE,8OAAC,oKAAA,CAAA,UAAI;;kCACH,8OAAC;kCAAM;;;;;;kCAGP,8OAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAEV,8OAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAEV,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBACC,UAAS;wBACT,SAAQ;;;;;;kCAEV,8OAAC;wBACC,UAAS;wBACT,SAAQ;;;;;;kCAEV,8OAAC;wBACC,UAAS;wBACT,SAAQ;;;;;;oBACP;kCAEH,8OAAC;wBACC,UAAS;wBACT,SAAQ;;;;;;kCAEV,8OAAC;wBAAK,MAAK;wBAAe,SAAQ;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;;;;;;;0BAE7B,8OAAC,8IAAA,CAAA,UAAa;;;;;;;AAGpB;uCAEe", "debugId": null}}]}