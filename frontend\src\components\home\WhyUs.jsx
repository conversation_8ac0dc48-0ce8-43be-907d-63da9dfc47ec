"use client";
import React from "react";
import Marquee from "react-fast-marquee";
import Image from "next/image";
import SkewFadeInWords from "../ui/animation/SkewFadeInWords";
import Link from "next/link";

const WhyUs = () => {
  const items = [
    {
      icon: "/images/home/<USER>",
      label: "One Stop Investment Solutions",
    },
    {
      icon: "/images/home/<USER>",
      label: "Quick Query Resolution",
    },
    {
      icon: "/images/home/<USER>",
      label: "Ethics & Professionalism",
    },
    {
      icon: "/images/home/<USER>",
      label: "Transparency",
    },
    {
      icon: "/images/home/<USER>",
      label: "Value Added Services",
    },
    {
      icon: "/images/home/<USER>",
      label: "Complete Control",
    },
  ];
  return (
    <div className="bg-white">
      <div className="s_wrapper overflow-x-hidden relative">
        <div className="absolute -top-4 right-4">
          <img src="/images/dot-pattern.svg" alt="." className="opacity-10" />
        </div>
        <div className="text-center mb-12">
          <h2 className="mb-4 text-2xl md:text-4xl lg:text-5xl font-medium lg:mb-4 text-[#040404]">
            <SkewFadeInWords text="Why Us?" />
          </h2>
          <p className="text-gray-700 max-w-2xl mx-auto mb-6">
            We’re bringing clarity and control back to your wealth
          </p>
        </div>
        <div className="flex flex-col lg:flex-row lg:items-start xl:items-center md:gap-6">
          <div className="w-full lg:w-1/2 rounded-2xl lg:rounded-tr-[100px] rounded-tr-[50px] lg:mt-10 xl:mt-0 overflow-hidden">
            <img src="/images/home/<USER>" alt="Why Us" />
          </div>
          <div className="w-full lg:w-1/2">
            <div className="grid xl:grid-cols-3 xl:gap-0 sm:grid-cols-3 lg:grid-cols-2 grid-cols-2">
              {items.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-center text-center md:mx-4 lg:min-w-[200px] xl:min-w-[180px] xl:aspect-square p-2 md:p-0"
                >
                  <div className="flex flex-col items-center justify-start w-full h-full">
                    <div className="bg-[#cfd0d750] aspect-square lg:w-[80px] rounded-full flex justify-center items-center mb-4 md:mb-6 mt-8">
                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={60}
                        height={60}
                        className="max-w-[60%]"
                      />
                    </div>
                    <p className="text-[#000] text-sm md:text-base">
                      {item.label}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="w-full">
              <Link href="/about-us">
                <button className="gradient-button text-[#ffffff] px-4 py-2 rounded-xl !mx-auto !mt-4 font-semibold">
                  Know More
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyUs;
