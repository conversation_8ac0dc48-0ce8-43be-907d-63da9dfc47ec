import EmployerMaster from '@/components/Business-Oppotunities/employer/EmployerMaster'
import Head from 'next/head'
import React from 'react'

const page = () => {
  return (
    <>
    <Head>
        <title>Winshine Employer Program | Empower Your Workforce with Financial Wellness</title>
        <meta name="description" content="Partner with Winshine through our Employer Program to offer your employees ethical financial solutions, including savings, investments, insurance, and tax planning." />
        <meta name="keywords" content="employer financial program, employee financial wellness, Winshine employer partnership, financial benefits for employees, workplace investment plans, tax planning for employees" />
        <meta name="author" content="Winshine Financial Services" />
        <meta property="og:title" content="Employer Partnership Program | Winshine Financial Services" />
        <meta property="og:description" content="Help your employees build ethical wealth with Winshine. Partner with us to deliver financial planning and investment services at the workplace." />
        <meta property="og:image" content="https://winshine.nipralo.com/images/business-oppotunity/employee-banner.jpg" />
        <meta property="og:url" content="https://winshine.nipralo.com/business-opportunity/employer" />
        <meta name="twitter:card" content="https://winshine.nipralo.com/images/business-oppotunity/employee-banner.jpg" />
        <link rel="canonical" href="https://winshine.nipralo.com/business-opportunity/employer" />
      </Head>
    <EmployerMaster/>
    </>
  )
}

export default page
