import React from "react";
import Banner from "../ui/reusable/banner/Banner";
import ServiceCard from "./ServiceCard/ServiceCard";
import TradingFingertips from "./TradingFingertips";
import ValueAddedServices from "./ValueAddedServices";
import ServiceTagline from "./ServiceTagline";

const ServiceMaster = () => {
  return (
    <div>
      <Banner
        title="Exclusive Client-Only Services"
        imageUrl="/images/services/banner-service.jpg"
        subtitle=""
      />
      <ServiceCard
        title={"Winshine App Integration"}
        description="Streamline your financial portfolio effortlessly with Winshine's integrated app, offering a centralised hub for all investment and insurance reports. Receive timely reminders for SIP/Insurance premium payments, access all investment documents, and securely store personal documents for enhanced convenience."
        image={
         "/images/services/service2.webp"
        }
        imageRightSide={true}
      />

      <ServiceCard
        title={"1 to 1 with Experts"}
        description="Experience personalised guidance from our seasoned experts, each backed by academic excellence, boasting a collective industry experience of 40+ years. Tailored solutions for your unique investment needs are just a conversation away."
        image={"/images/services/1to1with.webp"}
        imageRightSide={false}
      />
      <TradingFingertips />

      <ServiceCard
        title={"Real-Time Trading Calls"}
        description="Stay ahead of market trends with timely insights and recommendations, maximising your trading potential."
        image={
          "/images/services/service3.jpg"
        }
        imageRightSide={false}
      />
      <ServiceCard
        title={"Margin Funding Facility"}
        description="Unlock financial leverage with our margin funding trading facility through our associate, JM Financial Services Ltd. We also provide loans against shares, offering you additional flexibility in your investment strategy."
        image={
          "/images/services/service4.webp"
        }
        imageRightSide={true}
      />
      <ServiceCard
        title={"Financial Literacy Program"}
        description="Empowering you with knowledge to make informed financial decisions, through engaging and insightful literacy programs."
        image={
          "/images/services/service5.jpg"
        }
        imageRightSide={false}
      />
      <ValueAddedServices/>
      <ServiceCard
        title={"Complete Investment Universe"}
        description="Get access to a comprehensive array of investment options, covering all corners of the financial market to meet your diverse needs."
        image={
          "/images/services/service7.jpg"
        }
        imageRightSide={false}
      />
      <ServiceTagline/>
    </div>
  );
};

export default ServiceMaster;
