import PrivateClientMaster from "@/components/Business-Oppotunities/PrivateClient/PrivateClientMaster";
import Head from "next/head";
import React from "react";

const page = () => {
  return (
    <>
      <Head>
        <title>
          Winshine Private Client Services | Personalized Wealth Management
        </title>
        <meta
          name="description"
          content="Discover Winshine’s Private Client services – tailored wealth management, tax planning, and ethical investment strategies for high-net-worth individuals and families."
        />
        <meta
          name="keywords"
          content="private client services, wealth management, high-net-worth individuals, personalized financial planning, Winshine private clients, tax planning, ethical investing"
        />
        <meta name="author" content="Winshine Financial Services" />
        <meta
          property="og:title"
          content="Private Client Services | Winshine Financial Services"
        />
        <meta
          property="og:description"
          content="Exclusive financial services for discerning clients. Partner with Winshine for personalized, ethical wealth creation and legacy planning."
        />
        <meta
          property="og:image"
          content="https://winshine.nipralo.com/images/business-oppotunity/private-client-bannerr.jpg"
        />{" "}
        {/* Replace with the actual image URL */}
        <meta
          property="og:url"
          content="https://winshine.nipralo.com/business-opportunity/private-client"
        />
        <meta
          name="twitter:card"
          content="https://winshine.nipralo.com/images/business-oppotunity/private-client-bannerr.jpg"
        />
        <link
          rel="canonical"
          href="https://winshine.nipralo.com/business-opportunity/private-client"
        />
      </Head>
      <PrivateClientMaster />
    </>
  );
};

export default page;
