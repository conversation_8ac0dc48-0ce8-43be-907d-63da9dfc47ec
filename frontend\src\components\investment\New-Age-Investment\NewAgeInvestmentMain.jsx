"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import "./NewAgeInvestmentMain.css";
import { motion, AnimatePresence } from "framer-motion";

const data = [
  {
    title: "Securitised Debt",
    description: {
      p1: "A short-term financing arrangement where a company pledges its bills receivable as collateral to obtain a loan. Investors contribute to a fund that fulfills the company’s cash requirements, earning interest in return. If the borrowing company defaults, the intermediary gains ownership rights and collects receivables to repay investors.",
      list: [
        "Returns Range: Depending on the specific SDI, the returns can range from 10% to 14% or even higher.",
        "Risk: SDIs are generally considered moderate risk because they are backed by receivables. However, credit risk and market conditions can impact returns.",
        "Liquidity: SDIs are less liquid compared to publicly traded securities. Investors should consider their liquidity needs.",
        "Investment Horizon: 30 to 120 days.",
      ],
    },
    image: "/images/investment/investment12.jpg",
  },
  {
    title: "P2P Lending",
    description: {
      p1: "Investors contribute funds that are distributed to individuals seeking loans. Borrowers can get a loan up to 10 lakhs but only Rs.500 from one lender. Investors earn returns of 10% to 15% over 10 to 15 months. The invested amount is held in an escrow account to mitigate risk. P2P platforms must adhere to RBI regulations and have to be RBI registered.",
      list: [
        "Returns Range: P2P lending platforms offer 10% to 15% returns to lenders. However, these returns can vary based on the borrower’s creditworthiness and platform.",
        "Risk: P2P lending involves credit risk, as borrowers may default. Diversification across multiple loans can mitigate this risk.",
        "Liquidity: P2P lending generally comes with a lock-in and hence, are illiquid.",
        "Investment Horizons: 6 months to 3 years.",
      ],
    },
    image: "/images/investment/investment13.jpg",
  },
  {
    title: "Fractional Property Investing",
    description: {
      p1: "Investors contribute a minimum of 25 lakhs to a company specialising in commercial properties. They become proportional owners and receive incremental rental income (7.5-8.5% annually). When the company identifies capital appreciation opportunities, it sells the property, returning invested capital along with compounded capital gains (6-7% annually). Investors can participate in high-value commercial properties without the burden of full ownership.",
      p2: "Investors exit upon property sale, facilitated by annual valuations and an in-house legal team. ",
      list: [
        "Returns Range: Fractional ownership in real estate offers returns ranging from 13% to 15%.",
        "Risk: While fractional ownership diversifies risk, real estate markets can be volatile. Due diligence is crucial.",
        "Liquidity: Fractional ownership provides liquidity through secondary markets.",
        "Investment Horizons: Varies based on the specific property and investment strategy. Typically, 4 to 6 years.",
      ],
    },
    image: "/images/investment/investment14.jpg",
  },
  {
    title: "Funding Movies and Web-series",
    description: {
      p1: "A financing solution where producers secure loans using their OTT rights and other rights as collateral. Investors contribute to the fund, earning 18% interest. In case of default, investors hold the first lien on income from various movie rights until the loan is fully repaid.",
      list: [
        "Returns Range: The returns stand at an impressive annualised 18%.",
        "Risk: Risk of loss is low to medium but the risk of fluctuations in tenure is medium to high.",
        "Liquidity: While investors can earn interest, the liquidity of their investment is limited. Investors’ funds are locked-in throughout the period of funding.",
        "Investment Horizons: 1-4 months, 4-7 months, 9-14 months.",
      ],
    },
    image: "/images/investment/investment15.jpg",
  },
  {
    title: "Invoice Discounting",
    description: {
      p1: "",
      list: [
        "A short-term investment where businesses raise working capital by selling unpaid invoices.",
        "Investors fund these invoices and earn fixed returns once the buyer pays.",
        "Risk is mitigated using tools like trade credit insurance, bank guarantees, and escrow controls.",
        "Investment durations typically range from 30 to 180 days.",
        "Returns range between 10% to 14% annually, depending on the product.",
        "Suitable for: investors seeking short-term, fixed-income options with moderate risk.",
      ],
    },
    image: "/images/investment/investment108.jpeg",
  },
  {
    title: "Media Rights Financing",
    description: {
      p1: "",
      list: [
        "Investors fund short-term loans secured against OTT, satellite, and music rights of movies and series.",
        "Capital is repaid with interest from guaranteed revenue streams once rights are sold.",
        "Interest rates are attractive—typically 16–18% annually—due to time-sensitive nature of funding.",
        "Strong legal structuring ensures first lien on receivables, reducing downside risk.",
        "Investment tenures range from 1 to 14 months based on the project stage.",
        "Suitable for investors seeking high-yield, short-duration opportunities with structured safeguards.",
      ],
    },
    image: "/images/investment/investment109.jpeg",
  },
  {
    title: "Pre-IPO Equity Investing",
    description: {
      p1: "",
      list: [
        "Invest in shares of well-known companies before they go public and list on stock exchanges.",
        "Valuations can be attractive compared to listed peers, offering scope for long-term capital gains.",
        "Liquidity is lower than listed stocks, but strategic exits occur via IPOs or private placements.",
        "Typical holding period ranges from 2 to 5 years, depending on company plans.",
        "Suitable for investors with higher risk appetite looking for early access to tomorrow’s blue chips.",
      ],
    },
    image: "/images/investment/investment110.jpeg",
  },
];

export default function NewAgeInvestmentMain() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [blogs, setBlogs] = useState([]);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/blogs?populate=*`,
          {
            headers: {
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setBlogs(data?.data); // Strapi response format
      } catch (err) {
        console.error(err.message);
      }
    };

    fetchBlogs();
  }, []);

  const matchedBlog = blogs.find(
    (blog) => blog?.investment_type === data[activeIndex]?.title // match type
  );

  return (
    <section className="bg-[#f9f3f1]">
      <div className="s_wrapper">
        <div className="flex flex-col lg:flex-row">
          {/* Sidebar */}
          <aside className="w-full lg:w-[25%] xl:w-[20%] bg-white rounded-2xl shadow-md p-4 mb-4 lg:mb-0 hidden lg:block">
            <ul className="space-y-1 lg:space-y-2 flex flex-wrap lg:flex-col">
              {data.map((item, index) => (
                <li
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`cursor-pointer p-2 rounded-md border border-gray-400 lg:border-none transition-colors text-sm lg:text-base mr-1 mb-1 lg:mr-0 lg:mb-2 ${
                    index === activeIndex
                      ? "bg-[#101435] text-white"
                      : "hover:bg-blue-100 text-gray-700"
                  }`}
                >
                  {`${index + 1} - `}
                  {item.title}
                </li>
              ))}
            </ul>
          </aside>
          <div
            className="bg-white rounded-2xl shadow-md p-4 cursor-pointer border border-gray-300 lg:hidden"
            onClick={() => setDropdownOpen(!dropdownOpen)}
          >
            <div className="text-sm font-medium text-gray-700 flex justify-between items-center">
              {`${activeIndex + 1} - ${data[activeIndex]?.title}`}
              <svg
                className={`w-4 h-4 transition-transform ${
                  dropdownOpen ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <AnimatePresence>
            {dropdownOpen && (
              <motion.ul
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.25, ease: "easeOut" }}
                className="mt-2 bg-white rounded-xl shadow-md border border-gray-300 overflow-hidden"
              >
                {data.map((item, index) => (
                  <li
                    key={index}
                    onClick={() => {
                      setActiveIndex(index);
                      setDropdownOpen(false);
                    }}
                    className={`cursor-pointer px-4 py-2 text-sm transition-colors ${
                      index === activeIndex
                        ? "bg-[#101435] text-white"
                        : "hover:bg-blue-100 text-gray-700"
                    }`}
                  >
                    {`${index + 1} - ${item.title}`}
                  </li>
                ))}
              </motion.ul>
            )}
          </AnimatePresence>
          {/* Content Area */}
          <main className="flex-1">
            <div className="bg-transparent flex-col lg:flex-row p-4 lg:p-6 lg:pl-10 my-4 rounded-2xl lg:rounded-l-none lg:rounded-r-2xl flex gap-4 xl:gap-8">
              <div className="w-full lg:w-[50%] xl:w-[40%]">
                <Image
                  src={data[activeIndex].image || "/placeholder.svg"}
                  alt={data[activeIndex].title}
                  width={400}
                  height={400}
                  className="max-w-full md:max-w-[400px] w-full h-auto mx-auto lg:mx-0 rounded shadow-sm"
                />
                {matchedBlog?.slug && (
                  <p className="text-gray-500 font-light text-xs lg:text-base mt-3 lg:mt-4 hidden lg:block">
                    Explore in-depth insights into this investment opportunity
                    by clicking{" "}
                    <span className="text-blue-600 hover:text-blue-800 hover:underline">
                      <Link href={`/blogs/${matchedBlog?.slug || ""}`}>
                        here
                      </Link>
                    </span>
                  </p>
                )}

                {data[activeIndex].description.disclaimer && (
                  <p className="text-gray-500 font-light italic text-xs lg:text-base mt-3 lg:mt-4 hidden lg:block">
                    {data[activeIndex].description.disclaimer}
                  </p>
                )}
              </div>
              <div className="w-full lg:w-[50%] xl:w-[60%]">
                <h2 className="text-xl lg:text-2xl font-semibold mb-3 lg:mb-4 text-black mt-3 lg:mt-4 capitalize">
                  {data[activeIndex].title}
                </h2>
                <div>
                  {data[activeIndex].description.p1 && (
                    <p className="text-gray-700">
                      {data[activeIndex].description.p1}
                    </p>
                  )}
                  {data[activeIndex].description.p2 && (
                    <p className="text-gray-700">
                      {data[activeIndex].description.p2}
                    </p>
                  )}
                </div>
                {data[activeIndex].description.list && (
                  <ul className="text-gray-700 list-disc pl-5 lg:pl-6 mt-2 text-sm lg:text-base">
                    {data[activeIndex].description.list.map((li, index) => (
                      <li key={index}>{li}</li>
                    ))}
                  </ul>
                )}
                {matchedBlog?.slug && (
                  <p className="text-gray-500 font-light text-xs mt-3 lg:mt-4 lg:hidden">
                    Explore in-depth insights into this investment opportunity
                    by clicking{" "}
                    <span className="text-blue-600 hover:text-blue-800 hover:underline">
                      <Link href={`/blogs/${matchedBlog?.slug || ""}`}>
                        here
                      </Link>
                    </span>
                  </p>
                )}
                {data[activeIndex].description.disclaimer && (
                  <p className="text-gray-500 font-light italic text-xs mt-3 lg:mt-4 lg:hidden">
                    {data[activeIndex].description.disclaimer}
                  </p>
                )}
              </div>
            </div>
          </main>
          {/* <main className="flex-1">
            <div className="bg-[#e7380315] p-4 lg:p-6 my-4 rounded-2xl lg:rounded-l-none lg:rounded-r-2xl">
              <div className="clearfix">
                <Image
                  src={data[activeIndex].image || "/placeholder.svg"}
                  alt={data[activeIndex].title}
                  width={300}
                  height={300}
                  className="w-full h-[280px] max-h-[280px] lg:max-w-[350px] object-cover rounded-lg shadow-sm clearfix-img lg:mr-8 mb-2"
                />
                <div className="">
                  <h2 className="text-xl lg:text-2xl font-semibold mb-2 lg:mb-3 text-black capitalize">
                    {data[activeIndex].title}
                  </h2>
                  <div className="space-y-2 text-gray-700 text-sm lg:text-base  text-justify lg:text-start">
                    {data[activeIndex].description.p1 && (
                      <p>{data[activeIndex].description.p1}</p>
                    )}
                    {data[activeIndex].description.p2 && (
                      <p>{data[activeIndex].description.p2}</p>
                    )}
                  </div>
                  {data[activeIndex].description.list && (
                    <ul className="list-disc pl-5 lg:pl-6 mt-2 text-gray-700 space-y-1 text-sm lg:text-base">
                      {data[activeIndex].description.list.map((li, index) => (
                        <li key={index}>{li}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </main> */}
        </div>
      </div>
    </section>
  );
}
