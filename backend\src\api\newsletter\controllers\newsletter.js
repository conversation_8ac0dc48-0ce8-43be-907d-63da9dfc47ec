// 'use strict';

// /**
//  * newsletter controller
//  */

// const { createCoreController } = require('@strapi/strapi').factories;

// module.exports = createCoreController('api::newsletter.newsletter');


"use strict";

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::newsletter.newsletter",
  ({ strapi }) => ({
    async create(ctx) {
      try {
        // ✅ Extract data properly from request
        const { data } = ctx.request.body;

        if (!data) {
          return ctx.badRequest("Missing 'data' payload in the request.");
        }

        const { email } = data;

        // ✅ Validate required fields
        if (!email) {
          return ctx.badRequest("Missing required fields");
        }

        // ✅ Save the data in Strapi
        const enquiry = await strapi.entityService.create(
          "api::newsletter.newsletter",
          {
            data: {
              email,
            },
          }
        );

        // ✅ Send Email to Admin
        // ✅ Send Email to Admin
        await strapi.plugins["email"].services.email.send({
          to: ["<EMAIL>"], // Admin email
          from: '"Winshine Financial Services" <<EMAIL>>',
          subject: `New Newsletter Subscription on Winshine Financial Services`,
          text: `Hello Admin,
  
A new user has subscribed to the Winshine Financial Services newsletter.
  
  Subscriber Email Id.: ${email}

 Please make sure this email is added to your mailing list.
  
  Best regards,  
  Team Winshine Financial Services
  `,
          html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">New Newsletter Subscription on Winshine Financial Services</h2>
        <p style="color: #555;">A new user has subscribed to the Winshine Financial Services newsletter:</p>
        <table style="width: 100%; border-collapse: collapse;">
          <tr><td style="padding: 8px; font-weight: bold;">Email:</td><td style="padding: 8px;">${email}</td></tr>
        </table>
        <br>
        <p style="color: #555;">Please make sure this email is added to your mailing list.</p>
        <p style="color: #777;">Best regards,</p>
        <p style="font-weight: bold; color: #333;">Team Winshine Financial Services</p>
      </div>
    `,
        });

        // ✅ Send Confirmation Email to the User
        await strapi.plugins["email"].services.email.send({
          to: email, // User's email
          from: '"Winshine Financial Services" <<EMAIL>>',
          subject: "Thank You for Subscribing to Winshine Financial Services Newsletter!",
          text: `Hi ${email},
          
          Thank you for subscribing to the Winshine Financial Services newsletter!
        You're now part of a community that stays updated on the latest news, recognitions, and exclusive insights related to Winshine Financial Services.

          We'll be sending you periodic updates directly to this email.

          If your matter is urgent, feel free to call us directly at +91 98331 35459 or 022 35939918.
          
          Thank you once again for contacting us. We look forward to assisting you!
          
          Bestregards,
          Team Winshine Financial Services
          107, Manratna Business Park, Junction of Tilak Road, Derasar Ln, Ghatkopar East, Mumbai, Maharashtra - 400077, India
          https://www.winshine.in
          `,
          html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <p style="color: #555;">Hi ${email},</p>
                <p>Thank you for subscribing to the <strong>Winshine Financial Services</strong> newsletter!.
                <br> 
                <br> 
                You're now part of a community that stays updated on the latest news, recognitions, and exclusive insights related to Winshine Financial Services.<br><br>
                We'll be sending you periodic updates directly to this email.</p>
                <p>Our team is reviewing your message and will respond to you within 24 to 48 business hours.
                <br>
                <p style="font-weight: bold; color: #333;">Best regards,</p>
                <p style="font-weight: bold; color: #444;">Team Winshine Financial Services</p>
                <hr style="border: none; border-top: 1px solid #eee;">
                <p style="font-size: 14px; color: #999;">
                  107, Manratna Business Park, Junction of Tilak Road, Derasar Ln, Ghatkopar East, Mumbai, Maharashtra - 400077, India<br>
                  <a href="https://www.winshine.in" target="_blank" style="color: #007bff;">www.winshine.in</a>
                </p>
              </div>
            `,
        });

        return ctx.send({ message: "Subscription submitted successfully!" });
      } catch (error) {
        console.error("❌ Error:", error);
        return ctx.internalServerError("Failed to process subscription.");
      }
    },
  })
);