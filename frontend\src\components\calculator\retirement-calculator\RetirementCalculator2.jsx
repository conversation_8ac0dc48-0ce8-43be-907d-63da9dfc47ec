"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, Legend } from "recharts";

export default function RetirementCalculator2() {
  // State for form inputs - all independent of each other
  const [currentAge, setCurrentAge] = useState(30);
  const [retirementAge, setRetirementAge] = useState(60);
  const [lifeExpectancy, setLifeExpectancy] = useState(80);
  const [currentExpenses, setCurrentExpenses] = useState(50000);
  const [inflationRate, setInflationRate] = useState(5);
  const [returnRate, setReturnRate] = useState(12);
  const [currentInvestments, setCurrentInvestments] = useState(100000);
  const [retirementCorpusReturnRate, setRetirementCorpusReturnRate] =
    useState(6);

  // Calculate results whenever inputs change
  useEffect(() => {
    calculateRetirement();
  }, [
    currentAge,
    retirementAge,
    lifeExpectancy,
    currentExpenses,
    inflationRate,
    returnRate,
    currentInvestments,
    retirementCorpusReturnRate,
  ]);

  // State for calculation results
  const [results, setResults] = useState({
    futureExpenses: 114601,
    futureValueOfInvestments: 686604,
    corpusNeeded: 25034468,
    yearsToInvest: 17,
    lumpsumNeeded: 3546129,
    sipNeeded: 36453,
    amountInvested: 100000,
    totalGrowth: 586604,
  });

  const calculateRetirement = () => {
    // Years until retirement - handle edge cases where retirement age might be <= current age
    const yearsToInvest = Math.max(1, retirementAge - currentAge);

    // Future monthly expenses adjusted for inflation
    const futureExpenses =
      currentExpenses * Math.pow(1 + inflationRate / 100, yearsToInvest);

    // Future value of current investments
    const monthlyReturnRate = returnRate / 100 / 12;
    const months = yearsToInvest * 12;
    const futureValueOfInvestments =
      currentInvestments * Math.pow(1 + monthlyReturnRate, months);

    // Calculate corpus needed for retirement
    // Handle edge case where life expectancy might be <= retirement age
    const yearsInRetirement = Math.max(1, lifeExpectancy - retirementAge);
    const annualExpenses = futureExpenses * 12;

    // Avoid division by zero
    const effectiveReturnRate =
      retirementCorpusReturnRate === 0 ? 0.1 : retirementCorpusReturnRate;
    const corpusNeeded =
      (annualExpenses *
        (1 - Math.pow(1 + effectiveReturnRate / 100, -yearsInRetirement))) /
      (effectiveReturnRate / 100);

    // Additional amount needed
    const additionalAmountNeeded = Math.max(
      0,
      corpusNeeded - futureValueOfInvestments
    );

    // Lumpsum needed (if investing all at once)
    const lumpsumNeeded =
      additionalAmountNeeded / Math.pow(1 + returnRate / 100, yearsToInvest);

    // SIP needed (monthly investment)
    const monthlyRate = returnRate / 100 / 12;
    const sipNeeded =
      additionalAmountNeeded /
      (((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
        (1 + monthlyRate));

    // For pie chart
    const amountInvested = currentInvestments;
    const totalGrowth = futureValueOfInvestments - currentInvestments;

    setResults({
      futureExpenses,
      futureValueOfInvestments,
      corpusNeeded,
      yearsToInvest,
      lumpsumNeeded,
      sipNeeded,
      amountInvested,
      totalGrowth,
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return "₹ " + amount.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Pie chart data
  const pieData = [
    { name: "Amount Invested", value: results.amountInvested },
    { name: "Total Growth", value: results.totalGrowth },
  ];

  const COLORS = ["#b91c1c", "#1e1e5a"];

  return (
    <div className="bg-[#F9F3F1]">
      <div className="s_wrapper ">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Left column - Inputs */}
            <div className="bg-white p-6 rounded-lg shadow-md lg:w-[60%]">
              <div className="space-y-6">
                {/* Current Age - fixed range */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      What is your current age?
                    </label>
                    <input
                      type="text"
                      value={currentAge}
                      onChange={(e) => setCurrentAge(Number(e.target.value))}
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black "
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${((currentAge - 18) / (100 - 18)) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={18}
                      max={100}
                      step={1}
                      value={currentAge}
                      onChange={(e) => setCurrentAge(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((currentAge - 18) / (100 - 18)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Retirement Age - fixed range */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      At what age you want to retire?
                    </label>
                    <input
                      type="text"
                      value={retirementAge}
                      onChange={(e) => setRetirementAge(Number(e.target.value))}
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${
                              ((retirementAge - 18) / (100 - 18)) * 100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={18}
                      max={100}
                      step={1}
                      value={retirementAge}
                      onChange={(e) => setRetirementAge(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((retirementAge - 18) / (100 - 18)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Life Expectancy - fixed range */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      Expected life expectancy?
                    </label>
                    <input
                      type="text"
                      value={lifeExpectancy}
                      onChange={(e) =>
                        setLifeExpectancy(Number(e.target.value))
                      }
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${
                              ((lifeExpectancy - 18) / (120 - 18)) * 100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={18}
                      max={120}
                      step={1}
                      value={lifeExpectancy}
                      onChange={(e) =>
                        setLifeExpectancy(Number(e.target.value))
                      }
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((lifeExpectancy - 18) / (120 - 18)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Current Expenses */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      Your current monthly household expenses?
                    </label>
                    <input
                      type="text"
                      value={currentExpenses}
                      onChange={(e) =>
                        setCurrentExpenses(Number(e.target.value))
                      }
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${
                              ((currentExpenses - 10000) / (500000 - 10000)) *
                              100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={10000}
                      max={500000}
                      step={1000}
                      value={currentExpenses}
                      onChange={(e) =>
                        setCurrentExpenses(Number(e.target.value))
                      }
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((currentExpenses - 10000) / (500000 - 10000)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Inflation Rate */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      Expected inflation rate over the years (% per annum)
                    </label>
                    <input
                      type="text"
                      value={inflationRate}
                      onChange={(e) => setInflationRate(Number(e.target.value))}
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${((inflationRate - 1) / (15 - 1)) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={1}
                      max={15}
                      step={0.1}
                      value={inflationRate}
                      onChange={(e) => setInflationRate(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((inflationRate - 1) / (15 - 1)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Return Rate */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      The expected rate of return on your investments (% per
                      annum)
                    </label>
                    <input
                      type="text"
                      value={returnRate}
                      onChange={(e) => setReturnRate(Number(e.target.value))}
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${((returnRate - 1) / (20 - 1)) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={1}
                      max={20}
                      step={0.1}
                      value={returnRate}
                      onChange={(e) => setReturnRate(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((returnRate - 1) / (20 - 1)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Current Investments */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      How much investments you have now (₹)
                    </label>
                    <input
                      type="text"
                      value={currentInvestments}
                      onChange={(e) =>
                        setCurrentInvestments(Number(e.target.value))
                      }
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${(currentInvestments / 10000000) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={0}
                      max={10000000}
                      step={10000}
                      value={currentInvestments}
                      onChange={(e) =>
                        setCurrentInvestments(Number(e.target.value))
                      }
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          (currentInvestments / 10000000) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>

                {/* Retirement Corpus Return Rate */}
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-gray-700 mb-2">
                      What return you expect on your retirement corpus (% per
                      annum)
                    </label>
                    <input
                      type="text"
                      value={retirementCorpusReturnRate}
                      onChange={(e) =>
                        setRetirementCorpusReturnRate(Number(e.target.value))
                      }
                      className="w-22 px-2 py-1 border border-gray-300 rounded text-right text-black"
                    />
                  </div>
                  <div className="relative h-10 w-full mt-4">
                    <div className="absolute inset-0 flex items-center">
                      <div className="h-2 w-full bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-red-700 rounded-full"
                          style={{
                            width: `${
                              ((retirementCorpusReturnRate - 1) / (15 - 1)) *
                              100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <input
                      type="range"
                      min={1}
                      max={15}
                      step={0.1}
                      value={retirementCorpusReturnRate}
                      onChange={(e) =>
                        setRetirementCorpusReturnRate(Number(e.target.value))
                      }
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute h-5 w-5 rounded-full border-2 border-red-700 bg-white transform -translate-y-1/2 top-1/2 pointer-events-none"
                      style={{
                        left: `calc(${
                          ((retirementCorpusReturnRate - 1) / (15 - 1)) * 100
                        }% - 10px)`,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Results */}
            <div className="bg-white p-6 rounded-lg shadow-md lg:w-[40%]">
              <div className="mb-6">
                <h3 className="text-center mb-4 text-white p-1 px-2 rounded-lg shadow-xl bg-[#a91e22] ">Break-up of Total Payment</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Legend
                        layout="horizontal"
                        verticalAlign="bottom"
                        align="center"
                        formatter={(value) => {
                          return <span className="text-sm">{value}</span>;
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="space-y-4">
                
                <div className="text-center">
                  <p className="text-gray-600">
                    Your current monthly household expenses
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(currentExpenses)}
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Your future monthly household expenses
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(results.futureExpenses)}
                  </p>
                  <p className="text-sm text-gray-500">
                    (Your monthly expenses will increase annually by{" "}
                    {inflationRate}%)
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Your current investment amount
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(currentInvestments)}
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Future value of your current investments
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(results.futureValueOfInvestments)}
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Future corpus amount needed to meet expenses
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(results.corpusNeeded)} -{" "}
                    {formatCurrency(results.futureValueOfInvestments)} (Future
                    value of your current investments) ={" "}
                    {formatCurrency(
                      results.corpusNeeded - results.futureValueOfInvestments
                    )}
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Number of years you need to invest
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {results.yearsToInvest} Years
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">
                    Lumpsum amount you need to invest
                  </p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(results.lumpsumNeeded)}
                  </p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600">SIP amount you need to invest</p>
                  <p className="font-semibold text-red-700 text-sm">
                    {formatCurrency(results.sipNeeded)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
