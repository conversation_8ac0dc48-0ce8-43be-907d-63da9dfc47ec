.CardStack_main_legacy_services {
  width: 100%;
  height: calc(210% - 60px);
  scroll-behavior: smooth;

  @media screen and (max-width: 768px) {
    .CardStack_main_div {
      height: calc(350% - 75px);
    }

    .card__div {
      width: 100vw;
    }
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  .card__div {
    width: 100vw;
    height: calc(70vh - 60px);
    position: sticky;
    top: 75px;
    border-radius: 20px;
  }

  .card__div img {
    // box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  }

  .one {
    width: 100%;
    z-index: 1;
    height: auto;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  }

  .two {
    width: 100%;
    z-index: 1;
    //   background-color: cadetblue;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    height: auto;
  }

  .three {
    //   background-color: darkgray;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    width: 100%;
    z-index: 1;
    height: auto;
  }

  .four {
    //   background-color: hotpink;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    width: 100%;
    z-index: 1;
    height: 500px;
  }

  .five {
    //   background-color: darkcyan;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    width: 100%;
    z-index: 1;
    height: 500px;
  }

  .six {
    //   background-color: aquamarine;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    width: 100%;
    z-index: 1;
    height: 500px;
  }

  .seven {
    //   background-color: aquamarine;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    width: 100%;
    z-index: 1;
    height: 10vh;
    padding-bottom: 40vh;
  }
}